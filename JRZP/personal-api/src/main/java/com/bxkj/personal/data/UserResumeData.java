package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 用户简历
 * @TODO: TODO
 * @date 2018/5/8
 */

public class UserResumeData implements Parcelable {

  /**
   * id : 18425 name : 软件工程师 edate1 : 2017/2/15 14:32:54 detailsName : null count : 281 state : 0
   * sortId : 0 jobType1 : 0 jobType2 : 0 isApply : 0 isdefault : 0
   */

  private int id;
  private String name;
  private String edate1;
  private Object detailsName;
  private int count;
  private int state;
  private int sortId;
  private int jobType1;
  private int jobType2;
  private int isApply;
  private int isdefault;
  private int top;
  private int refreshState;

  protected UserResumeData(Parcel in) {
    id = in.readInt();
    name = in.readString();
    edate1 = in.readString();
    count = in.readInt();
    state = in.readInt();
    sortId = in.readInt();
    jobType1 = in.readInt();
    jobType2 = in.readInt();
    isApply = in.readInt();
    isdefault = in.readInt();
    top = in.readInt();
    refreshState = in.readInt();
  }

  public static final Creator<UserResumeData> CREATOR = new Creator<UserResumeData>() {
    @Override
    public UserResumeData createFromParcel(Parcel in) {
      return new UserResumeData(in);
    }

    @Override
    public UserResumeData[] newArray(int size) {
      return new UserResumeData[size];
    }
  };

  public int getTop() {
    return top;
  }

  public void setTop(int top) {
    this.top = top;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getEdate1() {
    return edate1;
  }

  public void setEdate1(String edate1) {
    this.edate1 = edate1;
  }

  public Object getDetailsName() {
    return detailsName;
  }

  public void setDetailsName(Object detailsName) {
    this.detailsName = detailsName;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getSortId() {
    return sortId;
  }

  public void setSortId(int sortId) {
    this.sortId = sortId;
  }

  public int getJobType1() {
    return jobType1;
  }

  public void setJobType1(int jobType1) {
    this.jobType1 = jobType1;
  }

  public int getJobType2() {
    return jobType2;
  }

  public void setJobType2(int jobType2) {
    this.jobType2 = jobType2;
  }

  public int getIsApply() {
    return isApply;
  }

  public void setIsApply(int isApply) {
    this.isApply = isApply;
  }

  public int getIsdefault() {
    return isdefault;
  }

  public void setIsdefault(int isdefault) {
    this.isdefault = isdefault;
  }

  public int getRefreshState() {
    return refreshState;
  }

  public void setRefreshState(int refreshState) {
    this.refreshState = refreshState;
  }

  public String getStateText() {
    switch (state) {
      case 0:
      case 1:
        return "所有企业可看";
      case 2:
        return "仅投递企业可看";
      default:
        return "";
    }
//    switch (state) {
//      case 0:
//        return "公开";
//      case 1:
//        return "部分公开";
//      case 2:
//        return "保密";
//      default:
//        return "";
//    }
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(id);
    dest.writeString(name);
    dest.writeString(edate1);
    dest.writeInt(count);
    dest.writeInt(state);
    dest.writeInt(sortId);
    dest.writeInt(jobType1);
    dest.writeInt(jobType2);
    dest.writeInt(isApply);
    dest.writeInt(isdefault);
    dest.writeInt(top);
    dest.writeInt(refreshState);
  }
}
