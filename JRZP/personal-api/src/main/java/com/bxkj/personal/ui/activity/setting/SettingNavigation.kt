package com.bxkj.personal.ui.activity.setting

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class SettingNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/setting"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}