package com.bxkj.personal.ui.activity.addanswer

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/22
 * @version: V1.0
 */
class AddAnswerNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/"

    const val EXTRA_REPLY_USER_ID = "REPLY_USER_ID"
    const val EXTRA_QUESTION_ID = "QUESTION_ID"
    const val EXTRA_QUESTION_TITLE = "QUESTION_TITLE"
    const val EXTRA_NEED_TO_QUESTION_PAGE = "NEED_TO_QUESTION_PAGE"

    fun navigate(
      replyUserId: Int,
      questionId: Int,
      questionTitle: String,
      needToQuestionPage: Boolean
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_REPLY_USER_ID, replyUserId)
        .withInt(EXTRA_QUESTION_ID, questionId)
        .withString(EXTRA_QUESTION_TITLE, questionTitle)
        .withBoolean(EXTRA_NEED_TO_QUESTION_PAGE, needToQuestionPage)
    }
  }
}