package com.bxkj.personal.ui.activity.aboutus

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

class AboutUsNavigation {
    companion object {

        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/aboutus"

        fun navigate(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}