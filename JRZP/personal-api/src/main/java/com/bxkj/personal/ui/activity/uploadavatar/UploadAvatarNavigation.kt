package com.bxkj.personal.ui.activity.uploadavatar

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/19
 * @version: V1.0
 */
class UploadAvatarNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/uploadavatar"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}