package com.bxkj.personal.ui.activity.postnotice

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

class PostNoticeNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/postnotice"

    const val EXTRA_POST_TYPE = "POST_TYPE"
    const val EXTRA_INFO_ID = "INFO_ID"

    const val EXTRA_TO_USER_HOME = "TO_USER_HOME"

    const val NORMAL_NOTICE = 0
    const val SCHOOL_NOTICE = 1

    fun create(
      postType: Int,
      infoID: Int = CommonApiConstants.NO_ID,
      toUserHome: Boolean = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_POST_TYPE, postType)
        .withInt(EXTRA_INFO_ID, infoID)
        .withBoolean(EXTRA_TO_USER_HOME, toUserHome)
    }
  }

}