package com.bxkj.jrzp.user.videorelate.data

import androidx.recyclerview.widget.DiffUtil

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
data class VideoRelateResumeData(
  var yiGuanlianList: List<Resume>,
  var weiGuanlianList: List<Resume>
) {

  data class Resume(
    var id: Int,
    var name: String? = "",
    var edate1: String? = "",
    var count: Int
  ) {
    class DiffCallback : DiffUtil.ItemCallback<Resume>() {
      override fun areItemsTheSame(oldItem: Resume, newItem: Resume): Bo<PERSON>an {
        return oldItem == newItem && oldItem.id == newItem.id
      }

      override fun areContentsTheSame(oldItem: Resume, newItem: Resume): Boolean {
        return oldItem.name == newItem.name && oldItem.edate1 == newItem.edate1 && oldItem.count == newItem.count
      }
    }

    override fun equals(other: Any?): Boolean {
      if (other == null || other !is Resume) {
        return false
      }
      return other.id == id && other.name == name && other.edate1 == edate1 && other.count == count
    }
  }
}