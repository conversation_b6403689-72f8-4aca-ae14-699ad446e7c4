package com.bxkj.jrzp.user.videorelate.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.jrzp.user.videorelate.data.VideoRelateJobData
import com.bxkj.jrzp.user.videorelate.data.VideoRelateResumeData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
interface VideoRelateApi {

  @POST("/Resume/GetShipinResumeList/")
  suspend fun getVideoRelateResume(@Body encryptReqParams: EncryptReqParams): BaseResponse<VideoRelateResumeData>

  @POST("/ReleaseJob/GetVideoGlJobListByPage/")
  suspend fun getVideoRelateJobs(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<VideoRelateJobData>>
}