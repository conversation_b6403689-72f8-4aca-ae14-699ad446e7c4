<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.videomanagement.VideoManagementViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:layout_gravity="center_vertical"
      android:orientation="horizontal">

      <ImageView
        android:id="@+id/iv_back"
        style="@style/wrap_wrap"
        android:layout_gravity="center_vertical"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_ic_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        style="@style/Text.18sp.333333"
        android:layout_gravity="center"
        android:text="@string/user_video_management_page_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_edit"
        style="@style/Text.15sp"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="@dimen/dp_14"
        android:enabled="@{viewModel.editEnableStatus[viewModel.currentPage]}"
        android:onClick="@{onClickListener}"
        android:text="@{viewModel.openEdit?@string/common_cancel:@string/common_edit}"
        android:textColor="@{viewModel.editEnableStatus[viewModel.currentPage]?@color/cl_333333:@color/common_b5b5b5}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View style="@style/Line.Horizontal" />

    <!--    <net.lucode.hackware.magicindicator.MagicIndicator-->
    <!--      android:id="@+id/indicator_video_type"-->
    <!--      android:layout_width="match_parent"-->
    <!--      android:layout_height="@dimen/common_dp_42" />-->

    <!--    <View style="@style/Line.Horizontal" />-->

    <androidx.viewpager.widget.ViewPager
      android:id="@+id/vp_video"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

  </LinearLayout>
</layout>