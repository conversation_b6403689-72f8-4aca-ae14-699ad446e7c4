package com.bxkj.jrzp.user.institutions.ui.institutionsinfo

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.imageloader.UCropEngine
import com.bxkj.common.widget.adresspickerdialog.AddressPickerDialogFragment
import com.bxkj.common.widget.dialog.selection.SelectionDialog
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityInstitutionsInfoBinding
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route

/**
 *  事业单位资料
 * @author: YangXin
 * @date: 2021/1/18
 */
@Route(path = InstitutionsInfoNavigation.PATH)
class InstitutionsInfoActivity :
  BaseDBActivity<UserActivityInstitutionsInfoBinding, InstitutionsInfoViewModel>(),
  OnClickListener {

  companion object {
    const val TO_EDIT_DETAILS_ADDRESS_CODE = 1
  }

  override fun getViewModelClass(): Class<InstitutionsInfoViewModel> =
    InstitutionsInfoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_institutions_info

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupNextBtnText()

    subscribeViewModelEvent()
  }

  private fun subscribeViewModelEvent() {
    viewModel.institutionsTypeOptions.observe(this, Observer {
      SelectionDialog.Builder(this)
        .setItems(it)
        .singleSelect(true)
        .setSelectedItems(
          if (viewModel.getSelectedType()
              .isNullOrEmpty()
          ) emptyArray() else arrayOf(viewModel.getSelectedType())
        )
        .setOnConfirmListener { dialog, selectedItems ->
          dialog.dismiss()
          if (selectedItems.isNotEmpty()) {
            viewModel.setSelectedType(selectedItems[0])
          }
        }.build().show()
    })

    viewModel.updateInstitutionsInfoSuccessEvent.observe(this, Observer {
      if (getIntentNextStep() == InstitutionsInfoNavigation.NEXT_BACK) {
        setResult(Activity.RESULT_OK)
        finish()
      } else {
        AuthenticationNavigation.navigate(
          it.name,
          intent.getIntExtra(
            BusinessBasicInfoNavigation.EXTRA_AUTH_TYPE,
            AuthenticationType.INSTITUTIONS
          )
        ).start()
      }
    })

    viewModel.showAreaSelectPickerCommand.observe(this, Observer {
      AddressPickerDialogFragment()
        .apply {
          setSelected(it.provinceName, it.cityName, it.countyName)
          setEndLevel(AddressPickerDialogFragment.AREA_LEVEL)
          setOnSelectedListener { province, city, district, _ ->
            viewModel.setupProvinceAndCity(province, city, district)
          }
        }.show(supportFragmentManager)
    })

    viewModel.toEditDetailsAddressCommand.observe(this, Observer {
      EditInfoNavigation.navigate("详细地址", "请输入详细地址", it).startForResult(
        this,
        TO_EDIT_DETAILS_ADDRESS_CODE
      )
    })
  }

  private fun setupNextBtnText() {
    if (getIntentNextStep() == InstitutionsInfoNavigation.NEXT_BACK) {
      viewBinding.tvNextStep.text = getString(R.string.user_institutions_info_completed)
    } else {
      viewBinding.tvNextStep.text = getString(R.string.user_institutions_info_next_step)
    }
  }

  private fun getIntentNextStep(): Int {
    return intent.getIntExtra(
      InstitutionsInfoNavigation.EXTRA_NEXT_STEP,
      InstitutionsInfoNavigation.NEXT_BACK
    )
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.iv_logo) {
        PermissionUtils.requestPermission(
          this,
          getString(R.string.permission_tips_title),
          getString(R.string.permission_select_img_tips),
          object : PermissionUtils.OnRequestResultListener {
            override fun onRequestSuccess() {
              PictureSelector.create(this@InstitutionsInfoActivity)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.getInstance())
                .setCompressEngine(ImageCompressEngine.getInstance())
                .setSandboxFileEngine(SandboxFileEngine.getInstance())
                .setCropEngine(UCropEngine())
                .setSelectionMode(SelectModeConfig.SINGLE)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            }

            override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
              showToast(getString(R.string.cancel_operation))
            }
          },
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE
        )
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}