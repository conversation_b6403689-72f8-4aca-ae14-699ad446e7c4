<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_wrap"
  android:orientation="vertical">

  <LinearLayout
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_16"
    android:paddingTop="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_10">

    <ImageView
      style="@style/wrap_wrap"
      android:scaleType="fitXY"
      android:src="@drawable/user_ic_contracts_friend" />

    <TextView
      style="@style/Text.14sp.767676"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_weight="1"
      android:text="@string/user_recommend_friends_view_contracts_friend" />

    <TextView
      style="@style/Text.14sp.FE6600"
      android:background="@drawable/frame_fe6600_round"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/user_recommend_friends_view" />

  </LinearLayout>

  <TextView
    style="@style/Text.14sp.999999.Bold"
    android:layout_width="match_parent"
    android:background="@drawable/bg_f4f4f4"
    android:paddingStart="@dimen/dp_18"
    android:paddingTop="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_10"
    android:text="@string/user_recommend_friends_tips" />

</LinearLayout>