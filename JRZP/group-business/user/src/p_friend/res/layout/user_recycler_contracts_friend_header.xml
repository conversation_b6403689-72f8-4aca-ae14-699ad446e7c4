<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.friend.ui.contractsfriend.ListContractsFriendHeader" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:orientation="vertical">

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
      style="@style/match_wrap"
      android:drawableStart="@drawable/user_ic_contracts_tag"
      android:drawablePadding="@dimen/dp_12"
      android:gravity="center_vertical"
      android:padding="@dimen/dp_16"
      android:text="@{@string/user_contracts_friend_count_format(data.count)}" />

    <View style="@style/Line.Horizontal.Light" />
  </LinearLayout>

</layout>