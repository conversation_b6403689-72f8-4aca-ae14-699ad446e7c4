<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.userhome.data.UserMomentData.UserJobData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="304dp"
        android:layout_height="wrap_content">

        <include
            layout="@layout/user_include_moment_job_item"
            app:data="@{data}" />

        <View
            style="@style/Line.Vertical"
            android:layout_height="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>