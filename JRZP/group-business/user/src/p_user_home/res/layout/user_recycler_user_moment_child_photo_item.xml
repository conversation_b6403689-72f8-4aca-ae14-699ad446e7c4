<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.userhome.data.UserMomentData.UserPicData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
      android:id="@+id/iv_item"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerCrop"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      bind:loadRadiusImg="@{data.sppic}" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>