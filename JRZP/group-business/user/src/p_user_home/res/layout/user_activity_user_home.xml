<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.userhome.ui.homepage.UserHomeViewModel" />
  </data>

  <androidx.coordinatorlayout.widget.CoordinatorLayout style="@style/match_match">

    <com.google.android.material.appbar.AppBarLayout
      android:id="@+id/abl_header"
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff"
      app:elevation="@dimen/dp_0">

      <com.google.android.material.appbar.CollapsingToolbarLayout
        android:id="@+id/ctl_user_info_group"
        style="@style/match_wrap"
        app:layout_scrollFlags="scroll|exitUntilCollapsed">

        <androidx.constraintlayout.widget.ConstraintLayout
          style="@style/match_wrap"
          android:animateLayoutChanges="true">

          <ImageView
            android:id="@+id/iv_user_header_bg"
            bind:imgErrorPlaceholder="@{@drawable/user_ic_user_home_top_bg}"
            bind:imgPlaceholder="@{@drawable/user_ic_user_home_top_bg}"
            bind:imgUrl="@{viewModel.userHomeInfo.pic}"
            android:layout_width="match_parent"
            android:layout_height="@dimen/user_home_page_header_bg_height"
            android:onClick="@{()->viewModel.editHeaderBg()}"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

          <FrameLayout
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/iv_user_header_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <View
              android:layout_width="match_parent"
              android:layout_height="@dimen/dp_24"
              android:layout_gravity="bottom"
              android:background="@drawable/user_shadow_user_home_header_bottom" />

            <View
              android:layout_width="match_parent"
              android:layout_height="@dimen/dp_8"
              android:layout_gravity="bottom"
              android:background="@drawable/user_shape_user_info_top" />

          </FrameLayout>

          <View
            android:id="@+id/v_guide_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_user_header_bg" />

          <FrameLayout
            android:id="@+id/fl_avatar"
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_14"
            app:layout_constraintBottom_toBottomOf="@id/v_guide_line"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/v_guide_line">

            <ImageView
              bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
              bind:imgIsCircle="@{true}"
              bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
              bind:imgUrl="@{viewModel.userHomeInfo.photo}"
              android:layout_width="@dimen/user_home_page_avatar_size"
              android:layout_height="@dimen/user_home_page_avatar_size"
              android:background="@drawable/user_shadow_user_home_avatar"
              android:onClick="@{()->viewModel.viewBigAvatarOrUpdateCompanyLogo()}" />

            <ImageView
              bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
              bind:imgUrl="@{viewModel.userHomeInfo.memberLevelIcon}"
              android:layout_width="@dimen/dp_20"
              android:layout_height="@dimen/dp_20"
              android:layout_gravity="bottom|end"
              android:layout_marginEnd="@dimen/dp_4"
              android:layout_marginBottom="@dimen/dp_4"
              android:visibility="@{viewModel.userHomeInfo.showVipIcon?View.VISIBLE:View.GONE}" />

          </FrameLayout>

          <FrameLayout
            android:id="@+id/fl_show_recommend_friends"
            bind:selected="@{viewModel.showRecommendFriends}"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_14"
            android:background="@drawable/bg_fe6600_to_ffffff_selector"
            android:onClick="@{()->viewModel.switchShowRecommendFriendsStatus()}"
            android:visibility="@{viewModel.showRecommendFriendsDropBtn?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/fl_option"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/fl_option">

            <ImageView
              style="@style/wrap_wrap"
              android:layout_gravity="center"
              android:src="@{viewModel.showRecommendFriends?@drawable/user_ic_show_recommend_friends_coll:@drawable/user_ic_show_recommend_friends_expand}" />

          </FrameLayout>

          <LinearLayout
            android:id="@+id/fl_option"
            style="@style/wrap_wrap"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            app:layout_constraintEnd_toStartOf="@id/fl_show_recommend_friends"
            app:layout_constraintTop_toBottomOf="@id/iv_user_header_bg"
            app:layout_goneMarginEnd="@dimen/dp_14">

            <TextView
              android:id="@+id/tv_auth"
              style="@style/Text.14sp.FE6600"
              android:layout_width="@dimen/user_home_page_option_btn_width"
              android:layout_height="wrap_content"
              android:layout_marginEnd="@dimen/dp_12"
              android:background="@drawable/frame_fe6600_radius_2"
              android:gravity="center"
              android:onClick="@{()->viewModel.toAuth()}"
              android:paddingTop="@dimen/dp_6"
              android:paddingBottom="@dimen/dp_6"
              android:text="@{viewModel.authStatusText}"
              android:visibility="@{viewModel.showAuthBtn?View.VISIBLE:View.GONE}" />

            <TextView
              android:id="@+id/tv_follow"
              style="@style/common_Text.14sp.FFFFFF"
              android:layout_width="@dimen/user_home_page_option_btn_width"
              android:background="@{viewModel.userHomeInfo.isFollowed?@drawable/frame_eaeaea_radius_2:@drawable/bg_10c198_radius_2}"
              android:gravity="center"
              android:onClick="@{()->viewModel.followOrUnFollowUser()}"
              android:paddingTop="@dimen/dp_6"
              android:paddingBottom="@dimen/dp_6"
              android:text="@{viewModel.userHomeInfo.isFollowed?@string/user_home_page_remove_follow:@string/user_home_page_add_follow}"
              android:textColor="@{viewModel.userHomeInfo.isFollowed?@color/cl_999999:@color/common_white}"
              android:visibility="@{viewModel.isSelf()?View.GONE:View.VISIBLE}" />

            <TextView
              android:id="@+id/tv_add_friend"
              style="@style/Text.14sp.FFFFFF"
              android:layout_width="@dimen/user_home_page_option_btn_width"
              android:layout_height="wrap_content"
              android:background="@drawable/bg_10c198_radius_2"
              android:gravity="center"
              android:onClick="@{onClickListener}"
              android:paddingTop="@dimen/dp_6"
              android:paddingBottom="@dimen/dp_6"
              android:text="@string/user_home_page_add_friend"
              android:visibility="@{viewModel.isSelf()?View.VISIBLE:View.GONE}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintTop_toBottomOf="@id/iv_user_header_bg" />

          </LinearLayout>

          <LinearLayout
            android:id="@+id/ll_recommend_friends"
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/bg_f4f4f4"
            android:orientation="vertical"
            android:visibility="@{viewModel.showRecommendFriends?View.VISIBLE:View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fl_avatar">

            <LinearLayout
              style="@style/match_wrap"
              android:layout_marginStart="@dimen/dp_16"
              android:layout_marginTop="@dimen/dp_8"
              android:layout_marginEnd="@dimen/dp_16"
              android:layout_marginBottom="@dimen/dp_8"
              android:orientation="horizontal">

              <TextView
                style="@style/Text.12sp.333333.Bold"
                android:text="@string/user_home_page_recommend_friends" />

              <Space
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                android:layout_weight="1" />

              <TextView
                android:id="@+id/tv_view_more_friends"
                style="@style/Text.12sp.999999"
                android:drawableEnd="@drawable/common_ic_next"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center_vertical"
                android:onClick="@{onClickListener}"
                android:text="@string/user_home_page_view_more_friends" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
              android:id="@+id/recycler_recommend_friends"
              style="@style/match_wrap"
              bind:items="@{viewModel.recommendFriendList}"
              android:layout_marginBottom="@dimen/dp_16"
              android:clipToPadding="false"
              android:paddingStart="@dimen/dp_16"
              android:paddingEnd="@dimen/dp_16" />

          </LinearLayout>

          <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_name"
            style="@style/Text.22sp.333333.Bold"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:autoSizeTextType="uniform"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{viewModel.userHomeInfo.name}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/iv_gender"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_recommend_friends"
            app:layout_goneMarginEnd="@dimen/dp_16"
            app:layout_goneMarginTop="@dimen/dp_12" />

          <ImageView
            android:id="@+id/iv_gender"
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@drawable/user_ic_user_home_male"
            android:visibility="@{viewModel.isEnterpriseAuth()?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/tv_name"
            app:layout_constraintEnd_toStartOf="@id/iv_edit_user_info"
            app:layout_constraintStart_toEndOf="@id/tv_name"
            app:layout_constraintTop_toTopOf="@id/tv_name" />

          <ImageView
            android:id="@+id/iv_edit_user_info"
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_16"
            android:onClick="@{()->viewModel.toEditInfo()}"
            android:src="@drawable/user_ic_edit_personal_info"
            android:visibility="@{viewModel.isSelf()?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/iv_gender"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_gender"
            app:layout_constraintTop_toTopOf="@id/iv_gender"
            app:layout_goneMarginStart="@dimen/dp_12" />

          <TextView
            android:id="@+id/tv_phone"
            style="@style/Text.14sp.333333"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:drawableStart="@drawable/user_ic_contract_phone"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_vertical"
            android:onClick="@{()->viewModel.contractUser()}"
            android:text="@string/user_home_page_phone"
            android:visibility="@{viewModel.userHomeInfo.hasContractPhone()?View.VISIBLE:View.GONE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

          <TextView
            android:id="@+id/tv_video"
            style="@style/Text.14sp.333333"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_8"
            android:drawableStart="@drawable/user_ic_company_video"
            android:drawablePadding="@dimen/dp_4"
            android:onClick="@{()->viewModel.handleVideoClick()}"
            android:text="@string/user_home_page_video"
            android:visibility="@{viewModel.showVideo?View.VISIBLE:View.GONE}"
            app:layout_constraintStart_toEndOf="@id/tv_phone"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:layout_goneMarginStart="@dimen/dp_16" />

          <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_relate_info"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_name,tv_video,tv_phone" />

          <LinearLayout
            android:id="@+id/ll_info"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:orientation="horizontal"
            android:visibility="@{viewModel.isSelf()?View.VISIBLE:View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier_relate_info"
            app:layout_goneMarginBottom="@dimen/dp_24"
            app:layout_goneMarginTop="@dimen/dp_8">

            <LinearLayout
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_16"
              android:gravity="bottom"
              android:orientation="horizontal">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{String.valueOf(viewModel.userHomeInfo.actionCount)}"
                android:typeface="monospace" />

              <TextView
                style="@style/common_Text.15sp.999999"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@string/user_home_page_notice" />

            </LinearLayout>

            <LinearLayout
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_16"
              android:gravity="bottom"
              android:orientation="horizontal">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{String.valueOf(viewModel.userHomeInfo.likesCount)}"
                android:typeface="monospace" />

              <TextView
                style="@style/common_Text.15sp.999999"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@string/user_home_page_like" />

            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_fans"
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_16"
              android:gravity="bottom"
              android:onClick="@{()->viewModel.toUserFansPage()}"
              android:orientation="horizontal">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{String.valueOf(viewModel.userHomeInfo.formatFansCount)}"
                android:typeface="monospace" />

              <TextView
                style="@style/common_Text.15sp.999999"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@string/user_home_page_fans" />

            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_follow"
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_16"
              android:gravity="bottom"
              android:onClick="@{onClickListener}"
              android:orientation="horizontal">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{String.valueOf(viewModel.userHomeInfo.followCount)}"
                android:typeface="monospace" />

              <TextView
                style="@style/common_Text.15sp.999999"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@string/user_home_page_follow" />

            </LinearLayout>

          </LinearLayout>

          <TextView
            android:id="@+id/tv_auth_text"
            style="@style/Text.14sp.333333"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_16"
            android:text="@{@string/user_home_page_auth_format(viewModel.userHomeInfo.dwName)}"
            android:visibility="@{viewModel.userHomeInfo.authSuccess()?View.VISIBLE:View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_info"
            app:layout_goneMarginTop="@dimen/dp_12" />

          <TextView
            android:id="@+id/tv_desc_text"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_3"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:maxLines="2"
            android:onClick="@{()->viewModel.viewAllUserDesc()}"
            android:text="@{@string/user_home_page_intro_format(viewModel.userHomeInfo.introduction)}"
            android:visibility="@{viewModel.userHomeInfo.hasIntro()?View.VISIBLE:View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_auth_text"
            app:layout_goneMarginTop="@dimen/dp_12" />

          <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_live_list"
            style="@style/match_wrap"
            bind:items="@{viewModel.liveList}"
            android:layout_marginTop="@dimen/dp_10"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.liveList)?View.GONE:View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_desc_text" />

          <View
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/user_shadow_user_home_header_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recycler_live_list" />

        </androidx.constraintlayout.widget.ConstraintLayout>

      </com.google.android.material.appbar.CollapsingToolbarLayout>

      <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/indicator_user_notice_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_42"
        android:layout_gravity="center_horizontal" />

      <View style="@style/Line.Horizontal.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager.widget.ViewPager
      android:id="@+id/vp_content"
      style="@style/match_match"
      app:layout_anchor="@+id/abl_header"
      app:layout_anchorGravity="center"
      app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:background="@drawable/bg_ffffff">

      <ImageView
        android:id="@+id/iv_back"
        style="@style/wrap_wrap"
        android:minWidth="@dimen/dp_36"
        android:minHeight="@dimen/dp_36"
        android:onClick="@{onClickListener}"
        android:scaleType="center"
        android:src="@drawable/user_ic_user_home_back_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_title_bar_name"
        style="@style/Text.17sp.000000.Bold"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_70"
        android:layout_marginEnd="@dimen/dp_70"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:text="@{viewModel.userHomeInfo.name}"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_share_id_card"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_8"
        android:onClick="@{()->viewModel.showSelectShareTypeIfNeed()}"
        android:scaleType="center"
        android:src="@drawable/user_ic_user_home_share_id_card"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_share"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_share"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_8"
        android:onClick="@{onClickListener}"
        android:scaleType="center"
        android:src="@drawable/user_ic_user_home_share_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

  </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>