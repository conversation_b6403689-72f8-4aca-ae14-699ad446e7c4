<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="android.view.View" />

    <variable
      name="isSelf"
      type="Boolean" />

    <variable
      name="data"
      type="com.bxkj.jrzp.user.data.JobData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_14"
    android:paddingBottom="@dimen/dp_12">

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginBottom="@dimen/dp_8"
      android:gravity="center_vertical">

      <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <com.google.android.material.imageview.ShapeableImageView
          android:id="@+id/iv_hr_avatar"
          bind:imgUrl="@{data.hrPhoto}"
          android:layout_width="@dimen/dp_36"
          android:layout_height="@dimen/dp_36"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          app:shapeAppearance="@style/roundedCornerImageStyle" />

        <TextView
          android:id="@+id/tv_hr_info"
          style="@style/Text.12sp.333333"
          android:layout_width="@dimen/dp_0"
          android:layout_marginStart="@dimen/dp_6"
          android:layout_marginEnd="@dimen/dp_16"
          android:text="@{data.hrInfo}"
          android:visibility="@{CheckUtils.isNullOrEmpty(data.hrInfo)?View.GONE:View.VISIBLE}"
          app:layout_constraintBottom_toTopOf="@id/tv_release_time"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toEndOf="@id/iv_hr_avatar"
          app:layout_constraintTop_toTopOf="@id/iv_hr_avatar" />

        <TextView
          android:id="@+id/tv_release_time"
          style="@style/Text.12sp.999999"
          android:layout_width="@dimen/dp_0"
          android:layout_marginStart="@dimen/dp_6"
          android:layout_marginEnd="@dimen/dp_12"
          android:ellipsize="end"
          android:lines="1"
          android:text="@{data.editTime}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toEndOf="@id/iv_hr_avatar"
          app:layout_constraintTop_toBottomOf="@id/tv_hr_info" />

      </androidx.constraintlayout.widget.ConstraintLayout>

      <ImageView
        android:id="@+id/iv_more_function"
        style="@style/wrap_wrap"
        android:layout_width="@dimen/common_dp_32"
        android:layout_height="@dimen/dp_24"
        android:scaleType="center"
        android:src="@drawable/user_ic_more_function"
        android:visibility="@{isSelf?View.VISIBLE:View.GONE}" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_has_video_layout"
      style="@style/match_wrap"
      android:visibility="@{data.hasVideo()?View.VISIBLE:View.GONE}">

      <TextView
        android:id="@+id/tv_name"
        style="@style/Text.17sp.000000.Bold"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{data.name}"
        app:layout_constraintEnd_toStartOf="@id/fl_video_cover"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_salary"
        style="@style/Text.17sp.FE6600"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{data.convertSalary}"
        app:layout_constraintEnd_toStartOf="@id/fl_video_cover"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

      <TextView
        android:id="@+id/tv_type"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.jnName}"
        android:visibility="@{data.emptyNatureName()?View.GONE:View.VISIBLE}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_salary" />

      <TextView
        android:id="@+id/tv_city"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.shiName}"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.shiName)?View.GONE:View.VISIBLE}"
        app:layout_constraintEnd_toStartOf="@id/tv_edu"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/tv_type"
        app:layout_constraintTop_toBottomOf="@id/tv_salary"
        app:layout_goneMarginStart="@dimen/dp_0" />

      <TextView
        android:id="@+id/tv_edu"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.quaName}"
        app:layout_constraintEnd_toStartOf="@id/tv_exp"
        app:layout_constraintStart_toEndOf="@id/tv_city"
        app:layout_constraintTop_toBottomOf="@id/tv_salary"
        app:layout_goneMarginStart="@dimen/dp_0" />

      <TextView
        android:id="@+id/tv_exp"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.wtName}"
        app:layout_constraintEnd_toStartOf="@id/fl_video_cover"
        app:layout_constraintStart_toEndOf="@id/tv_edu"
        app:layout_constraintTop_toBottomOf="@id/tv_salary"
        app:layout_goneMarginStart="@dimen/dp_0" />

      <FrameLayout
        android:id="@+id/fl_video_cover"
        android:layout_width="90dp"
        android:layout_height="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_name">

        <ImageView
          style="@style/match_match"
          bind:loadRadiusImg="@{data.sppic}" />

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:src="@drawable/ic_play_video" />
      </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_no_video_layout"
      style="@style/match_wrap"
      android:visibility="@{data.hasVideo()?View.GONE:View.VISIBLE}">

      <TextView
        android:id="@+id/tv_name1"
        style="@style/Text.17sp.000000.Bold"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{data.name}"
        app:layout_constraintEnd_toStartOf="@id/tv_salary1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_salary1"
        style="@style/Text.17sp.FE6600"
        android:text="@{data.convertSalary}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_type1"
        style="@style/Text.12sp.888888"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.jnName}"
        android:visibility="@{data.emptyNatureName()?View.GONE:View.VISIBLE}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />

      <TextView
        android:id="@+id/tv_city1"
        style="@style/Text.12sp.888888"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.shiName}"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.shiName)?View.GONE:View.VISIBLE}"
        app:layout_constraintEnd_toStartOf="@id/tv_edu1"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/tv_type1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />

      <TextView
        android:id="@+id/tv_edu1"
        style="@style/Text.12sp.888888"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.quaName}"
        app:layout_constraintEnd_toStartOf="@id/tv_exp1"
        app:layout_constraintStart_toEndOf="@id/tv_city1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />

      <TextView
        android:id="@+id/tv_exp1"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.wtName}"
        app:layout_constraintEnd_toStartOf="@id/tv_identity1"
        app:layout_constraintStart_toEndOf="@id/tv_edu1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1"
        app:layout_goneMarginStart="@dimen/dp_0" />

      <TextView
        android:id="@+id/tv_identity1"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.identityRequire}"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.identityRequire)?View.GONE:View.VISIBLE}"
        app:layout_constraintEnd_toEndOf="@id/tv_partner1"
        app:layout_constraintStart_toEndOf="@id/tv_exp1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />

      <TextView
        android:id="@+id/tv_partner1"
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.partnerNature}"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.partnerNature)?View.GONE:View.VISIBLE}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_identity1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />


    </androidx.constraintlayout.widget.ConstraintLayout>

  </LinearLayout>
</layout>
