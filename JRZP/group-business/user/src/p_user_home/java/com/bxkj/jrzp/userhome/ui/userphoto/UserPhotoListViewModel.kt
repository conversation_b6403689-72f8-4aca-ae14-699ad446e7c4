package com.bxkj.jrzp.userhome.ui.userphoto

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.api.UserHomeRepository
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/21
 * @version: V1.0
 */
class UserPhotoListViewModel @Inject constructor(
  private val mUserHomeRepository: UserHomeRepository
) : BaseViewModel() {

  val userPhotoListViewModel = RefreshListViewModel()

  private var mQueryUserId: Int = 0
  private var mQueryInstitutionsID: Int = 0

  fun start(queryUserId: Int?, queryInstitutionsID: Int, userAuthType: Int?) {
    mQueryUserId = queryUserId ?: 0
    mQueryInstitutionsID = queryInstitutionsID

    initUserPhotoListViewModel(userAuthType)

    userPhotoListViewModel.refresh()
  }

  private fun initUserPhotoListViewModel(userAuthType: Int? = AuthenticationType.PERSONAL) {
    when {
      userAuthType == AuthenticationType.PERSONAL -> {
        setupPersonalPhotoListViewModel()
      }
      AuthenticationType.isHROrEnterprise(userAuthType!!) -> {
        setupEnterprisePhotoListViewModel()
      }
      userAuthType == AuthenticationType.SCHOOL -> {
        setupSchoolListViewModel()
      }
      AuthenticationType.isInstitutions(userAuthType) -> {
        setupInstitutionsListViewModel()
      }
    }
  }

  private fun setupInstitutionsListViewModel() {
    userPhotoListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserHomeRepository.getInstitutionsNewList(
          mQueryUserId,
          mQueryInstitutionsID,
          getSelfUserID(),
          UserInfoNavigationData.NAVIGATION_PHOTO,
          getShowType(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          userPhotoListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            userPhotoListViewModel.noMoreData()
          } else {
            userPhotoListViewModel.loadError()
          }
        })
      }
    }
  }

  private fun setupSchoolListViewModel() {
    userPhotoListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserHomeRepository.getSchoolNewsList(
          mQueryUserId,
          getSelfUserID(),
          UserInfoNavigationData.NAVIGATION_PHOTO,
          getShowType(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          userPhotoListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            userPhotoListViewModel.noMoreData()
          } else {
            userPhotoListViewModel.loadError()
          }
        })
      }
    }
  }

  private fun setupPersonalPhotoListViewModel() {
    userPhotoListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserHomeRepository.getPersonalNewsList(
          mQueryUserId,
          getSelfUserID(),
          UserInfoNavigationData.NAVIGATION_PHOTO,
          getShowType(),
          currentPage,
          30
        ).handleResult({
          userPhotoListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            userPhotoListViewModel.noMoreData()
          } else {
            userPhotoListViewModel.loadError()
          }
        })
      }
    }
  }

  private fun setupEnterprisePhotoListViewModel() {
    userPhotoListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserHomeRepository.getEnterpriseNewsList(
          mQueryUserId,
          getSelfUserID(),
          UserInfoNavigationData.NAVIGATION_PHOTO,
          getShowType(),
          currentPage,
          30
        ).handleResult({
          userPhotoListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            userPhotoListViewModel.noMoreData()
          } else {
            userPhotoListViewModel.loadError()
          }
        })
      }
    }
  }

  private fun getShowType(): Int {
    return if (mQueryUserId == getSelfUserID()) 2 else 1
  }
}