package com.bxkj.jrzp.userhome.ui.usernews.viewbinder

import android.os.Build
import android.view.View
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.data.GalleryItem
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.userhome.data.UserMomentData
import com.bxkj.jrzp.userhome.data.UserMomentData.UserPicData
import com.bxkj.jrzp.userhome.data.UserNewsItemData
import com.bxkj.personal.ui.activity.gallery.ImageGalleryNavigation
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation
import com.bxkj.support.upload.data.FileItem

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/18
 * @version: V1.0
 */
class UserMomentViewBinder constructor(private val isSelf: Boolean) :
  DefaultViewBinder<UserNewsItemData>(R.layout.user_recycler_user_moment_item, BR.data, false) {

  override fun onBindViewHolder(holder: SuperViewHolder, item: UserNewsItemData, position: Int) {
    super.onBindViewHolder(holder, item, position)
    holder.bind(BR.isSelf, isSelf)
    item.dongtai?.let {
      holder.bind(BR.data, it)
      if (it.hasPicInfo()) {
        if (it.onlySinglePic()) {
          if (Build.VERSION.SDK_INT > 21) {
            holder.findViewById<ImageView>(R.id.iv_single_pic).transitionName =
              it.getFirstMediaInfo()?.sppic
          }
        } else {
          val momentPicListAdapter = object : SimpleDBListAdapter<UserPicData>(
            holder.itemView.context,
            R.layout.user_recycler_user_moment_child_photo_item
          ) {
            override fun convert(
              holder: SuperViewHolder,
              viewType: Int,
              item: UserPicData,
              position: Int,
            ) {
              super.convert(holder, viewType, item, position)
              if (Build.VERSION.SDK_INT > 21) {
                holder.findViewById<ImageView>(R.id.iv_item).transitionName = item.sppic
              }
            }
          }.apply {
            setOnItemClickListener(object :
              com.bxkj.common.adapter.superadapter.SuperItemClickListener {
              override fun onClick(v: View, position: Int) {
                val imgList = ArrayList<GalleryItem>()
                data.forEach { imgItem ->
                  imgList.add(FileItem.fromUrl(imgItem.sppic))
                }
                ImageGalleryNavigation.navigate(imgList, position).start()
              }
            })
          }
          it.picList?.let { picList ->
            var finalShowPicList = picList
            if (picList.size > 3) {
              finalShowPicList = picList.subList(0, 3)
            }
            momentPicListAdapter.reset(finalShowPicList)
            val recyclerChildPics =
              holder.findViewById<RecyclerView>(R.id.recycler_pic_list)
            recyclerChildPics.layoutManager = GridLayoutManager(
              holder.itemView.context,
              getShowPicSpanCount(finalShowPicList)
            )
            recyclerChildPics.isNestedScrollingEnabled = false
            if (recyclerChildPics.itemDecorationCount == 0) {
              recyclerChildPics.addItemDecoration(
                GridItemDecoration(
                  ContextCompat.getDrawable(
                    holder.itemView.context,
                    R.drawable.divider_8
                  )
                )
              )
            }
            recyclerChildPics.setOnTouchListener { v, event ->
              holder.itemView.onTouchEvent(event)
            }
            recyclerChildPics.adapter = momentPicListAdapter
          }
        }
      }
      if (it.hasJobInfo() && !it.onlyOneJob()) {
        val momentJobListAdapter = SimpleDBListAdapter<UserMomentData.UserJobData>(
          holder.itemView.context,
          R.layout.user_recycler_user_moment_child_job_item
        ).apply {
          setOnItemClickListener(object :
            com.bxkj.common.adapter.superadapter.SuperItemClickListener {
            override fun onClick(v: View, position: Int) {
              JobDetailsNavigation.navigate(data[position].id).start()
            }
          })
        }
        momentJobListAdapter.reset(it.relList)
        val recyclerChildJobs =
          holder.findViewById<RecyclerView>(R.id.recycler_moment_attach_job)
        recyclerChildJobs.layoutManager =
          LinearLayoutManager(
            holder.itemView.context,
            LinearLayoutManager.HORIZONTAL,
            false
          )
        recyclerChildJobs.isNestedScrollingEnabled = false
        recyclerChildJobs.adapter = momentJobListAdapter
      }
    }
  }

  private fun getShowPicSpanCount(finalShowPicList: List<UserPicData>): Int {
    return if (finalShowPicList.size == 1) 1 else 3
  }

}