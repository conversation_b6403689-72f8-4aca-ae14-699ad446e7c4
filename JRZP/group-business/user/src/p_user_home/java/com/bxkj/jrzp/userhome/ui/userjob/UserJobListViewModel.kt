package com.bxkj.jrzp.userhome.ui.userjob

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.user.DeleteStep
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.userhome.api.UserHomeRepository
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.data.UserNewsItemData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/24
 * @version: V1.0
 */
class UserJobListViewModel @Inject constructor(
  private val mUserHomeRepository: UserHomeRepository,
  private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

  companion object {
    const val JOB_TYPE_ALL = 0
    const val JOB_TYPE_SCHOOL_RECRUIT = 9
    const val JOB_TYPE_NORMAL_RECRUIT = 7
  }

  val jobListViewModel = RefreshListViewModel()

  val showConfirmDeleteTipsCommand =
    LiveEvent<String>()

  private var mQueryUserId: Int = 0
  private var mJobType: Int = 0

  private var mPendingDeleteJob: UserNewsItemData? = null

  init {
    setupJobListViewModel()
  }

  fun setupPageParams(queryUserId: Int?) {
    queryUserId?.let {
      mQueryUserId = queryUserId
    }
  }

  fun showAllJob() {
    mJobType = JOB_TYPE_ALL
    jobListViewModel.refresh(true)
  }

  fun showSchoolRecruitJob() {
    mJobType = JOB_TYPE_SCHOOL_RECRUIT
    jobListViewModel.refresh(true)
  }

  fun showNormalJob() {
    mJobType = JOB_TYPE_NORMAL_RECRUIT
    jobListViewModel.refresh(true)
  }

  fun currentSelf(): Boolean {
    return UserUtils.userIsSelf(mQueryUserId)
  }

  fun requestDeleteJobTips(userNewsItemData: UserNewsItemData) {
    mPendingDeleteJob = userNewsItemData
    userNewsItemData.relList?.let { job ->
      viewModelScope.launch {
        showLoading()
        mOpenUserRepository.deleteReleasedInfo(
          getSelfUserID(),
          userNewsItemData.lanmu,
          job.id,
          DeleteStep.STEP_TIPS
        ).handleResult({
          showConfirmDeleteTipsCommand.value = it
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun confirmDeleteJob() {
    mPendingDeleteJob?.let {
      deleteJob(it)
    }
  }

  private fun setupJobListViewModel() {
    jobListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserHomeRepository.getEnterpriseNewsList(
          mQueryUserId,
          getSelfUserID(),
          UserInfoNavigationData.NAVIGATION_JOB,
          getShowType(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          mJobType
        ).handleResult({
          jobListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            jobListViewModel.noMoreData()
          } else {
            jobListViewModel.loadError()
          }
        })
      }
    }
  }

  private fun getShowType(): Int {
    return if (mQueryUserId == getSelfUserID()) 2 else 1
  }

  private fun deleteJob(userNewsItemData: UserNewsItemData) {
    viewModelScope.launch {
      showLoading()
      mOpenUserRepository.deleteReleasedInfo(
        getSelfUserID(),
        userNewsItemData.lanmu,
        userNewsItemData.relList?.id ?: 0,
        DeleteStep.STEP_CONFIRM
      ).handleResult({
        if (jobListViewModel.remove(userNewsItemData) == 0) {
          jobListViewModel.refresh()
        }
        showToast(R.string.common_delete_success)
      }, {
        showToast(it.errMsg)
      }, {
        mPendingDeleteJob = null
        hideLoading()
      })
    }
  }

}