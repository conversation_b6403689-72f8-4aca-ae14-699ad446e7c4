package com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityCampusTalkManagementBinding
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 *
 * @author: sanjin
 * @date: 2022/5/31
 */
class CampusTalkManagementActivity :
    BaseDBActivity<UserActivityCampusTalkManagementBinding, BaseViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, CampusTalkManagementActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_activity_campus_talk_management

    override fun initPage(savedInstanceState: Bundle?) {
        setupContent()
    }

    private fun setupContent() {
        viewBinding.indicatorSeminarType.navigator = CommonNavigator(this).apply {
            adapter =
                MagicIndicatorAdapter(getResArray(R.array.user_enterprise_job_fair_type))
                    .apply {
                        setOnTabClickListener(object : OnTabClickListener {
                            override fun onTabClicked(v: View, index: Int) {
                                viewBinding.vpContent.currentItem = index
                            }
                        })
                    }
            isAdjustMode = true
        }

        viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                return CampusTalkListFragment.newInstance(position)
            }

        }

        viewBinding.vpContent.attachIndicator(viewBinding.indicatorSeminarType)
    }
}