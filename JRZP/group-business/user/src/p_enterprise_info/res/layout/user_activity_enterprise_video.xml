<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.video.VideoType" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo.EnterpriseVideoViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/user_enterprise_video_local"
      app:title="@string/user_enterprise_video_page_title" />

    <TextView
      style="@style/Text.14sp.333333"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_12"
      android:text="@string/user_enterprise_video_tips" />

    <TextView
      style="@style/Text.16sp.333333.Bold"
      android:layout_width="match_parent"
      android:background="@drawable/bg_ffffff"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_12"
      android:text="@string/user_enterprise_video_title" />

    <include
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.videoListViewModel}" />


    <TextView
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_24"
      android:enabled="@{viewModel.selectedItem.size()&gt;0}"
      android:onClick="@{()->viewModel.confirmSelected()}"
      android:paddingStart="@dimen/dp_8"
      android:paddingTop="@dimen/dp_6"
      android:paddingEnd="@dimen/dp_8"
      android:paddingBottom="@dimen/dp_6"
      android:text="@string/user_enterprise_video_confirm" />

  </LinearLayout>
</layout>