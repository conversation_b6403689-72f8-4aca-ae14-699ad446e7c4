package com.bxkj.jrzp.user.selectidentity

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.account.ui.login.LoginNavigation
import com.bxkj.account.ui.register.RegisterNavigation
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.user.R
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.databinding.UserActivitySelectIdentityBinding
import com.bxkj.jrzp.user.institutions.ui.institutionsinfo.InstitutionsInfoNavigation
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoNavigation
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.personal.ui.activity.main.PersonalMainNavigation
import com.kongzue.dialogx.dialogs.MessageDialog
import com.kongzue.dialogx.interfaces.OnBindView

/**
 * @Description: 选择用户身份
 * @author:45457
 * @date: 2020/8/10
 * @version: V1.0
 */
@Route(path = SelectIdentityNavigation.PATH)
class SelectIdentityActivity : BaseDBActivity<UserActivitySelectIdentityBinding, BaseViewModel>(),
  OnClickListener {

  private val extraToRegisterPage by lazy {
    intent.getBooleanExtra(
      SelectIdentityNavigation.EXTRA_TO_REGISTER,
      false
    )
  }

  private var mTempAuthType = AuthenticationType.UN_AUTH_CODE

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_select_identity

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this

    if (UserUtils.logged() && UserUtils.isPersonalRole()) {
      viewBinding.clUser.visibility = View.GONE
    }

    subscribeAuthSubmitMsg()
  }

  private fun subscribeAuthSubmitMsg() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_CERTIFICATION_SUBMIT_SUCCESS) {
            if (mTempAuthType != AuthenticationType.UN_AUTH_CODE) {
              UserUtils.setupUserRole(mTempAuthType)
            }
            finish()
          }
        }
    )
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.cl_user -> { //个人
          if (extraToRegisterPage) {
            RegisterNavigation.create(AuthenticationType.PERSONAL).start()
          } else {
            UserUtils.setupUserRole(AuthenticationType.PERSONAL)
            PersonalMainNavigation.navigate()
              .withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
              .start()
            finish()
          }
        }

        R.id.cl_company -> {  //公司
          checkVisitorMode(getString(R.string.user_select_identity_visitor_tips)) {

            val authTypeList = listOf<Map<String, Any>>(
              mapOf(
                "type" to AuthenticationType.ENTERPRISE,
                "icon" to R.drawable.user_ic_authentication_enterprise,
                "name" to "企业认证",
                "desc" to "营业执照认证"
              ),
              mapOf(
                "type" to AuthenticationType.PERSONAL_RECRUITER,
                "icon" to R.drawable.user_ic_authentication_personal,
                "name" to "个人认证",
                "desc" to "实名信息认证"
              )
            )

            MessageDialog.build()
              .setTitle("选择认证类型")
              .setCustomView(object :
                OnBindView<MessageDialog>(R.layout.user_layout_select_auth_type) {
                override fun onBind(
                  dialog: MessageDialog?,
                  v: View?
                ) {
                  v?.findViewById<RecyclerView>(R.id.recycler_auth_type)?.apply {
                    layoutManager = LinearLayoutManager(this@SelectIdentityActivity)
                    adapter = object : SuperAdapter<Map<String, Any>>(
                      this@SelectIdentityActivity, R.layout.user_layout_auth_type_item
                    ) {
                      override fun convert(
                        holder: SuperViewHolder,
                        viewType: Int,
                        item: Map<String, Any>?,
                        position: Int
                      ) {
                        item?.let { notNullItem ->
                          holder.findViewById<ImageView>(R.id.iv_logo).setImageResource(
                            notNullItem.get("icon") as Int
                          )
                          holder.findViewById<TextView>(R.id.tv_title).text =
                            notNullItem.get("name") as String
                          holder.findViewById<TextView>(R.id.tv_desc).text =
                            notNullItem.get("desc") as String
                        }
                      }
                    }.apply {
                      addItemDecoration(
                        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_12))
                          .build()
                      )
                      setOnItemClickListener(object : SuperItemClickListener {
                        override fun onClick(v: View, position: Int) {
                          val selectType = authTypeList[position]["type"] as Int
                          if (extraToRegisterPage) {
                            RegisterNavigation.create(selectType).start()
                          } else {
                            mTempAuthType = selectType
                            if (UserUtils.logged()) {
                              if (selectType == AuthenticationType.PERSONAL_RECRUITER) {
                                IDCardValidationNavigation.create().start()
                              } else {
                                BusinessBasicInfoNavigation.navigate(true).start()
                              }
                            } else {
                              LoginNavigation.navigate(selectType, false).start()
                            }
                          }
                        }
                      })
                      data = authTypeList
                    }
                  }
                }
              })
              .show(this)

            // if (extraToRegisterPage) {
            //   RegisterNavigation.create(AuthenticationType.ENTERPRISE).start()
            // } else {
            //   mTempAuthType = AuthenticationType.ENTERPRISE
            //   if (UserUtils.logged()) {
            //     BusinessBasicInfoNavigation.navigate(true).start()
            //   } else {
            //     LoginNavigation.navigate(AuthenticationType.ENTERPRISE, false).start()
            //   }
            // }
          }
        }

        R.id.cl_school -> { //学校
          checkVisitorMode(getString(R.string.user_select_identity_visitor_tips)) {
            if (extraToRegisterPage) {
              RegisterNavigation.create(AuthenticationType.SCHOOL).start()
            } else {
              mTempAuthType = AuthenticationType.SCHOOL
              if (UserUtils.logged()) {
                SchoolInfoNavigation.navigate(SchoolInfoNavigation.NEXT_STEP_AUTH)
                  .start()
              } else {
                LoginNavigation.navigate(AuthenticationType.SCHOOL, false).start()
              }
            }
          }
        }

        R.id.cl_institutions -> { //事业单位
          checkVisitorMode(
            getString(R.string.user_select_identity_visitor_tips)
          ) {
            if (extraToRegisterPage) {
              RegisterNavigation.create(AuthenticationType.SCHOOL).start()
            } else {
              mTempAuthType = AuthenticationType.INSTITUTIONS
              if (UserUtils.logged()) {
                InstitutionsInfoNavigation.create(InstitutionsInfoNavigation.NEXT_AUTH)
                  .start()
              } else {
                LoginNavigation.navigate(AuthenticationType.INSTITUTIONS, false)
                  .start()
              }
            }
          }
        }

        else -> {
        }
      }
    }
  }
}