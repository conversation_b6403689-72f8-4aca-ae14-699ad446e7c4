<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.ui.personalauthentication.steptwo.PersonalAuthStepTwoViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical">

    <LinearLayout
      style="@style/match_wrap"
      android:background="@color/common_eaeaea"
      android:orientation="vertical">

      <TextView
        style="@style/Text.16sp.333333.Bold"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_14"
        android:text="@string/user_personal_authentication_step_two_tips_1" />

      <ImageView
        android:id="@+id/iv_hand_held_ID_card"
        android:layout_width="@dimen/user_personal_authentication_step_two_pic_size"
        android:layout_height="@dimen/user_personal_authentication_step_two_pic_size"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_16"
        android:onClick="@{onClickListener}" />
    </LinearLayout>

    <TextView
      style="@style/Text.16sp.333333.Bold"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginTop="@dimen/dp_14"
      android:text="@string/user_personal_authentication_step_one_tips_2" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginTop="@dimen/dp_18"
      android:layout_marginEnd="@dimen/dp_12"
      android:orientation="horizontal">

      <ImageView
        style="@style/wrap_wrap"
        android:layout_weight="1"
        android:src="@drawable/user_ic_authentication_hand_held_id_card_require_one" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_weight="1"
        android:src="@drawable/user_ic_authentication_hand_held_id_card_require_two" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_weight="1"
        android:src="@drawable/user_ic_authentication_hand_held_id_card_require_three" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_weight="1"
        android:src="@drawable/user_ic_authentication_hand_held_id_card_require_four" />
    </LinearLayout>

    <TextView
      android:id="@+id/tv_submit"
      style="@style/Button.Basic"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/common_dp_56"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_30"
      android:enabled="@{viewModel.handHeldIDCard!=null}"
      android:onClick="@{onClickListener}"
      android:text="@string/user_personal_authentication_complete" />
  </LinearLayout>
</layout>
