package com.bxkj.jrzp.user.ui.jobproveauth

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.jrzp.user.repository.AuthenticationRepository
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.support.upload.data.UploadFileResult
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/28
 * @version: V1.0
 */
class JobProveAuthViewModel @Inject constructor(
  private val mUploadRepository: UploadRepository,
  private val mAuthenticationRepository: AuthenticationRepository
) : BaseViewModel() {

  val jobProvePic = MutableLiveData<String?>().apply { value = null }

  val submitSuccessEvent = LiveEvent<Void>()

  private var mUploadResult: UploadFileResult? = null

  fun submit() {
    mUploadResult?.let {
      viewModelScope.launch {
        showLoading()
        mAuthenticationRepository.submitAuthentication(
          getSelfUserID(),
          "",
          it.url,
          it.id.toString(),
          AuthenticationType.JOB_PROVE
        ).handleResult({
          submitSuccessEvent.call()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val selectedImg = data.getSelectedFirstMediaPath()
        jobProvePic.value = selectedImg
        uploadProveImg(selectedImg)
      }
    }
  }

  private fun uploadProveImg(path: String) {
    viewModelScope.launch {
      showLoading()
      mUploadRepository.uploadFileV3(
        path,
        UploadFileRequestParams.getImageUploadParams(
          getSelfUserID(),
          UploadFileRequestParams.PATH_NAME_BUSINESS_LICENSE
        )
      ).handleResult({
        mUploadResult = it
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }
}