package com.bxkj.jrzp.user.ui.idcardvalidation

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * author:Sanjin
 * date:2025/5/19
 **/
class IDCardValidationNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/idcardvalidation"

    const val EXTRA_NEXT_STEP = "NEXT_STEP"

    const val NEXT_STEP_FINISH = 0

    const val NEXT_STEP_TO_MAIN = 1

    @JvmOverloads
    @JvmStatic
    fun create(nextStep: Int = NEXT_STEP_FINISH): RouterNavigator {
      return Router.getInstance().to(PATH)
        .apply {
          withInt(EXTRA_NEXT_STEP, nextStep)
        }
    }
  }
}