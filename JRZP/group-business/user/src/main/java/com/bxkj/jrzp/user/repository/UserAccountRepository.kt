package com.bxkj.jrzp.user.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.user.AccountType.Type
import com.bxkj.jrzp.user.api.NAccountApi
import com.bxkj.jrzp.user.data.UserAccountData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/3
 * @version: V1.0
 */
class UserAccountRepository @Inject constructor(
  private val mAccountApi: NAccountApi
) : BaseRepo() {

  /**
   * 获取用户收款账户
   */
  suspend fun getUserReceivingAccount(): ReqResponse<UserAccountData> {
    return httpRequest {
      mAccountApi.getUserReceivingAccount()
    }
  }

  /**
   * 获取用户账户列表
   */
  suspend fun getUserAccountList(
    userID: Int,
    @Type type: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<UserAccountData>> {
    return httpRequest {
      mAccountApi.getUserAccountList(
        mapOf(
          "uid" to userID,
          "type" to type,
          "pageIndex" to pageIndex,
          "pageSize" to pageSize
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 添加支付宝账户
   */
  suspend fun addReceivingAccount(
    userAccountData: UserAccountData
  ): ReqResponse<Nothing> {
    return httpRequest {
      mAccountApi.addAlipayAccount(
        userAccountData.objectEncrypt()
      )
    }
  }
}