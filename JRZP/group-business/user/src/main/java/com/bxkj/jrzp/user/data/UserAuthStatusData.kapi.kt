package com.bxkj.jrzp.user.data

import com.bxkj.common.enums.AuthenticationType
import com.bxkj.common.enums.AuthenticationType.Type

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class UserAuthStatusData private constructor(
  var type: Int,
  var status: Int,
  var shResult: String? = "",
  var picDomain: String? = "",
  var picList: List<Pic>? = null
) {

  companion object {
    fun getDefault(): UserAuthStatusData {
      return UserAuthStatusData(AuthenticationType.PERSONAL, 1)
    }

    fun getSuccessStatus(@Type authType: Int): UserAuthStatusData {
      return UserAuthStatusData(authType, 2)
    }
  }

  data class Pic(
    var pic: String
  )

  fun authProcessing(): Boolean {
    return status == 0
  }

  fun authSuccess(): Boolean {
    return status == 2
  }

  fun typeAuthSuccess(@Type authType: Int): Boolean {
    return (type == authType) && authSuccess()
  }

  fun authFailed(): Boolean {
    return status == 1
  }

  fun getAuthStatusText(): String {
    return when (status) {
      0 -> {
        "认证中"
      }
      1 -> {
        "认证失败"
      }
      2 -> {
        "认证成功"
      }
      else -> {
        "申请认证"
      }
    }
  }
}
