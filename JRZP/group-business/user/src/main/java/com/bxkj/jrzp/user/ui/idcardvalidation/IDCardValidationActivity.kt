package com.bxkj.jrzp.user.ui.idcardvalidation

import android.os.Bundle
import android.view.View
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityIdcardValidationBinding
import com.bxkj.personal.ui.activity.main.PersonalMainNavigation
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceContant
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceVerifySdk
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyLoginListener
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyResultListener
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceError
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceVerifyResult
import com.tencent.cloud.huiyansdkface.facelight.process.FaceVerifyStatus
import com.therouter.router.Route

/**
 * author:Sanjin
 * date:2025/5/19
 **/
@Route(path = IDCardValidationNavigation.PATH)
class IDCardValidationActivity :
  BaseDBActivity<UserActivityIdcardValidationBinding, IDCardValidationViewModel>() {

  private val extraNextStep by lazy {
    intent.getIntExtra(
      IDCardValidationNavigation.EXTRA_NEXT_STEP,
      IDCardValidationNavigation.NEXT_STEP_FINISH
    )
  }

  override fun getViewModelClass(): Class<IDCardValidationViewModel> =
    IDCardValidationViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_idcard_validation

  override fun initPage(savedInstanceState: Bundle?) {
    // 设置数据绑定
    viewBinding.viewModel = viewModel

    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.errMsg.observe(this, {
      if (it.isNullOrEmpty()) {
        viewBinding.tvErrMsg.visibility = View.GONE
      } else {
        viewBinding.tvErrMsg.visibility = View.VISIBLE
        viewBinding.tvErrMsg.text = it
      }
    })

    viewModel.lockIDCardInfoCommand.observe(this, EventObserver {
      viewBinding.etName.apply {
        setContentEnabled(false)
        setContentTextColor(getResColor(R.color.cl_888888))
      }
      viewBinding.etIdCardNumber.apply {
        setContentEnabled(false)
        setContentTextColor(getResColor(R.color.cl_888888))
      }
    })

    viewModel.showFailedMsgEvent.observe(this, EventObserver {
      TipsDialog()
        .setContent(it)
        .show(supportFragmentManager)
    })

    viewModel.showErrMsgAndFinishEvent.observe(this, EventObserver {
      TipsDialog()
        .setContent(getString(R.string.user_idcard_validation_load_err_tips))
        .setOnConfirmClickListener {
          finish()
        }
        .show(supportFragmentManager)
    })

    viewModel.startFaceVerifyCommand.observe(this, EventObserver {
      showLoading()
      val faceVerifyInputData = WbCloudFaceVerifySdk.InputData(
        it.faceId,
        it.agreementNo,
        it.openApiAppId,
        it.openApiAppVersion,
        it.openApiNonce,
        it.openApiUserId,
        it.openApiSign,
        FaceVerifyStatus.Mode.GRADE,
        it.keyLicence
      )

      WbCloudFaceVerifySdk.getInstance()
        .initSdk(
          this,
          Bundle().apply { putSerializable(WbCloudFaceContant.INPUT_DATA, faceVerifyInputData) },
          object :
            WbCloudFaceVerifyLoginListener {
            override fun onLoginSuccess() {
              hiddenLoading()
              WbCloudFaceVerifySdk.getInstance()
                .startWbFaceVerifySdk(this@IDCardValidationActivity, object :
                  WbCloudFaceVerifyResultListener {
                  override fun onFinish(verifyResult: WbFaceVerifyResult?) {
                    verifyResult?.let {
                      if (verifyResult.isSuccess) {
                        showToast("身份认证通过")
                        viewModel.markAuthSuccessful()
                      } else {
                        verifyResult.error?.let { error ->
                          showToast(error.desc)
                        }
                      }
                    } ?: showToast("人脸识别初始化失败,请重试")
                    WbCloudFaceVerifySdk.getInstance().release()
                  }
                })
            }

            override fun onLoginFailed(p0: WbFaceError?) {
              hiddenLoading()
              showToast("人脸识别初始化失败,请重试")
              WbCloudFaceVerifySdk.getInstance().release()
            }
          })
    })

    viewModel.infoSubmitSuccessEvent.observe(this, EventObserver {
      if (extraNextStep == IDCardValidationNavigation.NEXT_STEP_FINISH) {
        RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_CERTIFICATION_SUBMIT_SUCCESS))
      } else {
        PersonalMainNavigation.navigate().start()
      }
      setResult(RESULT_OK)
      finish()
    })
  }
}