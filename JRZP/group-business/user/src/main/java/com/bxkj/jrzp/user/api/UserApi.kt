package com.bxkj.jrzp.user.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.friend.data.RecommendFriendItemData
import com.bxkj.jrzp.friend.data.RecommendFriendsPageData
import com.bxkj.jrzp.user.data.FaceVerifyParams
import com.bxkj.jrzp.user.data.InstitutionsInfoData
import com.bxkj.jrzp.user.data.UserAccountBalanceData
import com.bxkj.jrzp.user.mine.data.EnterpriseAccountData
import com.bxkj.jrzp.user.mine.data.UserHomeData
import com.bxkj.jrzp.user.schoolinfo.data.DoubleElectionData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
interface UserApi {

  @POST("/Company/UpdateComInfo/")
  suspend fun updatePersonalRecruiterDesc(@Body zpRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST("/BusinessLicense/UpdateRenlianRenzheng/")
  suspend fun markFaceAuthSuccessful(): BaseResponse<Nothing>

  @POST("/BusinessLicense/GetRenlianRenzheng/")
  suspend fun getFaceVerityParams(@Body encryptReqParams: EncryptReqParams): BaseResponse<FaceVerifyParams>

  @POST("/BusinessLicense/GetUserRenzhenStatus/")
  suspend fun getUserIDCardInfo(): BaseResponse<Map<String, String?>>

  @POST("/Resume/EditResumeEditTimeV2/")
  suspend fun refreshResume(): BaseResponse<Nothing>

  @POST(UserApiConstants.I_GET_USER_ACCOUNT_BALANCE)
  suspend fun getUserAccountBalance(@Body encryptReqParams: EncryptReqParams): BaseResponse<UserAccountBalanceData>

  @POST(UserApiConstants.I_GET_AD_IMG_URL)
  suspend fun getAdImgUrl(): BaseResponse<String>

  @POST(UserApiConstants.I_GET_USER_HOME_INFO)
  suspend fun getUserHomeInfo(@Body mRequestBody: ZPRequestBody): BaseResponse<UserHomeData>

  @POST(UserApiConstants.I_GET_USER_COLLECTION_JOB_COUNT)
  suspend fun getCollectionJobCount(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(UserApiConstants.I_GET_USER_FOLLOW_COMPANY_COUNT)
  suspend fun getFollowCompanyCount(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(UserApiConstants.I_GET_USER_SUBMIT_RECORD_COUNT)
  suspend fun getUserSubmitRecordCount(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(UserApiConstants.I_GET_SEW_ME_COMPANY_COUNT)
  suspend fun getSewMeCompanyCount(@Body mRequestBody: ZPRequestBody): BaseResponse<Map<String, Int>>

  @POST(UserApiConstants.I_GET_INVITE_TO_DELIVERY_COUNT)
  suspend fun getInviteToDeliveryCount(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(UserApiConstants.I_GET_ENTERPRISE_DATA_COUNT_INFO)
  suspend fun getEnterpriseDataCountInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<EnterpriseAccountData>

  @POST(UserApiConstants.I_DELETE_MY_PUBLISH)
  suspend fun deleteMyPublish(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  //============================ 好友相关（Friends） ============================//
  @POST("/User/GetTuijianUserListByPage/")
  suspend fun getRecommendFriendList(@Body encryptReqParams: EncryptReqParams): BaseResponse<RecommendFriendsPageData>

  @POST("/User/GetTongxunluUser/")
  suspend fun matchContractsFriend(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<RecommendFriendItemData>>

  @POST("/PushToken/AddPushToken/")
  suspend fun bindPushToken(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  //============================== 企业信息 ==============================//
  @POST("/NewsBaoming/GetBaomingShuangxuanhuiListByPage/")
  suspend fun getRegisteredDoubleElections(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<DoubleElectionData>>

  @POST("/Video/SetShipin2IsQixuan/")
  suspend fun setupEnterpriseVideo(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  //============================== 事业单位 ==============================//
  @POST("/Danwei/GetDanweiInfo/")
  suspend fun getInstitutionsInfo(): BaseResponse<InstitutionsInfoData>

  @POST("/Danwei/AddOrEditDanweiInfo/")
  suspend fun updateInstitutionsInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Danwei/GetDanweiTypeList/")
  suspend fun getInstitutionsTypeList(): BaseResponse<List<PickerOptionsData>>
}