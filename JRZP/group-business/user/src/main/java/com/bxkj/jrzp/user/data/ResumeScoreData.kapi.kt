package com.bxkj.jrzp.user.data

import com.google.gson.annotations.SerializedName

/**
 *
 * @author: sanjin
 * @date: 2022/9/13
 */
data class ResumeScoreData(
  @SerializedName("resfs")
  var score: Int? = 0,
  @SerializedName("resfs1")
  var level: String? = "",
  @SerializedName("resfs2")
  var exposure: String? = "",
  var top: Int = 0
) {
  fun noResume(): <PERSON><PERSON>an {
    return score == 30
  }

  fun isResumeOnTop(): Bo<PERSON>an {
    return top == 1
  }
}