package com.bxkj.jrzp.user.di

import com.bxkj.jrzp.commission.api.CommissionApi
import com.bxkj.jrzp.orderrecord.api.CoursesOrderApi
import com.bxkj.jrzp.user.api.NAccountApi
import com.bxkj.jrzp.user.api.OpenUserApi
import com.bxkj.jrzp.user.api.UserApi
import com.bxkj.jrzp.user.api.AuthenticationApi
import com.bxkj.jrzp.user.api.OpenAuthenticationApi
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoApi
import com.bxkj.jrzp.user.videorelate.api.VideoRelateApi
import com.bxkj.jrzp.user.withdraw.api.WithdrawApi
import com.bxkj.jrzp.userhome.api.OpenUserHomeApi
import com.bxkj.jrzp.userhome.api.UserHomeApi
import dagger.Module
import dagger.Provides
import retrofit2.Retrofit

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
@Module
class UserApiModule {

  @Provides
  fun provideCommissionApi(retrofit: Retrofit): CommissionApi {
    return retrofit.create(CommissionApi::class.java)
  }

  @Provides
  fun provideUserApi(retrofit: Retrofit): UserApi {
    return retrofit.create(UserApi::class.java)
  }

  @Provides
  fun provideCoursesOrderApi(retrofit: Retrofit): CoursesOrderApi {
    return retrofit.create(CoursesOrderApi::class.java)
  }

  @Provides
  fun provideOpenUserApi(retrofit: Retrofit): OpenUserApi {
    return retrofit.create(OpenUserApi::class.java)
  }

  @Provides
  fun provideWithdrawApi(retrofit: Retrofit): WithdrawApi {
    return retrofit.create(WithdrawApi::class.java)
  }

  @Provides
  fun provideNAccountApi(retrofit: Retrofit): NAccountApi {
    return retrofit.create(NAccountApi::class.java)
  }

  @Provides
  fun provideVideoRelateApi(retrofit: Retrofit): VideoRelateApi {
    return retrofit.create(VideoRelateApi::class.java)
  }

  //**************************************认证**************************************//

  @Provides
  fun provideAuthenticationApi(retrofit: Retrofit): AuthenticationApi {
    return retrofit.create(AuthenticationApi::class.java)
  }

  @Provides
  fun provideOpenAuthApi(retrofit: Retrofit): OpenAuthenticationApi {
    return retrofit.create(OpenAuthenticationApi::class.java)
  }

  //**************************************个人中心**************************************//

  @Provides
  fun provideUserHomeApi(retrofit: Retrofit): UserHomeApi {
    return retrofit.create(UserHomeApi::class.java)
  }

  @Provides
  fun provideOpenUserHomeApi(retrofit: Retrofit): OpenUserHomeApi {
    return retrofit.create(OpenUserHomeApi::class.java)
  }

  //============================ 学校 ============================//
  @Provides
  fun provideSchoolInfoApi(retrofit: Retrofit): SchoolInfoApi {
    return retrofit.create(SchoolInfoApi::class.java)
  }
}