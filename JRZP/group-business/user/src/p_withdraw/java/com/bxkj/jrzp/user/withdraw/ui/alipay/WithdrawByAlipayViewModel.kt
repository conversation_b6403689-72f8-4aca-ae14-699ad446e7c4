package com.bxkj.jrzp.user.withdraw.ui.alipay

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.AccountType
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.data.UserAccountData
import com.bxkj.jrzp.user.repository.UserAccountRepository
import com.bxkj.jrzp.user.ui.alipaylist.AlipayAccountListNavigation
import com.bxkj.jrzp.user.data.UserAccountBalanceData
import com.bxkj.jrzp.user.repository.UserRepository
import com.bxkj.jrzp.user.withdraw.repository.WithdrawRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/1
 * @version: V1.0
 */
class WithdrawByAlipayViewModel @Inject constructor(
    private val mUserRepository: UserRepository,
    private val mUserAccountRepository: UserAccountRepository,
    private val mWithdrawRepository: WithdrawRepository
) : BaseViewModel() {

    val alipayAccount = MutableLiveData<String>()
    val alipayName = MutableLiveData<String>()
    val withdrawAmount = MutableLiveData<String>()
    val accountBalance = MutableLiveData<UserAccountBalanceData>()
    val accountBalanceHint = MutableLiveData<String>()
    val hasAccount = MutableLiveData<Boolean>().apply { value = false }

    val submitSuccessEvent = LiveEvent<Void>()

    init {
        getAccountBalance()
        getUserAccountList()
    }

    private fun getAccountBalance() {
        viewModelScope.launch {
            mUserRepository.getAccountBalance(getSelfUserID())
                .handleResult({
                    it?.let {
                        if (it.totalMoney < 10000) {
                            showToast(R.string.user_withdraw_no_balance_tips)
                        }
                        accountBalance.value = it
                        accountBalanceHint.value = "可提现${it.getRealBalanceText()}元"
                    }
                })
        }
    }

    private fun getUserAccountList() {
        viewModelScope.launch {
            showLoading()
            mUserAccountRepository.getUserAccountList(getSelfUserID(), AccountType.ALIPAY_TYPE, 1, 1)
                .handleResult({
                    it?.let {
                        hasAccount.value = true
                        val defaultAccount = it[0]
                        alipayName.value = defaultAccount.cardUName
                        alipayAccount.value = defaultAccount.cardID
                    }
                }, final = {
                    hideLoading()
                })
        }
    }

    fun withdrawAll() {
        accountBalance.value?.let {
            withdrawAmount.value = it.getRealBalance()
        }
    }

    fun submitWithdraw() {
        viewModelScope.launch {
            showLoading()
            var intWithdrawAmount = withdrawAmount.value?.toFloat() ?: 0f
            if (intWithdrawAmount == 0f) {
                showToast(R.string.user_withdraw_amount_must_more_the_zero)
                hideLoading()
                return@launch
            } else {
                intWithdrawAmount *= 100
                accountBalance.value?.let {
                    if (intWithdrawAmount > it.totalMoney) {
                        showToast("最多可提现${it.getRealBalanceText()}元")
                        hideLoading()
                        return@launch
                    }
                }
            }
            mWithdrawRepository.submitWithdraw(
                getSelfUserID(),
                AccountType.ALIPAY_TYPE,
                alipayName.value!!,
                alipayAccount.value!!,
                intWithdrawAmount.toInt()
            ).handleResult({
                submitSuccessEvent.call()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == WithdrawByAlipayActivity.TO_ACCOUNT_LIST_CODE && resultCode == Activity.RESULT_OK) {
            data?.let {
                val resultAccount =
                    data.getParcelableExtra<UserAccountData>(AlipayAccountListNavigation.EXTRA_RESULT_ALIPAY_ACCOUNT)
                resultAccount?.let {
                    alipayName.value = resultAccount.cardUName
                    alipayAccount.value = resultAccount.cardID
                }
            }
        }
    }

}