package com.bxkj.jrzp.commission.ui.list

import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.WebActivity
import com.bxkj.common.widget.marquee.CommonDBMarqueeAdapter
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityCommissionListBinding
import com.bxkj.jrzp.user.withdraw.ui.alipay.WithdrawByAlipayNavigation
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener
import kotlin.math.abs

/**
 * @Description:
 * @author:45457 佣金列表
 * @date: 2020/7/29
 * @version: V1.0
 */
@Route(path = CommissionListNavigation.PATH)
class CommissionListActivity :
  BaseDBActivity<UserActivityCommissionListBinding, CommissionListViewModel>() {
  override fun getViewModelClass(): Class<CommissionListViewModel> =
    CommissionListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_commission_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    statusBarManager.titleBar(viewBinding.titleBar).init()

    setupAppBarLayoutScrollListener()
    setupBrokerageListAdapter()

    subscribeViewModelEvent()

    setupTitleBarRightClickListener()

    viewModel.start()
  }

  override fun onResume() {
    super.onResume()
    viewModel.refreshAccountBalance()
  }

  private fun setupTitleBarRightClickListener() {
    viewBinding.titleBar.setRightOptionClickListener {
      Router.getInstance().to(WebActivity.URL)
        .withString(
          WebActivity.EXTRA_URL,
          "https://jrzpapi2.jdzj.com/page/yongjinguize.html?ly=android"
        )
        .withBoolean(WebActivity.EXTRA_FULL_SCREEN, true)
        .start()
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.commissionNotice.observe(this, Observer {
      viewBinding.marqueeNotice.apply {
        setAdapter(CommonDBMarqueeAdapter(R.layout.user_marquee_commission_notice_item, it))
        start()
      }
    })

    viewModel.toWithdrawCommand.observe(this, Observer {
      WithdrawByAlipayNavigation.navigate().start()
    })
  }

  private fun setupAppBarLayoutScrollListener() {
    viewBinding.appBarLayout.addOnOffsetChangedListener(OnOffsetChangedListener { _, verticalOffset ->
      val absOffset = abs(verticalOffset)
      if (absOffset <= 300) {
        if (absOffset <= 150) {
          viewBinding.titleBar.setLeftImage(R.drawable.ic_back_white)
          viewBinding.titleBar.setTitleColor(getResColor(R.color.common_white))
          viewBinding.titleBar.setRightTextColor(getResColor(R.color.common_white))
        } else {
          viewBinding.titleBar.setLeftImage(R.drawable.common_ic_back)
          viewBinding.titleBar.setTitleColor(getResColor(R.color.cl_333333))
          viewBinding.titleBar.setRightTextColor(getResColor(R.color.cl_333333))
        }
        val progress = absOffset / 300.toFloat()
        viewBinding.titleBar.background?.mutate()?.alpha = (255 * progress).toInt()
      }
    })
  }

  private fun setupBrokerageListAdapter() {
    val brokerageListAdapter = CommissionListAdapter(this).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          LearningWebNavigation.navigate(
            data[position].type,
            data[position].kcid,
            localUserId
          ).start()
        }
      })
    }
    val brokerageList = viewBinding.includeBrokerageList.recyclerContent
    brokerageList.layoutManager = LinearLayoutManager(this)
    viewModel.brokerageListViewModel.setAdapter(brokerageListAdapter)
  }

}