package com.bxkj.jrzp.user.mine.ui.group

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenAuthenticationRepository
import com.bxkj.common.enums.AuthenticationType
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class MinePageContainerViewModel @Inject constructor(
  private val mOpenAuthenticationRepository: OpenAuthenticationRepository
) : BaseViewModel() {

  val initPersonalMineCommand = MutableLiveData<VMEvent<Unit>>()
  val initEnterpriseMineCommand = MutableLiveData<VMEvent<Unit>>()
  val initSchoolMineCommand = MutableLiveData<VMEvent<Unit>>()
  val initInstitutionsMineCommand = MutableLiveData<VMEvent<Unit>>()

  val toSwitchIdentityPageCommand = MutableLiveData<VMEvent<Unit>>()

  val toSelectIdentityCommand = MutableLiveData<VMEvent<Unit>>()

  private var mCurrentPageType: Int = -1

  fun switchIdentity() {
    if (AuthenticationType.isPersonal(UserUtils.getUserAuthType())) {
      viewModelScope.launch {
        showLoading()
        mOpenAuthenticationRepository.getUserAuthStatus(getSelfUserID())
          .handleResult({
            it?.let {
              val currentAuthStatus = it[0]
              if (AuthenticationType.isPersonal(currentAuthStatus.type)) {
                toSelectIdentityCommand.value = (VMEvent(Unit))
              } else {
                toSwitchIdentityPageCommand.value = VMEvent(Unit)
              }
            } ?: let {
              toSelectIdentityCommand.value = VMEvent(Unit)
            }
          }, {
            if (it.errCode == AuthenticationType.UN_AUTH_CODE) {
              toSelectIdentityCommand.value = VMEvent(Unit)
            } else {
              showToast(it.errMsg)
            }
          }, {
            hideLoading()
          })
      }
    } else {
      UserUtils.setupUserRole(AuthenticationType.PERSONAL)
      showToast(R.string.user_mine_switch_to_personal_success)
      switchToPersonalPage()
    }
  }

  fun refresh() {
    if (UserUtils.logged()) {
      switchPageByLocalAuthState()
    }
  }

  fun resetCurrentUserRole() {
    mCurrentPageType = -1
  }

  /**
   * 根据本地保存的身份切换页面
   */
  private fun switchPageByLocalAuthState() {
    when {
      AuthenticationType.isHROrEnterprise(UserUtils.getUserAuthType()) -> {
        switchToEnterprisePage()
      }

      AuthenticationType.isSchool(UserUtils.getUserAuthType()) -> {
        switchToSchoolPage()
      }

      AuthenticationType.isInstitutions(UserUtils.getUserAuthType()) -> {
        switchToInstitutionsPage()
      }

      AuthenticationType.higherEnterpriseAuth(UserUtils.getUserAuthType()) -> {
        switchToEnterprisePage()
      }

      else -> {
        switchToPersonalPage()
      }
    }
  }

  private fun switchToSchoolPage() {
    if (mCurrentPageType != AuthenticationType.SCHOOL) {
      initSchoolMineCommand.postValue(VMEvent(Unit))
      mCurrentPageType = AuthenticationType.SCHOOL
    }
  }

  private fun switchToEnterprisePage() {
    if (mCurrentPageType != AuthenticationType.ENTERPRISE) {
      initEnterpriseMineCommand.postValue(VMEvent(Unit))
      mCurrentPageType = AuthenticationType.ENTERPRISE
    }
  }

  private fun switchToInstitutionsPage() {
    if (mCurrentPageType != AuthenticationType.INSTITUTIONS) {
      initInstitutionsMineCommand.postValue(VMEvent(Unit))
      mCurrentPageType = AuthenticationType.INSTITUTIONS
    }
  }

  private fun switchToPersonalPage() {
    if (mCurrentPageType != AuthenticationType.PERSONAL) {
      initPersonalMineCommand.postValue(VMEvent(Unit))
      mCurrentPageType = AuthenticationType.PERSONAL
    }
  }
}