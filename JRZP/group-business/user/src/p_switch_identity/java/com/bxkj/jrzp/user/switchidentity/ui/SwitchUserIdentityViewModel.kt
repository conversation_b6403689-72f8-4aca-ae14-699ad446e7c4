package com.bxkj.jrzp.user.switchidentity.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenAuthenticationRepository
import com.bxkj.common.enums.AuthenticationType
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/10/28
 */
class SwitchUserIdentityViewModel @Inject constructor(
    private val openAuthenticationRepository: OpenAuthenticationRepository
) : BaseViewModel() {

    val userIdentityChangeEvent = MutableLiveData<VMEvent<Unit>>()

    val showErrorTipsEvent = MutableLiveData<VMEvent<String>>()

    fun switchIdentity() {
        if (AuthenticationType.isPersonal(UserUtils.getUserAuthType())) {
            viewModelScope.launch {
                showLoading()
                openAuthenticationRepository.getUserAuthStatus(getSelfUserID())
                    .handleResult({
                        it?.let {
                            val currentAuthStatus = it[0]
                            UserUtils.setupUserRole(currentAuthStatus.type)
                            when {
                                AuthenticationType.isHROrEnterprise(currentAuthStatus.type) -> {
                                    showToast(R.string.user_mine_switch_to_enterprise_success)
                                }
                                AuthenticationType.isSchool(currentAuthStatus.type) -> {
                                    showToast(R.string.user_mine_switch_to_school_success)
                                }
                                AuthenticationType.isInstitutions(currentAuthStatus.type) -> {
                                    showToast(R.string.user_mine_switch_to_institutions_success)
                                }
                                else -> {
                                    showToast(R.string.user_mine_switch_to_enterprise_success)
                                }
                            }
                            noticeUserIdentityChange()
                        }
                    }, {
                        showErrorTipsEvent.value = VMEvent(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        } else {
            UserUtils.setupUserRole(AuthenticationType.PERSONAL)
            showToast(R.string.user_mine_switch_to_personal_success)
            noticeUserIdentityChange()
        }
    }

    private fun noticeUserIdentityChange() {
        userIdentityChangeEvent.value = VMEvent(Unit)
    }
}