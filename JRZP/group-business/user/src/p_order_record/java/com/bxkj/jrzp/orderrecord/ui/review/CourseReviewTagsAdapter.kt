package com.bxkj.jrzp.orderrecord.ui.review

import android.content.Context
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.orderrecord.data.CourseRatingTagData
import com.bxkj.jrzp.user.BR

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
class CourseReviewTagsAdapter constructor(context: Context, layoutId: Int) :
  SuperAdapter<CourseRatingTagData>(context, layoutId) {

  override fun openMultiSelect(): Boolean {
    return true
  }

  override fun convert(
      holder: SuperViewHolder,
      viewType: Int,
      item: CourseRatingTagData?,
      position: Int
  ) {
    holder.bind(BR.data, item)
    holder.itemView.isSelected = selectedItems.contains(item)
  }

}