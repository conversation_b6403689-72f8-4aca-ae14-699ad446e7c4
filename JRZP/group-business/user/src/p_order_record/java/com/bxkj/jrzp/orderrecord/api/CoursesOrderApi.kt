package com.bxkj.jrzp.orderrecord.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.orderrecord.data.CourseOrderGroupBuyShareData
import com.bxkj.jrzp.orderrecord.data.CourseRatingTagData
import com.bxkj.jrzp.orderrecord.data.CoursesOrderData
import com.bxkj.jrzp.orderrecord.data.IndustrialServiceOrderData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
interface CoursesOrderApi {

  @POST(CoursesOrderApiConstants.I_COURSES_ORDER_LIST)
  suspend fun getCoursesOrderList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<CoursesOrderData>>

  @POST(CoursesOrderApiConstants.I_CANCEL_COURSE_ORDER)
  suspend fun cancelCourseOrder(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST(CoursesOrderApiConstants.I_GET_COURSE_RATING_TAG)
  suspend fun getCourseRatingTag(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<CourseRatingTagData>>

  @POST(CoursesOrderApiConstants.I_ADD_ORDER_REVIEW)
  suspend fun addOrderReview(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST(CoursesOrderApiConstants.I_GET_COURSE_ORDER_DETAILS)
  suspend fun getCourseOrderDetails(@Body encryptReqParams: EncryptReqParams): BaseResponse<CoursesOrderData>

  @POST(CoursesOrderApiConstants.I_GET_GROUP_BUY_SHARE_INFO)
  suspend fun getGroupBuyShareInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<CourseOrderGroupBuyShareData>

  @POST(CoursesOrderApiConstants.I_GET_INDUSTRIAL_SERVICE_ORDER_LIST)
  suspend fun getIndustrialServiceOrderList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<IndustrialServiceOrderData>>

  @POST(CoursesOrderApiConstants.I_GET_SERVICE_ORDER_DETAILS)
  suspend fun getServiceOrderDetails(@Body mRequestBody: ZPRequestBody): BaseResponse<IndustrialServiceOrderData>

  @POST(CoursesOrderApiConstants.I_ADD_SERVICE_ORDER_COMMENT)
  suspend fun addServiceOrderComment(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST(CoursesOrderApiConstants.I_CANCEL_SERVICE_ORDER)
  suspend fun cancelServiceOrder(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>
}