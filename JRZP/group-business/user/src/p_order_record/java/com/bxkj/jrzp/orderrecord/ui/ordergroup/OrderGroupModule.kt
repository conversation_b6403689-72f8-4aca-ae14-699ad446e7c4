package com.bxkj.jrzp.orderrecord.ui.ordergroup

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.jrzp.orderrecord.ui.courseorder.CoursesOrderFragment
import com.bxkj.jrzp.orderrecord.ui.industrialserviceorderlist.IndustrialServiceOrderListFragment
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderListFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
@Module
abstract class OrderGroupModule {

  @PerFragment
  @ContributesAndroidInjector
  abstract fun coursesOrderListFragment(): CoursesOrderListFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun industrialServiceOrderListFragment(): IndustrialServiceOrderListFragment
}