package com.bxkj.jrzp.orderrecord.data

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.common.util.kotlin.convertUrlToHttp
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.api.BR
import com.google.gson.annotations.SerializedName

/**
 *
 * @author: YangXin
 * @date: 2021/5/8
 */
data class IndustrialServiceOrderData(
  val id: Int,
  val orderNo: String,
  val type: Int,
  var typeName: String? = "",

  @SerializedName("type_id")
  val productType: Int,

  @SerializedName("typeidName")
  var productTypeName: String? = "",

  @SerializedName("dgcount")
  val quantity: Int,

  @SerializedName("ispay")
  val payStatus: Int,

  @SerializedName("ispayName")
  var payStatusName: String? = "",

  @SerializedName("cpid")
  val productId: Int,

  @SerializedName("cpids")
  var encryptProductId: String = "",

  @SerializedName("cpName")
  var productName: String? = "",

  @SerializedName("cpPic")
  var productImg: String? = "",

  @SerializedName("cpModel")
  var productModel: String? = "",

  @SerializedName("attributes")
  var productAttribute: String? = "",

  @SerializedName("yp_id")
  val businessId: Int,

  @SerializedName("yp_ids")
  var encryptBusinessId: String = "",

  @SerializedName("ypids")
  var encryptBusinessUserId: String = "",

  @SerializedName("logo")
  var businessLogo: String? = "",

  @SerializedName("coname")
  var businessName: String? = "",

  @SerializedName("realprice")
  var price: String? = "",

  @Bindable
  var productScore: Int,

  @SerializedName("payTypeName")
  var paymentMethod: String? = "",
  var time: String? = "",
  var payTime: String? = "",

  @SerializedName("phone")
  var servicePhone: String? = ""
) : BaseObservable(),Parcelable {

  constructor(parcel: Parcel) : this(
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readString().getOrDefault(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString()
  ) {
  }

  fun getFixBusinessLogo(): String {
    return businessLogo.getOrDefault().convertUrlToHttp()
  }

  fun getFixProductImg(): String {
    return productImg.getOrDefault().convertUrlToHttp()
  }

  @Bindable
  fun getCanReview(): Boolean {
    return (payStatus == -1 || payStatus == 1) && (productScore != 2)
  }

  fun addCommentSuccess() {
    productScore += 1
  }

  fun updateCommentStatus(productScore: Int) {
    this.productScore = productScore
    notifyPropertyChanged(BR.canReview)
    notifyPropertyChanged(BR.productScore)
  }

  fun paid(): Boolean {
    return payStatus == 1
  }

  override fun equals(other: Any?): Boolean {
    if (this === other) return true
    if (javaClass != other?.javaClass) return false

    other as IndustrialServiceOrderData

    if (id != other.id) return false

    return true
  }

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(id)
    parcel.writeString(orderNo)
    parcel.writeInt(type)
    parcel.writeString(typeName)
    parcel.writeInt(productType)
    parcel.writeString(productTypeName)
    parcel.writeInt(quantity)
    parcel.writeInt(payStatus)
    parcel.writeString(payStatusName)
    parcel.writeInt(productId)
    parcel.writeString(encryptProductId)
    parcel.writeString(productName)
    parcel.writeString(productImg)
    parcel.writeString(productModel)
    parcel.writeString(productAttribute)
    parcel.writeInt(businessId)
    parcel.writeString(encryptBusinessId)
    parcel.writeString(encryptBusinessUserId)
    parcel.writeString(businessLogo)
    parcel.writeString(businessName)
    parcel.writeString(price)
    parcel.writeInt(productScore)
    parcel.writeString(paymentMethod)
    parcel.writeString(time)
    parcel.writeString(payTime)
    parcel.writeString(servicePhone)
  }

  override fun describeContents(): Int {
    return 0
  }

  companion object CREATOR : Creator<IndustrialServiceOrderData> {
    override fun createFromParcel(parcel: Parcel): IndustrialServiceOrderData {
      return IndustrialServiceOrderData(parcel)
    }

    override fun newArray(size: Int): Array<IndustrialServiceOrderData?> {
      return arrayOfNulls(size)
    }
  }

}