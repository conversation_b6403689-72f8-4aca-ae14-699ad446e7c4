package com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.common.widget.dialog.ActionDialog.Builder
import com.bxkj.common.widget.dialog.selection.SelectionDialog
import com.bxkj.common.widget.popup.OptionsPickerPopup
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.string
import com.bxkj.jrzp.user.databinding.UserActivitySchoolInfoBinding
import com.bxkj.jrzp.user.schoolinfo.data.SchoolInfoData
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationNavigation
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route

/**
 * @Description: 学校信息
 * @author: YangXin
 * @date: 2020/11/27
 * @version: V1.0
 */
@Route(path = SchoolInfoNavigation.PATH)
class SchoolInfoActivity : BaseDBActivity<UserActivitySchoolInfoBinding, SchoolInfoViewModel>(),
  OnClickListener {

  companion object {
    const val TO_EDIT_SCHOOL_DESC_CODE = 1
    const val TO_SELECT_DETAILS_ADDRESS_CODE = 2
  }

  override fun getViewModelClass(): Class<SchoolInfoViewModel> = SchoolInfoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_school_info

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    initView()

    subscribeViewModelEvent()
  }

  private fun initView() {
    when (getIntentNextStep()) {
      SchoolInfoNavigation.NEXT_STEP_BACK -> {
        viewBinding.tvConfirm.setText(R.string.user_school_info_save)
      }

      SchoolInfoNavigation.NEXT_STEP_AUTH -> {
        viewBinding.tvConfirm.setText(R.string.user_school_info_next_step)
      }
    }

    if (getIntentShowIdentity()) {
      viewBinding.titleBar.setRightText(getString(R.string.mine_switch_identity))
      viewBinding.titleBar.setRightOptionClickListener {
        Builder()
          .setTitle(getString(string.common_tips))
          .setContent(getString(string.user_school_info_cancel_tips))
          .setOnConfirmClickListener {
            UserUtils.setupUserRole(AuthenticationType.PERSONAL)
            finish()
          }.build().show(supportFragmentManager)
      }
    }
  }

  private fun getIntentShowIdentity(): Boolean {
    return intent.getBooleanExtra(SchoolInfoNavigation.EXTRA_SHOW_SWITCH_IDENTITY, false)
  }

  private fun getIntentNextStep(): Int {
    return intent.getIntExtra(
      SchoolInfoNavigation.EXTRA_NEXT_STEP,
      SchoolInfoNavigation.NEXT_STEP_BACK
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.toSelectDetailsAddressCommand.observe(this, Observer {
      // SelectAddressNavigation.navigate(
      //   false,
      //   it.getAddressData(),
      //   it.dizhi.getOrDefault(),
      //   3
      // ).startForResult(this, TO_SELECT_DETAILS_ADDRESS_CODE)
    })

    viewModel.submitSuccessEvent.observe(this, Observer {
      showToast(string.user_school_info_submit_success)
      startNextStep(it)
    })

    viewModel.showSchoolNaturesSelectDialogCommand.observe(this, Observer {
      SelectionDialog.Builder(this)
        .setTitle(getString(R.string.user_school_info_natures_select_title))
        .setItems(getResArray(R.array.user_school_natures))
        .setSelectedItems(it.toTypedArray())
        .setOnItemClickListener { selectionDialog, position ->
          val selectItem = selectionDialog.data[position].name
          viewModel.switchSchoolNatureSelectStatus(selectItem)
        }.setOnConfirmListener { dialog, _ ->
          dialog.dismiss()
          viewModel.applySchoolNaturesChange()
        }.build().show()
    })

    viewModel.showSchoolTypePickerCommand.observe(this, Observer {
      OptionsPickerPopup(this)
        .setFirstOptions(it)
        .setOnConfirmClickListener { firstPosition, _, _ ->
          viewModel.setupSelectSchoolType(it[firstPosition])
        }.show()
    })

    viewModel.editSchoolDescCommand.observe(this, Observer {
      EditInfoNavigation.navigate(
        getString(R.string.user_school_info_desc),
        getString(R.string.please_enter),
        it
      ).startForResult(this, TO_EDIT_SCHOOL_DESC_CODE)
    })
  }

  private fun startNextStep(schoolInfo: SchoolInfoData) {
    when (getIntentNextStep()) {
      SchoolInfoNavigation.NEXT_STEP_AUTH -> {
        AuthenticationNavigation.navigate(schoolInfo.name.getOrDefault(), AuthenticationType.SCHOOL)
          .start()
        finish()
      }

      else -> {
        setResult(Activity.RESULT_OK)
        finish()
      }
    }
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_logo -> {
          PermissionUtils.requestPermission(
            this,
            getString(R.string.permission_tips_title),
            getString(R.string.permission_select_img_tips),
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                PictureSelector.create(this@SchoolInfoActivity)
                  .openGallery(SelectMimeType.ofImage())
                  .setImageEngine(GlideEngine.getInstance())
                  .setSandboxFileEngine(SandboxFileEngine.getInstance())
                  .setCompressEngine(ImageCompressEngine.getInstance())
                  .setSelectionMode(SelectModeConfig.SINGLE)
                  .isDirectReturnSingle(true)
                  .forResult(PictureConfig.CHOOSE_REQUEST)
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel_operation))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE
          )
        }
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }
}