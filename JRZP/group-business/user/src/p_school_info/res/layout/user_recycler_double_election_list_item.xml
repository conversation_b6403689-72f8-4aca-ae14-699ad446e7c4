<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.user.schoolinfo.data.DoubleElectionData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_14">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.NormalInfoTitle"
      android:layout_width="@dimen/dp_0"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.title}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.Tertiary"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.date}"
      app:layout_constraintEnd_toStartOf="@id/tv_views"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintHorizontal_chainStyle="packed"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_goneMarginBottom="@dimen/dp_16" />

    <TextView
      android:id="@id/tv_views"
      style="@style/Text.Tertiary"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{@string/user_double_elections_view_count(data.count)}"
      android:visibility="@{data.reviewSuccess()?View.VISIBLE:View.GONE}"
      app:layout_constrainedWidth="true"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_date"
      app:layout_constraintEnd_toStartOf="@id/tv_state"
      app:layout_constraintStart_toEndOf="@id/tv_date" />

    <TextView
      android:id="@+id/tv_state"
      style="@style/common_Text.12sp.ff4040"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{data.stateText}"
      android:visibility="@{data.reviewSuccess()?View.GONE:View.VISIBLE}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_views"
      app:layout_constraintEnd_toStartOf="@id/tv_edit"
      app:layout_constraintStart_toEndOf="@id/tv_views"
      app:layout_goneMarginStart="@dimen/dp_8" />

    <TextView
      style="@style/Text.14sp.333333"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{@{HtmlUtils.fromHtml(@string/user_double_elections_sign_up_count(data.bmCount))}}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_date" />

    <TextView
      android:id="@+id/tv_edit"
      style="@style/Text.Options"
      android:layout_marginEnd="@dimen/dp_8"
      android:text="@string/user_double_elections_edit"
      android:visibility="@{data.expired()?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_delete"
      app:layout_constraintEnd_toStartOf="@id/tv_delete" />

    <TextView
      android:id="@+id/tv_delete"
      style="@style/Text.Options"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_14"
      android:text="@string/user_double_elections_delete"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>