plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
  id 'com.google.devtools.ksp'
}

android {

  namespace "com.bxkj.learning"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()

    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  buildFeatures {
    dataBinding = true
  }
}

dependencies {
  implementation fileTree(dir: "libs", include: ["*.jar"])

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor

  includeApi(":group-business:user")

  implementation project(":lib-common")
  implementation project(":group-support:share")
}