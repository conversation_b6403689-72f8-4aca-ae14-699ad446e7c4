package com.bxkj.business.ui.serviceprovider

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.business.ui.courseinfogroup.LearningInfoChild
import com.bxkj.business.ui.serviceproviderhome.ServiceProviderHomeActivity
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.learning.data.ProductImgData
import com.bxkj.learning.data.ServiceProviderData
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningFragmentServiceProviderBinding

/**
 *
 * @author: YangXin
 * @date: 2021/4/22
 */
class ServiceProviderFragment :
  BaseDBFragment<LearningFragmentServiceProviderBinding, ServiceProviderViewModel>(),
  LearningInfoChild {

  companion object {

    const val EXTRA_INFO_TYPE = "INFO_TYPE"

    fun newInstance(infoType: Int): Fragment {
      return ServiceProviderFragment().apply {
        arguments = bundleOf(EXTRA_INFO_TYPE to infoType)
      }
    }
  }

  override fun getViewModelClass(): Class<ServiceProviderViewModel> =
    ServiceProviderViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_fragment_service_provider

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupServiceProviderListAdapter()

    arguments?.let {
      viewModel.start(it.getInt(EXTRA_INFO_TYPE))
    }
  }

  private fun setupServiceProviderListAdapter() {
    val serviceProviderListAdapter = object : SimpleDBListAdapter<ServiceProviderData>(
      parentActivity,
      R.layout.learning_recycler_service_provider_item
    ) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: ServiceProviderData,
        position: Int
      ) {
        holder.findViewById<RecyclerView>(R.id.list_product_photo).let {
          if (it.adapter == null) {
            val productImgListAdapter = SimpleDiffListAdapter<ProductImgData>(
              R.layout.learning_recycler_service_provider_img_item,
              ProductImgData.DiffCallback()
            )
            it.layoutManager = GridLayoutManager(parentActivity, 5)
            it.addItemDecoration(GridItemDecoration(getResDrawable(R.drawable.divider_8)))
            it.adapter = productImgListAdapter
          }
        }
        super.convert(holder, viewType, item, position)
      }
    }.apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          startActivity(
            ServiceProviderHomeActivity.newIntent(
              parentActivity,
              data[position].businessUserId,
              data[position].encryptBusinessUserId
            )
          )
        }
      })
    }
    viewBinding.includeServiceProviderList.recyclerContent.let {
      it.layoutManager = LinearLayoutManager(parentActivity)
      it.addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4))
          .margin(dip(12)).build()
      )
    }
    viewModel.serviceProviderListViewModel.setAdapter(serviceProviderListAdapter)
  }

  override fun startSearch(keyword: String) {
    viewModel.startSearch(keyword)
    viewBinding.includeServiceProviderList.recyclerContent.toPosition(0)
  }
}