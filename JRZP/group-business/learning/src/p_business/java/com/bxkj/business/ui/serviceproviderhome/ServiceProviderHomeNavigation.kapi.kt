package com.bxkj.business.ui.serviceproviderhome

import com.bxkj.learning.LearningConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator

/**
 *
 * @author: YangXin
 * @date: 2021/5/10
 */
class ServiceProviderHomeNavigation {

  companion object {

    const val PATH = "${LearningConstants.LEARNING_DIRECTORY}/serviceproviderhome"

    const val EXTRA_BUSINESS_ID = "MERCHANT_ID"
    const val EXTRA_ENCRYPT_BUSINESS_USER_ID = "ENCRYPT_BUSINESS_USER_ID"

    fun create(businessId: Int, encryptBusinessUserId: String): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_BUSINESS_ID, businessId)
        .withString(EXTRA_ENCRYPT_BUSINESS_USER_ID, encryptBusinessUserId)
    }
  }
}