package com.bxkj.business.ui.courselist

import com.bxkj.learning.LearningConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/24
 * @version: V1.0
 */
object CourseListNavigation {

  const val PATH = "${LearningConstants.LEARNING_DIRECTORY}/courseslist"

  fun navigate(): RouterNavigator {
    return Router.getInstance().to(PATH)
  }

}