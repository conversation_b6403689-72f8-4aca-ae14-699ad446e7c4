package com.bxkj.business

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description: 课程列表排序
 * @author:45457
 * @date: 2020/7/24
 * @version: V1.0
 */
class CoursesListSort {
  companion object {
    const val SORT_VIEW_COUNT = 1
    const val SORT_UPDATE_TIME = 2
  }

  @IntDef(
    SORT_VIEW_COUNT,
    SORT_UPDATE_TIME
  )
  @Retention(AnnotationRetention.SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Sort
}