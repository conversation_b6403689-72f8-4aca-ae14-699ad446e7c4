package com.bxkj.business.ui.courselist

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.business.CoursesListSort
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/24
 * @version: V1.0
 */
class CourseListViewModel @Inject constructor(
  private val mLearningRepository: LearningRepository
) : BaseViewModel() {

  val coursesListViewModel = RefreshListViewModel()

  init {
    setupCoursesListViewModel()
  }

  private fun setupCoursesListViewModel() {
    coursesListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mLearningRepository.getCoursesList(
          CoursesListSort.SORT_UPDATE_TIME,
          0,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          coursesListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            coursesListViewModel.noMoreData()
          } else {
            coursesListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start() {
    coursesListViewModel.refresh()
  }
}