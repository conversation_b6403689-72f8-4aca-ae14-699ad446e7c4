<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.business.ui.skillcourse.SkillCourseViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="horizontal">

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_course_type"
      android:layout_width="@dimen/dp_0"
      android:layout_height="match_parent"
      android:layout_weight="0.21"
      android:background="@drawable/bg_f4f4f4"
      android:visibility="@{viewModel.showTypeOptions?View.VISIBLE:View.GONE}"
      bind:items="@{viewModel.typeList}" />

    <LinearLayout
      android:layout_width="@dimen/dp_0"
      android:layout_height="match_parent"
      android:layout_weight="0.79"
      android:orientation="vertical">

      <LinearLayout
        style="@style/match_wrap"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_course_sub_type"
          android:layout_width="@dimen/dp_0"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="@dimen/dp_4"
          android:visibility="@{viewModel.showTypeOptions?View.VISIBLE:View.GONE}"
          bind:items="@{viewModel.subTypeList}" />

        <ImageView
          android:id="@+id/iv_all_sub_type"
          android:layout_width="@dimen/common_dp_32"
          android:layout_height="@dimen/common_dp_32"
          android:onClick="@{()->viewModel.showAllSubType()}"
          android:scaleType="center"
          android:src="@drawable/learning_ic_course_sub_type_more"
          android:visibility="@{viewModel.showTypeOptions&amp;&amp;viewModel.subTypeList.size()&gt;3?View.VISIBLE:View.GONE}" />
      </LinearLayout>

      <include
        android:id="@+id/include_course_list"
        layout="@layout/include_mvvm_refresh_layout"
        app:listViewModel="@{viewModel.courseListViewModel}" />
    </LinearLayout>

  </LinearLayout>
</layout>