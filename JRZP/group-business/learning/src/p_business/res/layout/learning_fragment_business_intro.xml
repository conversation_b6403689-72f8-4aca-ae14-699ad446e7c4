<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.business.ui.businessintro.BusinessIntroViewModel" />
  </data>

  <androidx.core.widget.NestedScrollView
    style="@style/match_wrap"
    android:background="@drawable/bg_f4f4f4">

    <LinearLayout
      style="@style/match_wrap"
      android:focusable="true"
      android:focusableInTouchMode="true"
      android:orientation="vertical"
      android:padding="@dimen/dp_12">

      <LinearLayout
        style="@style/match_wrap"
        android:background="@drawable/learning_bg_business_safe_tag"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_6">

        <ImageView
          style="@style/wrap_wrap"
          android:src="@drawable/learning_img_business_safe_tag" />

        <TextView
          style="@style/Text.12sp.FF7647"
          android:layout_marginTop="@dimen/dp_4"
          android:text="@string/learning_service_provider_safe_tag" />

      </LinearLayout>

      <LinearLayout
        style="@style/match_wrap"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_ffffff_radius_4"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <TextView
          style="@style/Text.16sp.333333.Bold"
          android:text="@string/learning_service_provider_about_us" />

        <com.bxkj.common.widget.expand.ExpandTextView
          style="@style/match_wrap"
          android:layout_marginTop="@dimen/dp_8"
          app:content="@{viewModel.businessIntro}"
          app:indicateImage="@drawable/common_ic_text_expand" />

      </LinearLayout>

      <LinearLayout
        style="@style/match_wrap"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_ffffff_radius_4"
        android:orientation="vertical"
        android:padding="@dimen/dp_10"
        android:visibility="@{viewModel.businessServiceTypeList!=null?View.VISIBLE:View.GONE}">

        <TextView
          style="@style/Text.16sp.333333.Bold"
          android:text="@string/learning_service_provider_skill" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/list_skill"
          style="@style/match_wrap"
          android:layout_marginTop="@dimen/dp_8"
          bind:items="@{viewModel.businessServiceTypeList}" />

      </LinearLayout>
    </LinearLayout>

  </androidx.core.widget.NestedScrollView>

</layout>