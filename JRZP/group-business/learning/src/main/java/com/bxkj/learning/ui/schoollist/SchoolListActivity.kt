package com.bxkj.learning.ui.schoollist

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.bxkj.learning.R
import com.bxkj.learning.databinding.ActivitySchoolListBinding
import com.bxkj.learning.data.SchoolItemData

/**
 * @Description:
 * @author:45457 技能提升列表
 * @date: 2020/7/24
 * @version: V1.0
 */
@Route(path = SchoolListNavigation.PATH)
class SchoolListActivity : BaseDBActivity<ActivitySchoolListBinding, SchoolListViewModel>() {
  override fun getViewModelClass(): Class<SchoolListViewModel> = SchoolListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_school_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupCoursesList()

    viewModel.start()
  }

  private fun setupCoursesList() {
    val coursesListAdapter =
      SimpleDBListAdapter<SchoolItemData>(this, R.layout.recycler_school_item)
        .apply {
          setOnItemClickListener(object : SuperItemClickListener {
            override fun onClick(v: View, position: Int) {
              afterLogin {
                LearningWebNavigation.navigate(
                  LearningWebNavigation.PAGE_TYPE_EDU,
                  data[position].id,
                  localUserId
                ).start()
              }
            }
          })
        }
    val coursesList = viewBinding.includeCourseList.recyclerContent
    coursesList.layoutManager = LinearLayoutManager(this)
    viewModel.schoolListViewModel.setAdapter(coursesListAdapter)
  }

}