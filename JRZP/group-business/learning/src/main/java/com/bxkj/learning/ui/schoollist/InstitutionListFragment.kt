package com.bxkj.learning.ui.schoollist

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.business.ui.courseinfogroup.LearningInfoChild
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningFragmentInstitutionListBinding
import com.bxkj.learning.data.InstitutionData

/**
 * 学校\机构列表
 * @author: YangXin
 * @date: 2021/4/17
 */
class InstitutionListFragment :
  BaseDBFragment<LearningFragmentInstitutionListBinding, InstitutionListViewModel>(),
  LearningInfoChild {

  companion object {
    fun newInstance(): Fragment {
      return InstitutionListFragment()
    }
  }

  override fun getViewModelClass(): Class<InstitutionListViewModel> =
    InstitutionListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_fragment_institution_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupInstitutionListAdapter()

    viewModel.start()
  }

  private fun setupInstitutionListAdapter() {
    viewBinding.includeInstitutionList.recyclerContent.layoutManager =
      LinearLayoutManager(parentActivity)
    viewBinding.includeInstitutionList.recyclerContent.addItemDecoration(
      LineItemDecoration.Builder()
        .divider(getResDrawable(R.drawable.divider_f4f4f4))
        .orientation(LinearLayoutManager.VERTICAL)
        .marginStart(dip(12))
        .marginEnd(dip(12))
        .build()
    )

    val institutionListAdapter =
      SimpleDBListAdapter<InstitutionData>(
        parentActivity,
        R.layout.learning_recycler_institution_item
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data[position].let {
              if (v.id == R.id.tv_follow) {
                afterLogin {
                  viewModel.followUser(it)
                }
              } else {
                UserHomeNavigation.navigate(it.uid).start()
              }
            }
          }
        }, R.id.tv_follow)
      }

    viewModel.institutionListViewModel.setAdapter(institutionListAdapter)
  }

  override fun startSearch(keyword: String) {
    viewModel.startSearch(keyword)
    viewBinding.includeInstitutionList.recyclerContent.toPosition(0)
  }
}