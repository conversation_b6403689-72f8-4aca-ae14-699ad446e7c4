package com.bxkj.learning.api

import com.bxkj.learning.data.CourseData
import com.bxkj.learning.data.CoursesShareInfoData
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.learning.data.UserPublishLearningInfoData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.business.data.InfoTypeData
import com.bxkj.learning.data.IndustrialServicesData
import com.bxkj.learning.data.ServiceAttributeData
import com.bxkj.learning.data.ServiceCommentData
import com.bxkj.learning.data.ServiceProviderData
import com.bxkj.learning.data.InstitutionData
import com.bxkj.learning.data.SchoolItemData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Project: gzgk
 * @Description: 视频API
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */

interface LearningApi {

  @POST(LearningApiConstants.I_GET_COURSES_LIST)
  suspend fun getCoursesList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<CourseData>>

  @POST(LearningApiConstants.I_GET_COURSE_DETAILS)
  suspend fun getCourseDetails(@Body mRequestBody: ZPRequestBody): BaseResponse<CourseData>

  @POST(LearningApiConstants.I_GET_COURSE_SHARE_INFO)
  suspend fun getCourseShareInfo(@Body mRequestBody: ZPRequestBody): BaseResponse<CoursesShareInfoData>

  @POST(LearningApiConstants.I_GET_CONTINUING_EDUCATION_SHARE_INFO)
  suspend fun getContinuingEducationShareInfo(@Body mRequestBody: ZPRequestBody): BaseResponse<CoursesShareInfoData>

  @POST(LearningApiConstants.I_GET_SCHOOL_LIST)
  suspend fun getSchoolList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<SchoolItemData>>

  @POST(LearningApiConstants.I_GET_COURSE_TYPE_LIST)
  suspend fun getCourseTypeList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<InfoTypeData>>

  @POST(LearningApiConstants.I_GET_INSTITUTION_LIST)
  suspend fun getInstitutionList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<InstitutionData>>

  @POST(LearningApiConstants.I_GET_USER_COURSE_LIST)
  suspend fun getUserLearningInfoList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<UserPublishLearningInfoData>>

  @POST(LearningApiConstants.I_GET_INDUSTRIAL_SERVICE_LIST)
  suspend fun getIndustrialServiceList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<IndustrialServicesData>>

  @POST(LearningApiConstants.I_GET_INDUSTRIAL_SERVICE_TYPE_LIST)
  suspend fun getIndustrialServiceTypeList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<InfoTypeData>>

  @POST(LearningApiConstants.I_GET_SERVICE_PROVIDER_LIST)
  suspend fun getServiceProviderList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<ServiceProviderData>>

  @POST(LearningApiConstants.I_GET_INDUSTRIAL_SERVICE_DETAILS)
  suspend fun getIndustrialServiceDetails(
    @Body mRequestBody: ZPRequestBody
  ): BaseResponse<IndustrialServicesData>

  @POST(LearningApiConstants.I_GET_RECOMMEND_SERVICE_LIST)
  suspend fun getRecommendServiceList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<IndustrialServicesData>>

  @POST(LearningApiConstants.I_GET_SERVICE_COMMENT_LIST)
  suspend fun getServiceCommentList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<ServiceCommentData>>

  @POST(LearningApiConstants.I_GET_INDUSTRIAL_SERVICE_ATTRIBUTE)
  suspend fun getIndustrialServiceAttribute(@Body mRequestBody: ZPRequestBody): BaseResponse<List<ServiceAttributeData>>

  @POST(LearningApiConstants.I_CREATE_INDUSTRIAL_SERVICE_ORDER)
  suspend fun createIndustrialServiceOrder(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(LearningApiConstants.I_GET_SERVICE_PROVIDER_DETAILS)
  suspend fun getServiceProviderDetails(@Body encryptReqParams: EncryptReqParams): BaseResponse<ServiceProviderData>

  @POST(LearningApiConstants.I_GET_BUSINESS_SERVICE_TYPE_LIST)
  suspend fun getBusinessServiceList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<InfoTypeData>>
}