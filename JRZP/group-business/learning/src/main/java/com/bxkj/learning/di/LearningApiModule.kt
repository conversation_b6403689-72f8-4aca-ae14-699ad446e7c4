package com.bxkj.learning.di

import com.bxkj.learning.api.LearningApi
import dagger.Module
import dagger.Provides
import retrofit2.Retrofit

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/20
 * @version: V1.0
 */
@Module
class LearningApiModule {

  @Provides
  fun provideLearningApi(retrofit: Retrofit): LearningApi {
    return retrofit.create(LearningApi::class.java)
  }

}