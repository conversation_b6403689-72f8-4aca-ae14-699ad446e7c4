<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.learning.ui.schoollist.SchoolListViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      app:title="@string/learning_school_list_page_title" />

    <include
      android:id="@+id/include_course_list"
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.schoolListViewModel}" />

  </LinearLayout>
</layout>