package com.bxkj.jrzp.live.api

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.live.room.data.LiveRoomData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/2
 * @version: V1.0
 */
class OpenLiveRepository @Inject constructor(
  private val mOpenLiveApi: OpenLiveApi
) : BaseRepo() {

  /**
   * 获取用户直播列表
   */
  suspend fun getUserLiveList(userID: Int, userType: Int): ReqResponse<List<LiveRoomData>> {
    return httpRequest {
      mOpenLiveApi.getUserLiveList(
        ZPRequestBody().apply {
          put("uid", userID)
          put("type", userType)
        }.paramsEncrypt()
      )
    }
  }
}