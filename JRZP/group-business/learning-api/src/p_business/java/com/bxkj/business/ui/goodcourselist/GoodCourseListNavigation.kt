package com.bxkj.business.ui.goodcourselist

import com.bxkj.learning.LearningConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator

/**
 *
 * @author: YangXin
 * @date: 2021/4/16
 */
class GoodCourseListNavigation {

  companion object {
    const val PATH = "${LearningConstants.LEARNING_DIRECTORY}/goodcourselist"

    const val EXTRA_TARGET_PAGE_INDEX = "TARGET_PAGE_INDEX"

    fun create(targetPageType: Int = 0): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_TARGET_PAGE_INDEX, targetPageType)
    }
  }
}