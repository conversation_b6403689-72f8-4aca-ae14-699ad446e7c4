package com.bxkj.learning.data

import android.text.Spanned
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.HtmlUtils

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/23
 * @version: V1.0
 */
data class CourseData(
  var id: Int,
  var name: String = "",
  var photourl: String = "",
  var kkDateShow: String = "",
  var pxAddress: String = "",
  var labelName: String = "",
  var price: Float = 0f,
  var priceArea: String = "",
  var intro: String = "",
  var kzbmEDate: String = "",
  var kzDateStr: String = "",
  var jibieList: List<LevelData>? = null,
  var maxYongjinPrice: Float = 0f,
  var buyCount: Int,
  var haopinglv: String,
  var ziyingState: Int,
  var maxPintuanChajia: Float = 0f,
  var online: Int,
  var state: Int
) {
  class DiffCallback : DiffUtil.ItemCallback<CourseData>() {
    override fun areItemsTheSame(
      old: CourseData,
      aNew: CourseData
    ): Boolean {
      return old == aNew
    }

    override fun areContentsTheSame(
      old: CourseData,
      aNew: CourseData
    ): Boolean {
      return old.id == aNew.id
    }
  }

  fun getInfoTags(): List<String>? {
    if (CheckUtils.isNullOrEmpty(labelName)) {
      return null
    }
    return labelName.split(",")
  }

  data class LevelData(
    var id: Int = 0,
    var name: String = CommonApiConstants.NO_TEXT,
    var price: Float = 0f,
    var areaList: List<AreaOptionsData>? = null
  )

  fun hasCommission(): Boolean {
    return maxYongjinPrice > 0
  }

  fun formatPriceText(): Spanned {
    return HtmlUtils.fromHtml(String.format("<small>￥</small>%.2f", price))
  }

  fun isSelfProduct(): Boolean {
    return ziyingState == 1
  }

  fun hasDiscount(): Boolean {
    return maxPintuanChajia > 0
  }

  fun offline(): Boolean {
    return online == 0
  }

  fun auditApproved(): Boolean {
    return state == 1
  }
}