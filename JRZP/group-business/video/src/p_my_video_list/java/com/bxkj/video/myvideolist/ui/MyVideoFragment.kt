package com.bxkj.video.myvideolist.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.HasCheckBoxListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder.OnItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.jrzp.user.videorelate.ui.MyVideoRelateNavigation
import com.bxkj.personal.ui.activity.myhistory.EditList
import com.bxkj.personal.ui.activity.myhistory.HasEditListGroup
import com.bxkj.video.BR
import com.bxkj.video.R
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.databinding.VideoFragmentMyVideoListBinding

/**
 * @Description: 我的视频
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
@Route(path = MyVideoListNavigation.PATH)
class MyVideoFragment : BaseDBFragment<VideoFragmentMyVideoListBinding, MyVideoViewModel>(),
    EditList {

    companion object {

        const val TO_VIDEO_RELATE_CODE = 1

        fun newInstance(): Fragment {
            return MyVideoFragment()
        }
    }

    private var mVideoListAdapter: HasCheckBoxListAdapter? = null

    override fun getViewModelClass(): Class<MyVideoViewModel> = MyVideoViewModel::class.java

    override fun getLayoutId(): Int = R.layout.video_fragment_my_video_list

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        subscribeViewModelEvent()

        setupVideoListAdapter()

        subscribePostVideoSuccessEvent()

        viewModel.start(arguments?.getInt(MyVideoListNavigation.EXTRA_USER_TYPE))
    }

    private fun subscribePostVideoSuccessEvent() {
        addDisposable(
            RxBus.get().toObservable(RxBus.Message::class.java)
                .subscribe {
                    if (it.code == RxMsgCode.ACTION_PUBLISH_VIDEO_SUCCESS) {
                        viewModel.refresh()
                    }
                }
        )
    }

    private fun subscribeViewModelEvent() {
        viewModel.deleteSuccessEvent.observe(this, Observer {
            mVideoListAdapter?.clearChecked()
        })

        viewModel.deleteToEmptyEvent.observe(this, Observer {
            (this.parentActivity as HasEditListGroup).closeEdit()
        })

        viewModel.showClearConfirmEvent.observe(this, Observer {
            ActionDialog.Builder()
                .setTitle(getString(R.string.tips))
                .setContent(getString(R.string.my_history_clear_tips))
                .setOnConfirmClickListener {
                    (this.parentActivity as HasEditListGroup).closeEdit()
                    viewModel.removeAllVideo()
                }.build().show(childFragmentManager)
        })


        viewModel.enableEditCommand.observe(this) {
            arguments?.getInt(MyVideoListNavigation.EXTRA_USER_TYPE)?.let { videoType ->
                (parentActivity as HasEditListGroup).enableEdit(videoType, it)
            }
        }
    }

    private fun setupVideoListAdapter() {
        mVideoListAdapter = HasCheckBoxListAdapter(parentActivity, R.id.iv_checked).apply {
            register(
                OnlineVideoData::class.java,
                DefaultViewBinder<OnlineVideoData>(
                    R.layout.video_recycler_my_video_item,
                    BR.data,
                    true
                ).apply {
                    setOnItemClickListener(object : OnItemClickListener<OnlineVideoData> {
                        override fun onItemClicked(
                            v: View,
                            position: Int,
                            item: OnlineVideoData,
                        ) {
                            MyVideoRelateNavigation.navigate(
                                item,
                                arguments?.getInt(MyVideoListNavigation.EXTRA_USER_TYPE)
                                    ?: AppConstants.USER_TYPE_PERSONAL
                            ).startForResult(this@MyVideoFragment, TO_VIDEO_RELATE_CODE)
                        }
                    })
                }
            )
            setOnCheckedItemChangeListener(object :
                HasCheckBoxListAdapter.OnCheckedItemChangeListener {
                override fun onCheckedItemChange(items: List<Int>) {
                    viewModel.setCheckedItem(items)
                }
            })
        }

        val myVideoList = viewBinding.includeVideoList.recyclerContent

        myVideoList.layoutManager = GridLayoutManager(parentActivity, 3)
        myVideoList.addItemDecoration(
            GridItemDecoration(
                ContextCompat.getDrawable(
                    parentActivity,
                    R.drawable.divider_ffffff_4
                )
            )
        )

        viewModel.myVideoListViewModel.setAdapter(mVideoListAdapter)
    }

    override fun switchEditState(state: Boolean) {
        if (isVisible) {
            viewModel.openOrCloseDeleteBar(state)
            mVideoListAdapter?.setShowCheckbox(state)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == TO_VIDEO_RELATE_CODE && resultCode == MyVideoRelateNavigation.RESULT_VIDEO_DELETED) {
            viewBinding.includeVideoList.recyclerContent.toPosition(0)
            viewModel.refresh()
        }
    }

}