package com.bxkj.video.openapi

/**
 * @Project: gzgk
 * @Description: 视频接口常量
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
class VideoApiConstants {
  companion object {
    // 删除视频
    const val I_DELETE_VIDEO = "/Video/DeleteXspVideo/"

    // 获取视频列表
    const val I_GET_VIDEO_LIST = "/Video/GetJrzpAppXspVideoListByPageU/"

    // 视频招聘报名
    const val I_VIDEO_RECRUIT_SIGN_UP = "/Video/VideoSignUp/"

    // 获取用户报名信息
    const val I_GET_USER_SIGN_IN_INFO = "/User/GetUserForVideo/"

    // ************************************举报*************************************//

    // 举报
    const val I_REPORT_INFO = "/Report/Report/"
  }
}
