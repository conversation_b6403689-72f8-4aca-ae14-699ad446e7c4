package com.bxkj.staggeredlist.ui.list

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshLayoutViewModel.OnLayoutActionListenerAdapter
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.jrzp.live.room.repository.OpenLiveRoomRepository
import com.bxkj.staggeredlist.VideoListType
import com.bxkj.video.R
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.message.VideoLoadResultMessage
import com.bxkj.video.repository.VideoRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/17
 * @version: V1.0
 */
class StaggeredVideoListViewModel @Inject constructor(
  private val mVideoRepository: VideoRepository,
  private val mOpenLiveRoomRepository: OpenLiveRoomRepository,
) : BaseViewModel() {

  val videoListViewModel = RefreshListViewModel()

  private var recommendVideoIds: List<String>? = null

  //不存在直播
  private var noLive: Boolean = false

  private var forceRefreshNormalVideo: Boolean = false

  //直播视频结束页码
  private var liveVideoEndPage: Int = 0

  private var videoListType: Int = VideoListType.RECOMMEND

  private var searchKeyword: String = ""

  private val mVideoList = ArrayList<OnlineVideoData>()

  init {
    videoListViewModel.refreshLayoutViewModel.setOnLayoutActionListener(object :
      OnLayoutActionListenerAdapter() {
      override fun onRefresh() {
        liveVideoEndPage = 0
        noLive = false
      }
    })

    videoListViewModel.setOnLoadDataListener { currentPage: Int ->
      if (noLive) {
        getNormalVideoList(currentPage)
        return@setOnLoadDataListener
      }

      if (videoListType != VideoListType.RECOMMEND) {
        forceRefreshNormalVideo = true
        switchLoadNormalVideo(currentPage)
        return@setOnLoadDataListener
      }

      viewModelScope.launch {
        mOpenLiveRoomRepository.getLiveRoomList(currentPage, 16)
          .handleResult({
            if (it.isNullOrEmpty()) {
              switchLoadNormalVideo(currentPage)
            } else {
              videoListViewModel.autoAddAll(it)
              if (it.size < CommonApiConstants.DEFAULT_PAGE_SIZE) {
                switchLoadNormalVideo(currentPage)
              }
            }
          }, {
            if (currentPage == 1) {
              forceRefreshNormalVideo = true
            }
            switchLoadNormalVideo(currentPage)
          })
      }
    }
  }

  private fun switchLoadNormalVideo(currentPage: Int) {
    mVideoList.clear()
    noLive = true
    liveVideoEndPage = currentPage
    videoListViewModel.reRequest()
  }

  private fun getNormalVideoList(currentPage: Int) {
    val realPage = currentPage - liveVideoEndPage + 1
    var recommendVideoId = 0
    recommendVideoIds?.let { ids ->
      if (currentPage < ids.size) {
        if (ids[currentPage].isNotEmpty()) {
          recommendVideoId = ids[currentPage].toInt()
        }
      }
    }
    viewModelScope.launch {
      mVideoRepository.getVideoList(
        getSelfUserID(),
        videoListType,
        realPage,
        16,
        recommendVideoId,
        UserUtils.getUserSelectedCityId(),
        searchKeyword
      ).handleResult({
        it?.let {
          if (realPage == 1) {
            recommendVideoIds = it.videoID?.split(",")
          }

          if (forceRefreshNormalVideo) {
            videoListViewModel.reset(it.dataList)
            forceRefreshNormalVideo = false
          } else {
            videoListViewModel.addAll(it.dataList)
          }

          mVideoList.addAll(it.dataList)
          sendVideoListLoadRestMsg(VideoLoadResultMessage.success(it.dataList))
        }
      }, {
        if (it.isNoDataError) {
          videoListViewModel.noMoreData()
          sendVideoListLoadRestMsg(VideoLoadResultMessage.noMore())
        } else {
          videoListViewModel.loadError()
          sendVideoListLoadRestMsg(VideoLoadResultMessage.error())
        }
      })
    }
  }

  fun start(videoType: Int) {
    videoListType = videoType
    videoListViewModel.refresh()
  }

  fun refreshData() {
    forceRefreshNormalVideo = true
    videoListViewModel.refresh(true)
  }

  fun loadMoreVideo() {
    videoListViewModel.loadMore()
  }

  fun getVideoList(): List<OnlineVideoData> {
    return mVideoList
  }

  /**
   * 发送视频加载结果消息
   */
  private fun sendVideoListLoadRestMsg(msg: VideoLoadResultMessage) {
    RxBus.get().post(msg)
  }

  fun startSearch(keyword: String) {
    searchKeyword = keyword
    refreshData()
  }
}