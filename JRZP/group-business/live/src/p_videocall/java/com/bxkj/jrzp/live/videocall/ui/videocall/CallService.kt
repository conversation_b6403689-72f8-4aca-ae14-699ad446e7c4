package com.bxkj.jrzp.live.videocall.ui.videocall

import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat.Builder
import androidx.core.app.NotificationManagerCompat
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.AppStatusManager
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.user.mine.data.UserHomeData
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.api.OpenUserHomeRepository
import com.tencent.qcloud.ugckit.trtc.model.TRTCCalling
import com.tencent.qcloud.ugckit.trtc.model.TRTCCallingDelegate
import com.tencent.qcloud.ugckit.trtc.model.TRTCCallingImpl
import dagger.android.AndroidInjection
import dagger.android.AndroidInjector
import dagger.android.DispatchingAndroidInjector
import dagger.android.HasAndroidInjector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/3/16
 */
class CallService : Service(), HasAndroidInjector {

  companion object {

    const val CALL_NOTIFICATION_ID = 1

    const val EXTRA_USER_ID = "USER_ID"
    const val EXTRA_USER_SIG_ID = "USER_SIG_ID"

    fun start(context: Context) {
      if (SystemUtil.isServiceRunning(context, CallService::class.java.name)) {
        return
      }
      val starter = Intent(context, CallService::class.java)
      context.startService(starter)
    }

    fun stop(context: Context) {
      val intent = Intent(context, CallService::class.java)
      context.stopService(intent)
    }
  }

  @Inject
  lateinit var androidInjector: DispatchingAndroidInjector<Any>

  @Inject
  lateinit var openUserHomeRepository: OpenUserHomeRepository

  private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

  private var trtcCalling: TRTCCalling? = null

  private var selfUserId: Int = 0

  private val mTRTCCallingDelegate = object : TRTCCallingDelegate {
    override fun onError(code: Int, msg: String?) {
    }

    override fun onInvited(
      sponsor: String?, userIdList: MutableList<String>?, isFromGroup: Boolean, callType: Int
    ) {
      serviceScope.launch {
        if (callType == TRTCCalling.TYPE_VIDEO_CALL) {
          sponsor?.let { sponsorId ->
            serviceScope.launch {
              openUserHomeRepository.getUserHomePageInfo(
                sponsorId.toInt(),
                AuthenticationType.QUERY_HIGHER_AUTH,
                sponsorId.toInt()
              ).handleResult({ userInfo ->
                userInfo?.let {
                  //APP在前台展示
                  if (AppStatusManager.isForeground()) {
                    VideoCallNavigation.create(
                      VideoCallNavigation.TYPE_BEING_CALLED,
                      selfUserId,
                      VideoCallUserInfo(
                        userInfo.userID,
                        userInfo.name,
                        userInfo.photo
                      )
                    ).start(this@CallService)
                  } else {  //APP在后台运行
                    val notification =
                      Builder(
                        this@CallService,
                        AppConstants.NOTIFICATION_CHANNEL_ID_PUSH
                      )
                        .setSmallIcon(R.mipmap.ic_launcher)
                        .setContentTitle("视频面试")
                        .setContentText("${userInfo.name}邀请您进行视频面试")
                        .setWhen(System.currentTimeMillis())
                        .setContentIntent(
                          getNotificationClickedIntent(
                            userInfo
                          )
                        )
                        .setAutoCancel(true)
                        .build()
                    NotificationManagerCompat.from(this@CallService)
                      .notify(CALL_NOTIFICATION_ID, notification)
                  }
                }
              })
            }
          }
        }
      }
    }

    override fun onGroupCallInviteeListUpdate(userIdList: MutableList<String>?) {
    }

    override fun onUserEnter(userId: String?) {
    }

    override fun onUserLeave(userId: String?) {
    }

    override fun onReject(userId: String?) {
    }

    override fun onNoResp(userId: String?) {
    }

    override fun onLineBusy(userId: String?) {
    }

    override fun onCallingCancel() {
      NotificationManagerCompat.from(this@CallService).cancel(CALL_NOTIFICATION_ID)
    }

    override fun onCallingTimeout() {
      NotificationManagerCompat.from(this@CallService).cancel(CALL_NOTIFICATION_ID)
    }

    override fun onCallEnd() {
      NotificationManagerCompat.from(this@CallService).cancel(CALL_NOTIFICATION_ID)
    }

    override fun onUserVideoAvailable(userId: String?, isVideoAvailable: Boolean) {
    }

    override fun onUserAudioAvailable(userId: String?, isVideoAvailable: Boolean) {
    }

    override fun onUserVoiceVolume(volumeMap: MutableMap<String, Int>?) {
    }
  }

  private fun getNotificationClickedIntent(userInfo: UserHomeData): PendingIntent? {
    return PendingIntent.getActivity(
      this, 1, VideoCallNavigation.create(
        VideoCallNavigation.TYPE_BEING_CALLED,
        selfUserId,
        VideoCallUserInfo(userInfo.userID, userInfo.name, userInfo.photo)
      ).createIntent(this), PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
    )
  }

  override fun onCreate() {
    AndroidInjection.inject(this)
    super.onCreate()
  }

  override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
    intent?.let {
      selfUserId = it.getIntExtra(EXTRA_USER_ID, 0)
      val sigID = it.getStringExtra(EXTRA_USER_SIG_ID).getOrDefault()
      registerTencentTRTCService(selfUserId, sigID)
    }
    return super.onStartCommand(intent, flags, startId)
  }

  private fun registerTencentTRTCService(selfUserId: Int, sig: String) {
    trtcCalling = TRTCCallingImpl.sharedInstance(this).apply {
      addDelegate(mTRTCCallingDelegate)
      login(AppConstants.TENCENT_LIVE_APP_ID.toInt(), selfUserId.toString(), sig, null)
    }
  }

  override fun androidInjector(): AndroidInjector<Any> {
    return androidInjector
  }

  override fun onBind(intent: Intent?): IBinder? {
    throw UnsupportedOperationException("Not yet implemented")
  }

  override fun onDestroy() {
    super.onDestroy()
    serviceScope.cancel()
    TRTCCallingImpl.destroySharedInstance()
  }
}