<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.bxkj.jrzp.live" >

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <uses-feature android:name="android.hardware.Camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <permission android:name="android.permission.BIND_JOB_SERVICE" />

    <uses-permission android:name="android.permission.BLUETOOTH" />

    <application
        android:allowBackup="false"
        tools:replace="android:allowBackup" >
        <activity
            android:name="com.bxkj.jrzp.live.anchor.ui.anchor.LiveAnchorActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait"
            android:theme="@style/Live_AnchorActivityTheme" />
        <activity
            android:name="com.bxkj.jrzp.live.room.ui.roomlist.LiveRoomListActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.bxkj.jrzp.live.room.ui.createroom.CreateLiveRoomActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.bxkj.jrzp.live.room.ui.livenotice.LiveNoticeActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.bxkj.jrzp.live.anchor.ui.livestatistics.LiveStatisticsActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.bxkj.jrzp.live.notice.ui.list.LiveNoticeListActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.bxkj.jrzp.live.videocall.ui.videocall.VideoCallActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait" />

        <service
            android:name="com.bxkj.jrzp.live.videocall.ui.videocall.CallJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
    </application>

</manifest>