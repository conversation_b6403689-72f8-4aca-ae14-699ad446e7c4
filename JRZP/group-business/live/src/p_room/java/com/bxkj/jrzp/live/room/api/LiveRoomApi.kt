package com.bxkj.jrzp.live.room.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.jrzp.live.room.data.LiveRoomDetailsData
import com.bxkj.jrzp.live.room.data.LiveRoomTypeData
import com.bxkj.jrzp.live.room.data.PendingLiveData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/15
 * @version: V1.0
 */
interface LiveRoomApi {

  @POST("/Zhibo/BeginZhiboByYugao/")
  suspend fun startLiveByLiveID(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Zhibo/AddZhibo/")
  suspend fun startLive(@Body encryptReqParams: EncryptReqParams): BaseResponse<Int>

  @POST("/Zhibo/GetNowZhiboYugao/")
  suspend fun checkHasPendingLive(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<PendingLiveData>>

  @POST("/Zhibo/EndZhibo/")
  suspend fun stopLive(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Zhibo/GetZhiboInfo/")
  suspend fun getLiveRoomInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<LiveRoomDetailsData>

  @POST("/Zhibo/GetZhiboMenuList/")
  suspend fun getStartLiveType(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<LiveRoomTypeData>>
}