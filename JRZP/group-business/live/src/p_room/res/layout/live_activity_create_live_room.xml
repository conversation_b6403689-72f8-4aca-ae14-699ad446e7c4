<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="com.bxkj.common.util.CheckUtils" />

        <import type="android.view.View" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.live.room.ui.createroom.CreateLiveRoomViewModel" />
    </data>

    <RelativeLayout style="@style/match_match"
        xmlns:android="http://schemas.android.com/apk/res/android">

        <FrameLayout
            android:id="@+id/fl_video_group"
            style="@style/match_match" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/common_dp_32"
            android:layout_height="@dimen/common_dp_32"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_10"
            android:onClick="@{onClickListener}"
            android:scaleType="centerInside"
            android:src="@drawable/live_ic_close" />

        <LinearLayout
            android:id="@+id/ll_room_info"
            style="@style/match_wrap"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_toEndOf="@id/iv_back"
            android:layout_toStartOf="@id/tv_switch_camera"
            android:background="@drawable/live_bg_create_room_layout"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_room_info"
                style="@style/match_wrap"
                android:layout_marginBottom="@dimen/dp_4">

                <ImageView
                    android:id="@+id/iv_cover"
                    android:layout_width="@dimen/live_create_room_cover_size"
                    android:layout_height="@dimen/live_create_room_cover_size"
                    android:layout_marginStart="@dimen/dp_4"
                    android:layout_marginTop="@dimen/dp_4"
                    android:onClick="@{onClickListener}"
                    android:src="@drawable/live_bg_create_resume_cover"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:imgPlaceholder="@{@drawable/upload_ic_uploading_placeholder}"
                    bind:imgRadius="@{@dimen/live_create_room_cover_radius}"
                    bind:imgUrl="@{viewModel.roomFrontCover}" />

                <ImageView
                    android:id="@+id/iv_add_cover"
                    style="@style/wrap_wrap"
                    android:src="@drawable/live_ic_add_room_cover"
                    android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.roomFrontCover)?View.VISIBLE:View.GONE}"
                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                    app:layout_constraintBottom_toTopOf="@id/tv_add_cover"
                    app:layout_constraintEnd_toEndOf="@id/iv_cover"
                    app:layout_constraintStart_toStartOf="@id/iv_cover"
                    app:layout_constraintTop_toTopOf="@id/iv_cover" />

                <TextView
                    android:id="@+id/tv_add_cover"
                    style="@style/Text.10sp.FFFFFF"
                    android:layout_marginBottom="@dimen/dp_4"
                    android:text="@string/live_create_room_add_cover"
                    android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.roomFrontCover)?View.VISIBLE:View.GONE}"
                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                    app:layout_constraintEnd_toEndOf="@id/iv_cover"
                    app:layout_constraintStart_toStartOf="@id/iv_cover" />

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/Text.14sp.FFFFFF"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp_8"
                    android:hint="@string/live_create_room_title_hint"
                    android:lines="2"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.roomTitle}"
                    android:textColorHint="@color/common_f4f4f4"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_cover"
                    app:layout_constraintTop_toTopOf="@id/iv_cover" />

                <CheckBox
                    android:id="@+id/cb_notice_fans"
                    style="@style/Text.10sp.FFFFFF"
                    android:layout_marginStart="@dimen/dp_8"
                    android:button="@null"
                    android:checked="true"
                    android:drawablePadding="@dimen/dp_4"
                    android:drawableStart="@drawable/live_ic_notice_fans_selector"
                    android:text="@string/live_create_room_notice_fans"
                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                    app:layout_constraintStart_toEndOf="@id/iv_cover"
                    app:layout_constraintTop_toBottomOf="@id/tv_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                style="@style/Line.Horizontal"
                android:layout_height="1px"
                android:layout_marginEnd="@dimen/dp_4"
                android:layout_marginStart="@dimen/dp_4"
                android:visibility="@{viewModel.liveType.meeting?View.GONE:View.VISIBLE}" />

            <LinearLayout
                style="@style/match_wrap"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="@{viewModel.liveType.meeting?View.GONE:View.VISIBLE}">

                <TextView
                    style="@style/Text.12sp.FFFFFF"
                    android:layout_margin="@dimen/dp_4"
                    android:text="@string/live_create_room_select_relate_job" />

                <TextView
                    android:id="@+id/tv_relate_job"
                    style="@style/Text.12sp.FFFFFF"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:layout_weight="1"
                    android:drawableEnd="@drawable/live_ic_more"
                    android:drawablePadding="@dimen/dp_8"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:hint="@string/live_create_room_select_relate_job_hint"
                    android:lines="1"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.relateJob}"
                    android:textColorHint="@color/common_white" />
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_live_notes"
            style="@style/Text.12sp.FFFFFF"
            android:layout_alignEnd="@id/ll_room_info"
            android:layout_alignStart="@id/ll_room_info"
            android:layout_below="@id/ll_room_info"
            android:layout_marginTop="@dimen/dp_8"
            android:background="@drawable/live_bg_create_room_layout"
            android:drawableEnd="@drawable/live_ic_more"
            android:drawablePadding="@dimen/dp_4"
            android:drawableStart="@drawable/live_ic_create_room_notes"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:paddingBottom="@dimen/dp_4"
            android:paddingEnd="@dimen/dp_8"
            android:paddingStart="@dimen/dp_8"
            android:paddingTop="@dimen/dp_4"
            android:singleLine="true"
            android:text="@string/live_create_room_notes" />

        <TextView
            android:id="@+id/tv_switch_camera"
            style="@style/Text.12sp.FFFFFF"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_14"
            android:drawablePadding="@dimen/dp_2"
            android:drawableTop="@drawable/live_ic_switch_camera"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:text="@string/live_create_room_switch_camera" />

        <TextView
            android:id="@+id/tv_beauty"
            style="@style/Text.12sp.FFFFFF"
            android:layout_alignEnd="@id/tv_switch_camera"
            android:layout_alignStart="@id/tv_switch_camera"
            android:layout_below="@id/tv_switch_camera"
            android:layout_marginTop="@dimen/dp_24"
            android:drawablePadding="@dimen/dp_2"
            android:drawableTop="@drawable/live_ic_beauty"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:text="@string/live_create_room_beauty" />

<!--        <TextView-->
<!--            android:id="@+id/tv_mirror"-->
<!--            style="@style/Text.12sp.FFFFFF"-->
<!--            android:layout_alignEnd="@id/tv_switch_camera"-->
<!--            android:layout_alignStart="@id/tv_switch_camera"-->
<!--            android:layout_below="@id/tv_beauty"-->
<!--            android:layout_marginTop="@dimen/dp_24"-->
<!--            android:drawablePadding="@dimen/dp_2"-->
<!--            android:drawableTop="@drawable/live_ic_mirror"-->
<!--            android:gravity="center"-->
<!--            android:onClick="@{onClickListener}"-->
<!--            android:text="@string/live_create_room_mirror" />-->

        <!--    <TextView-->
        <!--      android:id="@+id/tv_share"-->
        <!--      style="@style/Text.12sp.FFFFFF"-->
        <!--      android:layout_below="@id/tv_beauty"-->
        <!--      android:layout_alignStart="@id/tv_beauty"-->
        <!--      android:layout_alignEnd="@id/tv_beauty"-->
        <!--      android:layout_marginTop="@dimen/dp_24"-->
        <!--      android:drawableTop="@drawable/live_ic_share"-->
        <!--      android:drawablePadding="@dimen/dp_2"-->
        <!--      android:onClick="@{onClickListener}"-->
        <!--      android:text="@string/live_create_room_share" />-->

        <LinearLayout
            android:id="@+id/ll_live_type"
            style="@style/wrap_wrap"
            android:layout_above="@id/tv_start_live"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_40"
            android:orientation="horizontal">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_live_type"
                style="@style/wrap_wrap"
                bind:items="@{viewModel.liveTypeList}" />

            <!--      <TextView-->
            <!--        style="@style/wrap_wrap"-->
            <!--        android:button="@null"-->
            <!--        android:text="@string/live_create_room_type_recruitment"-->
            <!--        android:textColor="@color/live_room_type_text_selector"-->
            <!--        android:textSize="@dimen/common_sp_16"-->
            <!--        android:textStyle="bold"-->
            <!--        bind:selected="@{true}" />-->

        </LinearLayout>

        <CheckBox
            android:id="@+id/cb_live_by_other"
            style="@style/Text.14sp.FFFFFF"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/ll_live_type"
            android:layout_alignTop="@id/ll_live_type"
            android:layout_centerHorizontal="true"
            android:layout_toEndOf="@id/ll_live_type"
            android:buttonTint="@color/white"
            android:text="其他设备直播"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_start_live"
            style="@style/Button.Basic.Round"
            android:layout_above="@id/ll_agreement"
            android:layout_marginBottom="@dimen/dp_12"
            android:layout_marginEnd="@dimen/common_dp_60"
            android:layout_marginStart="@dimen/common_dp_60"
            android:onClick="@{onClickListener}"
            android:text="@string/live_create_room_start" />

        <LinearLayout
            android:id="@+id/ll_agreement"
            style="@style/wrap_wrap"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_77"
            android:onClick="@{onClickListener}"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_privacy"
                style="@style/Text.12sp.FFFFFF"
                android:drawablePadding="@dimen/dp_4"
                android:drawableStart="@drawable/ic_privacy_selector"
                android:onClick="@{onClickListener}"
                android:text="@string/live_create_room_agreement_prefix" />

            <TextView
                android:id="@+id/tv_agreement"
                style="@style/Text.12sp.FF7647"
                android:onClick="@{onClickListener}"
                android:text="@string/live_create_room_agreement" />

        </LinearLayout>

        <com.tencent.liteav.support.beauty.view.LiveBeautyPanel
            android:id="@+id/beauty_pannel"
            style="@style/match_wrap"
            android:layout_alignParentBottom="true"
            android:visibility="gone" />

    </RelativeLayout>
</layout>