<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.live.room.data.LiveRoomTypeData" />
  </data>

  <TextView
    style="@style/wrap_wrap"
    android:button="@null"
    android:text="@{data.name}"
    android:textColor="@color/live_room_type_text_selector"
    android:textSize="@dimen/common_sp_16"
    android:textStyle="bold"
    bind:selected="@{true}" />

</layout>