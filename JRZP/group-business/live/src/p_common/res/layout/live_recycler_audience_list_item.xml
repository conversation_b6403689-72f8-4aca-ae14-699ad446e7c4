<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="isSelf"
      type="Boolean" />

    <variable
      name="data"
      type="com.bxkj.jrzp.live.data.LiveAudienceInfo" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_16">

    <com.google.android.material.imageview.ShapeableImageView
      app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/common_dp_32"
      android:layout_height="@dimen/common_dp_32"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_16"
      bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgUrl="@{data.userAvatar}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:gravity="start"
      android:lines="1"
      android:text="@{data.userName}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_follow"
      style="@style/Text.12sp"
      android:layout_width="55dp"
      android:background="@{data.followed?@drawable/frame_eaeaea_round:@drawable/bg_10c198_round}"
      android:gravity="center"
      android:paddingTop="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{!data.followed?@string/shuangxuan_follow:@string/shuangxuan_followed}"
      android:textColor="@{!data.followed?@color/common_white:@color/cl_999999}"
      android:visibility="@{isSelf?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <View
      style="@style/Line.Horizontal"
      android:layout_width="@dimen/dp_0"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_name" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>