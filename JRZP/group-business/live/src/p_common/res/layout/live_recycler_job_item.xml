<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.PositionItemBean" />
    </data>

    <LinearLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:paddingEnd="@dimen/dp_16"
        android:paddingStart="@dimen/dp_16">

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_12"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/Text.12sp.FFFFFF"
                android:background="@drawable/live_bg_job_index"
                android:gravity="center"
                android:paddingEnd="@dimen/dp_4"
                android:paddingStart="@dimen/dp_4"
                android:text="@{String.valueOf(data.index)}" />

            <TextView
                style="@style/common_Text.18sp.333333.Bold"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="start"
                android:lines="1"
                android:text="@{data.name}" />

            <TextView
                style="@style/Text.16sp.ff7647"
                android:text="@{data.money}" />

        </LinearLayout>

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_8"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/live_Text.LiveJobItemParams"
                android:text="@{data.shiName}" />

            <TextView
                style="@style/live_Text.LiveJobItemParams"
                android:layout_marginStart="@dimen/dp_8"
                android:text="@{data.wtName}" />

            <TextView
                style="@style/live_Text.LiveJobItemParams"
                android:layout_marginStart="@dimen/dp_8"
                android:text="@{data.quaName}" />

            <Space
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_send_resume"
                style="@style/Text.14sp.FFFFFF"
                android:background="@drawable/common_bg_basic_round_btn_selector"
                android:enabled="@{!data.delivered}"
                android:paddingBottom="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_12"
                android:paddingStart="@dimen/dp_12"
                android:paddingTop="@dimen/dp_4"
                android:text="@{data.delivered?@string/live_job_resume_delivered:@string/live_job_send_resume}" />

            <TextView
                android:id="@+id/tv_received_resume"
                style="@style/Text.12sp.333333"
                android:text="@{@string/live_job_send_received_resume_count_format(data.toudiCount)}" />
        </LinearLayout>

        <View
            style="@style/Line.Horizontal"
            android:layout_marginTop="@dimen/dp_16" />
    </LinearLayout>
</layout>