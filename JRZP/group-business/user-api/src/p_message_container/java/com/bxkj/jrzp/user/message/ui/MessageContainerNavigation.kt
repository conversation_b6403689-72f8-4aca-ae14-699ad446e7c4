package com.bxkj.jrzp.user.message.ui

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/25
 * @version: V1.0
 */
class MessageContainerNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/messagegroup"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}