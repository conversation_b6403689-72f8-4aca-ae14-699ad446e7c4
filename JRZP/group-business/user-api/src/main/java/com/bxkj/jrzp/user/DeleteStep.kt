package com.bxkj.jrzp.user

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/30
 * @version: V1.0
 */
class DeleteStep {

  companion object {

    const val STEP_TIPS = 0
    const val STEP_CONFIRM = 1
  }

  @IntDef(STEP_TIPS, STEP_CONFIRM)
  @Target(VALUE_PARAMETER)
  @Retention(SOURCE)
  annotation class Step {}
}