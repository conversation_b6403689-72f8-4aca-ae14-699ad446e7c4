package com.bxkj.jrzp.user.schoolinfo.data

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.util.kotlin.getOrDefault

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/26
 * @version: V1.0
 */
class SchoolAttachInfoData private constructor(
  var id: Int = 0,
  var name: String,
  var uid: Int = 0
) : Parcelable {

  constructor(parcel: Parcel) : this(
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readInt()
  ) {
  }

  class DiffCallback : DiffUtil.ItemCallback<SchoolAttachInfoData>() {
    override fun areItemsTheSame(
      oldItem: SchoolAttachInfoData,
      newItem: SchoolAttachInfoData
    ): Boolean {
      return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
      oldItem: SchoolAttachInfoData,
      newItem: SchoolAttachInfoData
    ): Boolean {
      return oldItem.id == newItem.id && oldItem.name == newItem.name
    }
  }

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(id)
    parcel.writeString(name)
    parcel.writeInt(uid)
  }

  override fun describeContents(): Int {
    return 0
  }

  companion object CREATOR : Creator<SchoolAttachInfoData> {
    override fun createFromParcel(parcel: Parcel): SchoolAttachInfoData {
      return SchoolAttachInfoData(parcel)
    }

    override fun newArray(size: Int): Array<SchoolAttachInfoData?> {
      return arrayOfNulls(size)
    }

    fun fromName(name: String): SchoolAttachInfoData {
      return SchoolAttachInfoData(0, name)
    }
  }

}