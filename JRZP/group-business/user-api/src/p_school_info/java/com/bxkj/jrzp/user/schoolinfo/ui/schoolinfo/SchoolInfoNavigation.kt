package com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/27
 * @version: V1.0
 */
class SchoolInfoNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/schoolinfo"

    const val EXTRA_NEXT_STEP = "NEXT_STEP"
    const val EXTRA_SHOW_SWITCH_IDENTITY = "SHOW_SWITCH_IDENTITY"

    const val NEXT_STEP_BACK = 0
    const val NEXT_STEP_AUTH = 1

    @JvmStatic
    fun navigate(
      nextStep: Int = NEXT_STEP_BACK,
      showSwitchIdentity: Boolean = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_NEXT_STEP, nextStep)
        .withBoolean(EXTRA_SHOW_SWITCH_IDENTITY, showSwitchIdentity)
    }
  }
}