package com.bxkj.jrzp.user.mine.ui.membercenter

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/23
 * @version: V1.0
 */
class MemberCenterWebNavigation {
  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/membercenter"

    @JvmStatic
    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}