package com.bxkj.staggeredlist

import androidx.annotation.IntDef

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/17
 * @version: V1.0
 */
class VideoListType {
  companion object {
    const val FOLLOW = 1
    const val RECOMMEND = 2
    const val ADDRESS = 3
  }

  @IntDef(
    FOLLOW,
    RECOMMEND,
    ADDRESS
  )
  @Target(AnnotationTarget.VALUE_PARAMETER)
  @Retention(AnnotationRetention.SOURCE)
  annotation class Type
}