package com.bxkj.video.message;

import androidx.annotation.IntDef;

import com.bxkj.video.data.VideoData;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

/**
 * @Project: VideoRecruitment
 * @Description: 视频加载结果
 * @author:45457
 * @date: 2020/5/14
 * @version: V1.0
 */
public class VideoLoadResultMessage {

  public static final int RESULT_SUCCESS = 1;
  public static final int RESULT_ERROR = 2;
  public static final int RESULT_NO_MORE = 3;

  @IntDef({ RESULT_SUCCESS, RESULT_ERROR, RESULT_NO_MORE })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface ResultStatus {
  }

  private int status;
  private List<? extends VideoData> list;

  public static VideoLoadResultMessage success(List<? extends VideoData> list) {
    return new VideoLoadResultMessage(RESULT_SUCCESS, list);
  }

  public static VideoLoadResultMessage error() {
    return new VideoLoadResultMessage(RESULT_ERROR, null);
  }

  public static VideoLoadResultMessage noMore() {
    return new VideoLoadResultMessage(RESULT_NO_MORE, null);
  }

  private VideoLoadResultMessage(@ResultStatus int status, List<? extends VideoData> list) {
    this.status = status;
    this.list = list;
  }

  public int getStatus() {
    return status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public List<? extends VideoData> getList() {
    return list;
  }

  public void setList(List<? extends VideoData> list) {
    this.list = list;
  }

  public boolean isError() {
    return status == RESULT_ERROR;
  }

  public boolean isSuccess() {
    return status == RESULT_SUCCESS;
  }

  public boolean isNoMore() {
    return status == RESULT_NO_MORE;
  }
}
