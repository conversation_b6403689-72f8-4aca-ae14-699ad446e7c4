<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">

  <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
  <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />

  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="*" />
    </intent>

    <intent>
      <action android:name="android.intent.action.SENDTO" />
      <data android:scheme="*" />
    </intent>
  </queries>

  <application
    android:name="com.bxkj.jrzp.AppApplication"
    android:allowBackup="false"
    android:enableOnBackInvokedCallback="true"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/common_app_name"
    android:requestLegacyExternalStorage="true"
    android:roundIcon="@mipmap/ic_launcher"
    android:supportsRtl="true"
    android:theme="@style/JrzpAppTheme"
    tools:replace="android:name,android:theme,android:allowBackup,android:label">

    <meta-data
      android:name="ScopedStorage"
      android:value="true" />

    <activity
      android:name="com.bxkj.jrzp.ui.splash.SplashActivity"
      android:exported="true"
      android:screenOrientation="portrait"
      android:theme="@style/Theme.AppLauncher">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />

        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>

    <activity
      android:name="com.bxkj.jrzp.ui.guide.GuideActivity"
      android:screenOrientation="portrait" />

    <!--微信回调activity-->
    <activity
      android:name=".wxapi.WXEntryActivity"
      android:exported="true"
      android:label="@string/common_app_name"
      android:launchMode="singleTask"
      android:taskAffinity="com.bxkj.jrzp"
      android:theme="@android:style/Theme.Translucent.NoTitleBar" />

    <!--============================== 信鸽推送 ==============================-->
    <meta-data
      android:name="XG_SERVER_SUFFIX"
      android:value="tpns.sh.tencent.com" />

    <meta-data
      android:name="XG_SERVICE_PULL_UP_OFF"
      android:value="true" />

    <receiver
      android:name=".push.XGPushMsgReceiver"
      android:exported="true">
      <intent-filter>
        <!-- 接收消息透传 -->
        <action android:name="com.tencent.android.xg.vip.action.PUSH_MESSAGE" />
        <!-- 监听注册、反注册、设置/删除标签、通知被点击等处理结果 -->
        <action android:name="com.tencent.android.xg.vip.action.FEEDBACK" />
      </intent-filter>
    </receiver>

    <activity
      android:name=".push.PushMsgHandleActivity"
      android:exported="true"
      android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">

      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />

        <data
          android:host="com.jrzp.push"
          android:path="/msg_handle"
          android:scheme="jrzp" />
      </intent-filter>

    </activity>
  </application>

</manifest>