<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.onlinecampustalkdetails.OnlineCampusTalkDetailsViewModel" />
    </data>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.bxkj.common.widget.MyVideoPlayer
                android:id="@+id/videp_player"
                android:layout_width="match_parent"
                android:layout_height="220dp" />

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    style="@style/match_wrap"
                    android:background="@drawable/bg_f4f4f4"
                    android:orientation="vertical"
                    android:padding="@dimen/dp_6">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_ffffff_radius_4"
                        android:paddingBottom="@dimen/dp_10"
                        android:paddingEnd="@dimen/dp_8"
                        android:paddingStart="@dimen/dp_8"
                        android:paddingTop="@dimen/dp_10">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/iv_logo"
                            android:layout_width="@dimen/common_dp_44"
                            android:layout_height="@dimen/common_dp_44"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:shapeAppearance="@style/roundedCornerImageStyle"
                            bind:imgUrl="@{viewModel.onlineCampusTalkDetails.comLogo}" />

                        <TextView
                            android:id="@+id/tv_title"
                            style="@style/Text.18sp.333333"
                            android:layout_width="@dimen/dp_0"
                            android:layout_marginStart="@dimen/dp_8"
                            android:ellipsize="end"
                            android:lines="1"
                            android:text="@{viewModel.onlineCampusTalkDetails.title}"
                            app:layout_constraintBottom_toTopOf="@id/tv_date"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/iv_logo"
                            app:layout_constraintTop_toTopOf="@id/iv_logo"
                            app:layout_constraintVertical_chainStyle="packed" />

                        <TextView
                            android:id="@+id/tv_date"
                            style="@style/Text.12sp.999999"
                            android:layout_width="0dp"
                            android:text="@{@string/personal_campus_talk_date_format(viewModel.onlineCampusTalkDetails.ksdate)}"
                            app:layout_constraintBottom_toBottomOf="@id/iv_logo"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@id/tv_title"
                            app:layout_constraintTop_toBottomOf="@id/tv_title" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        style="@style/Text.16sp.333333"
                        android:layout_marginBottom="@dimen/dp_8"
                        android:layout_marginTop="@dimen/dp_8"
                        android:text="@string/personal_campus_recruit_brochure_text" />

                    <com.bxkj.common.widget.YUIWebView
                        android:id="@+id/web_brochure"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_ffffff_radius_4" />

                    <TextView
                        style="@style/Text.16sp.333333"
                        android:layout_marginBottom="@dimen/dp_8"
                        android:layout_marginTop="@dimen/dp_8"
                        android:text="@string/personal_campus_recruit_job_text" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_job_list"
                        style="@style/match_wrap"
                        android:background="@drawable/bg_ffffff_radius_4"
                        bind:items="@{viewModel.campusRecruitList}" />


                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:background="@drawable/bg_00000000"
            app:left_img="@drawable/common_ic_white_back" />

    </FrameLayout>

</layout>