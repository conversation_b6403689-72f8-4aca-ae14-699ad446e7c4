<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.bxkj.common.util.HtmlUtils" />

        <variable
            name="data"
            type="com.bxkj.personal.data.CompanyDetailsData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingTop="@dimen/dp_16">

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:loadRadiusImg="@{data.companyLogo}" />

        <TextView
            android:id="@+id/tv_name"
            style="@style/Text.18sp.333333.Bold"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_14"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{HtmlUtils.fromHtml(data.companyName)}"
            app:layout_constraintEnd_toStartOf="@id/tv_follow"
            app:layout_constraintStart_toEndOf="@id/iv_logo"
            app:layout_constraintTop_toTopOf="@id/iv_logo" />

        <TextView
            android:id="@+id/tv_follow"
            style="@style/Text.12sp"
            android:layout_width="@dimen/common_dp_60"
            android:layout_height="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_14"
            android:background="@{data.followCompany?@drawable/frame_10c198_round:@drawable/bg_10c198_round}"
            android:gravity="center"
            android:text="@{data.followCompany?@string/video_list_followed:@string/video_list_follow}"
            android:textColor="@{data.followCompany?@color/cl_ff7405:@color/common_white}"
            app:layout_constraintBottom_toBottomOf="@id/iv_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_logo" />

        <TextView
            android:id="@+id/tv_about_info"
            style="@style/Text.12sp.999999"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_14"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.aboutInfo}"
            app:layout_constraintEnd_toStartOf="@id/tv_follow"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

        <View
            android:id="@+id/v_split"
            style="@style/Line.Horizontal"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_logo" />

        <LinearLayout
            android:id="@+id/ll_jobs_info"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_split">

            <TextView
                android:id="@+id/tv_job_prefix"
                style="@style/Text.14sp.333333"
                android:layout_marginStart="@dimen/dp_14"
                android:text="@string/company_jobs_text" />

            <TextView
                style="@style/Text.14sp.333333"
                android:layout_marginEnd="@dimen/dp_14"
                android:layout_weight="1"
                android:ellipsize="middle"
                android:singleLine="true"
                android:text="@{data.count==0?@string/company_jobs_have_not_tips:@string/company_jobs_count_format(data.jobName,data.count)}" />

            <ImageView
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/dp_14"
                android:src="@drawable/common_ic_next" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>