<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="com.bxkj.common.util.CheckUtils" />

        <import type="com.bxkj.common.util.HtmlUtils" />

        <variable
            name="data"
            type="com.bxkj.personal.data.AnswerItemData.CommentItem" />
    </data>

    <TextView
        style="@style/Text.14sp.black"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:text="@{HtmlUtils.fromHtml(CheckUtils.isNullOrEmpty(data.toUserNickName)?@string/moment_details_comment_reply_format(data.nickName,data.content):@string/moment_details_comment_reply_two_level_format(data.nickName,data.toUserNickName,data.content))}" />

</layout>
