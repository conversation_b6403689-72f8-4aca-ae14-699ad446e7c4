<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.learning.data.IndustrialServicesData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="345dp"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_10">

    <View
      android:id="@+id/v_split"
      style="@style/Line.Vertical"
      android:layout_height="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_20"
      android:layout_marginBottom="@dimen/dp_20"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:id="@+id/iv_logo"
      bind:imgRadius="@{@dimen/dp_4}"
      bind:imgUrl="@{data.fixProductImg}"
      android:layout_width="90dp"
      android:layout_height="90dp"
      android:layout_marginStart="@dimen/dp_16"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.productName}"
      app:layout_constraintEnd_toStartOf="@id/tv_price"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_price"
      style="@style/Text.14sp.FE6600.Bold"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{@string/personal_home_news_pro_employment_price_format(data.minPrice)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.14sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{@string/personal_home_news_pro_employment_desc_format(data.volume,data.commentCount)}"
      app:layout_constraintEnd_toStartOf="@id/tv_buy_service"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      style="@style/Text.12sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_8"
      android:ellipsize="end"
      android:singleLine="true"
      android:text="@{data.companyName}"
      app:layout_constraintBottom_toBottomOf="@id/tv_buy_service"
      app:layout_constraintEnd_toStartOf="@id/tv_buy_service"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="@id/tv_buy_service" />

    <TextView
      android:id="@+id/tv_buy_service"
      style="@style/Text.12sp.FFFFFF"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:background="@drawable/bg_fe6600_round"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/home_recommend_buy_service"
      app:layout_constraintBottom_toBottomOf="@id/iv_logo"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>