<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:background="@drawable/bg_ffffff"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_back" />

            <TextView
                android:id="@+id/tv_search_text"
                style="@style/Text.15sp.333333"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/common_dp_5"
                android:layout_marginEnd="@dimen/common_dp_35"
                android:layout_marginBottom="@dimen/common_dp_5"
                android:layout_weight="1"
                android:background="@drawable/common_bg_rounded_rectangle_f4f4f4"
                android:drawableStart="@drawable/common_ic_search"
                android:drawablePadding="@dimen/dp_10"
                android:gravity="center_vertical"
                android:hint="@string/common_search_position"
                android:onClick="@{onClickListener}"
                android:paddingStart="@dimen/dp_20"
                android:text="@{viewModel.searchText}" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cl_job_result_filter_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:background="@drawable/common_bg_title_bar">

            <FrameLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tv_expect_industry"
                    style="@style/Text.JobResultFilter"
                    android:layout_gravity="center"
                    android:onClick="@{onClickListener}"
                    android:text="@string/personal_expect_industry" />

            </FrameLayout>

            <FrameLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tv_work_place"
                    style="@style/Text.JobResultFilter"
                    android:layout_gravity="center"
                    android:onClick="@{onClickListener}"
                    android:text="@string/personal_working_place" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tv_sort"
                    style="@style/Text.JobResultFilter"
                    android:layout_gravity="center"
                    android:onClick="@{onClickListener}"
                    android:text="@string/personal_sort" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tv_more_requirements"
                    style="@style/Text.JobResultFilter"
                    android:layout_gravity="center"
                    android:onClick="@{onClickListener}"
                    android:text="@string/personal_more_requirements" />
            </FrameLayout>

        </LinearLayout>

        <include
            android:id="@+id/include_job_list"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.jobListViewModel}" />
    </LinearLayout>
</layout>