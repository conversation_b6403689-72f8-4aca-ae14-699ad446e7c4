<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.UnreadVideoMsgData" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:orientation="vertical">

    <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
      style="@style/match_wrap"
      android:paddingStart="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_12">

      <FrameLayout
        android:id="@+id/fl_logo"
        style="@style/wrap_wrap"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <de.hdodenhof.circleimageview.CircleImageView
          android:layout_width="46dp"
          android:layout_height="46dp"
          android:src="@drawable/ic_video_un_read_msg"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <View
          android:layout_width="@dimen/dp_10"
          android:layout_height="@dimen/dp_10"
          android:layout_gravity="end"
          android:background="@drawable/shape_red_point"
          android:visibility="@{data.hasUnReadMsg?View.VISIBLE:View.GONE}" />
      </FrameLayout>

      <TextView
        android:id="@+id/tv_video_msg_title"
        style="@style/Text.16sp.333333"
        android:layout_marginStart="@dimen/dp_12"
        android:text="@string/message_notice_video_msg"
        app:layout_constraintStart_toEndOf="@id/fl_logo"
        app:layout_constraintTop_toTopOf="@id/fl_logo" />

      <TextView
        style="@style/Text.12sp.888888"
        android:text="@{data.hasUnReadMsg?@string/message_notice_video_msg_has:@string/message_notice_video_msg_no}"
        app:layout_constraintBottom_toBottomOf="@id/fl_logo"
        app:layout_constraintStart_toStartOf="@id/tv_video_msg_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <FrameLayout
      android:id="@+id/fl_msg_type"
      style="@style/match_wrap"
      android:background="@drawable/bg_f4f4f4">

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_height="@dimen/dp_40"
        android:drawableEnd="@drawable/ic_black_expand"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:text="@{data.msgType}" />
    </FrameLayout>

  </LinearLayout>

</layout>