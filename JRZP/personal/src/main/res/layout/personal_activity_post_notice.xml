<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.postnotice.PostNoticeViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_match"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            style="@style/match_wrap"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:right_text="@string/post_notice_release_text"
            app:title="@string/post_notice_title" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_title"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_54"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@null"
                android:hint="@string/post_notice_title_hint"
                android:maxLength="30"
                android:text="@={viewModel.noticeTitle}"
                android:textColorHint="@color/cl_999999"
                android:textSize="22sp" />

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12" />

<!--            <com.sanjindev.yui.weiget.commonitemview.YUICommonListItemView-->
<!--                style="@style/JrzpCommonListItemView"-->
<!--                android:onClick="@{()->viewModel.showSchoolNoticeType()}"-->
<!--                android:visibility="@{viewModel.showNoticeTypePicker?View.VISIBLE:View.GONE}"-->
<!--                app:yui_accessory_type="chevron"-->
<!--                app:yui_content_hint="@string/common_please_select"-->
<!--                app:yui_content="@{viewModel.noticeType.name}"-->
<!--                app:yui_content_type="text"-->
<!--                app:yui_title="@string/post_notice_type" />-->

            <com.rex.editor.view.RichEditorNew
                android:id="@+id/rich_editor"
                style="@style/match_match" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:background="@drawable/frame_f4f4f4"
            android:orientation="horizontal"
            android:visibility="gone">

            <HorizontalScrollView
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <LinearLayout
                    style="@style/match_match"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_upload_photo"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_rechtext_photo" />

                    <ImageView
                        android:id="@+id/iv_bold"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_richtext_bold_selector" />

                    <ImageView
                        android:id="@+id/iv_underline"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_richtext_underline_selector" />

                    <ImageView
                        android:id="@+id/iv_title"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_richtext_title_selector" />

                    <!--                    <CheckBox-->
                    <!--                        android:id="@+id/iv_quote"-->
                    <!--                        style="@style/common_wrap_wrap"-->
                    <!--                        android:button="@drawable/ic_richtext_quote_selector"-->
                    <!--                        android:onClick="@{onClickListener}" />-->

                    <ImageView
                        android:id="@+id/iv_unorderedlist"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_richtext_unorderedlist_selector" />

                    <ImageView
                        android:id="@+id/iv_orderedlist"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_richtext_orderedlist_selector" />

                </LinearLayout>
            </HorizontalScrollView>

            <View
                style="@style/Line.Vertical"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginBottom="@dimen/dp_6"
                android:background="@color/common_eaeaea" />

            <LinearLayout
                style="@style/wrap_match"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_last"
                    style="@style/wrap_wrap"
                    android:layout_marginStart="@dimen/dp_16"
                    android:onClick="@{onClickListener}"
                    android:src="@drawable/ic_richtext_last_step" />

                <ImageView
                    android:id="@+id/iv_next"
                    style="@style/wrap_wrap"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:onClick="@{onClickListener}"
                    android:src="@drawable/ic_richtext_next_step" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</layout>