<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <View
        android:id="@+id/v_line_four"
        style="@style/Line.JobDetailsDivider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bxkj.common.widget.ZPTextView
        android:id="@+id/tv_work_place"
        style="@style/Text.JobDetailsItemTitle"
        android:text="@string/personal_working_place"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line_four" />

    <TextView
        android:id="@+id/tv_see_directions"
        style="@style/Text.12sp.FF7647"
        android:layout_height="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/common_bg_rounded_rectangle_f8f8f8"
        android:drawableEnd="@drawable/personal_ic_job_desc_contract_see_derictions"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_10"
        android:text="@string/personal_see_directions"
        app:layout_constraintBottom_toBottomOf="@id/tv_work_place"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_work_place" />

    <FrameLayout
        android:id="@+id/fl_company_location"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/img_map_placeholder"
        app:layout_constraintDimensionRatio="25:9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_see_directions">

        <include layout="@layout/personal_layout_map_address" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>