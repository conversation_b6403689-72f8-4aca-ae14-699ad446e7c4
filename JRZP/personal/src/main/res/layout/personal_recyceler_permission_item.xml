<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.ui.activity.permissionmanagement.PermissionItem" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap">

    <TextView
      android:id="@+id/tv_permission_title"
      style="@style/Text.16sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{data.permissionTitle}"
      app:layout_constraintEnd_toStartOf="@id/tv_setting"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_setting"
      style="@style/Text.14sp.B5B5B5"
      android:layout_marginEnd="@dimen/dp_16"
      android:drawableEnd="@drawable/common_ic_next"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical"
      android:text="@{data.opened?@string/permission_management_opened:@string/permission_management_setting}"
      app:layout_constraintBottom_toBottomOf="@id/tv_permission_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_permission_title" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp.FF7647"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{data.permissionUrlTips}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_permission_title"
      app:layout_constraintTop_toBottomOf="@id/tv_permission_title" />

    <View
      style="@style/Line.Horizontal.Margin12OfStartAndEnd"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>