<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.FeedbackMsgContentItemData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.10sp.333333"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/common_ececec"
            android:paddingStart="@dimen/dp_4"
            android:paddingEnd="@dimen/dp_4"
            android:text="@{data.date}" />

        <LinearLayout
            style="@style/match_wrap"
            android:background="@drawable/bg_ffffff_radius_4"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_msg_type"
                style="@style/Text.16sp.333333"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@{data.typeName}" />

            <TextView
                android:id="@+id/tv_content"
                style="@style/Text.15sp.333333"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginBottom="@dimen/dp_12"
                android:background="@drawable/frame_eaeaea"
                android:padding="@dimen/dp_10"
                android:text="@{data.content}"
                android:textStyle="bold" />

        </LinearLayout>
    </LinearLayout>
</layout>
