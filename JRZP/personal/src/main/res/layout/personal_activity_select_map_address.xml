<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_match"
  android:background="@drawable/bg_ffffff"
  android:orientation="vertical">

  <com.bxkj.common.widget.CommonTitleBar style="@style/match_wrap" />

  <LinearLayout
    android:id="@+id/ll_select_address"
    style="@style/Layout.InfoItem">

    <TextView
      style="@style/Text.14sp.333333"
      android:text="@string/select_address_region" />

    <TextView
      android:id="@+id/tv_region"
      style="@style/Text.InfoItem.Select"
      android:hint="@string/common_please_select" />
  </LinearLayout>

  <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

  <LinearLayout
    android:id="@+id/ll_details_address"
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_1"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_12"
    android:paddingTop="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_16"
    android:visibility="gone">

    <TextView
      style="@style/Text.14sp.333333"
      android:text="@string/select_address_details" />

    <EditText
      android:id="@+id/et_details"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/dp_6"
      android:layout_weight="1"
      android:background="@null"
      android:gravity="top|end"
      android:hint="@string/select_address_details_hint"
      android:lines="1"
      android:textColor="@color/cl_333333"
      android:textColorHint="@color/common_b5b5b5"
      android:textSize="@dimen/sp_14" />
  </LinearLayout>

  <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

  <androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_map_group"
    android:layout_width="match_parent"
    android:layout_height="365dp"
    android:layout_marginTop="@dimen/dp_10">

    <com.baidu.mapapi.map.MapView
      android:id="@+id/map_address"
      android:layout_width="match_parent"
      android:layout_height="365dp"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
      android:id="@+id/gl_center_line"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      app:layout_constraintGuide_percent="0.5" />

    <ImageView
      android:id="@+id/iv_position_shadow"
      style="@style/wrap_wrap"
      android:src="@drawable/ic_select_address_position_shadow"
      app:layout_constraintBottom_toBottomOf="@id/gl_center_line"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/gl_center_line" />

    <ImageView
      android:id="@+id/iv_position"
      style="@style/wrap_wrap"
      android:src="@drawable/ic_select_address_position"
      app:layout_constraintBottom_toTopOf="@id/gl_center_line"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />
  </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>