<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.postvideo.PostVideoViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:background="@color/f2f2f2"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/post_video_title" />

        <LinearLayout
            android:id="@+id/ll_upload_progress"
            style="@style/match_wrap"
            android:background="@drawable/bg_ffffff"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.videoCompressStatus)?View.GONE:View.VISIBLE}">

            <TextView
                style="@style/Text.14sp.333333"
                android:padding="@dimen/dp_12"
                android:text="@{viewModel.videoCompressStatus}" />

            <ProgressBar
                android:id="@+id/progress_compress"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_2" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="210dp"
            android:background="@color/common_black"
            android:onClick="@{onClickListener}">

            <VideoView
                android:id="@+id/video_player"
                style="@style/match_match"
                android:layout_gravity="center" />

            <ImageView
                android:id="@+id/iv_cover"
                style="@style/match_match" />

            <ImageView
                android:id="@+id/iv_play"
                style="@style/wrap_wrap"
                android:layout_gravity="center"
                android:onClick="@{onClickListener}"
                android:src="@drawable/ic_play_video" />

            <TextView
                android:id="@+id/tv_edit_cover"
                style="@style/Text.14sp.FFFFFF"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="@dimen/dp_8"
                android:layout_marginBottom="@dimen/common_dp_5"
                android:onClick="@{()->viewModel.toEditVideoCover()}"
                android:text="@string/post_video_edit_cover" />
        </FrameLayout>

        <LinearLayout
            style="@style/match_wrap"
            android:background="@drawable/bg_ffffff"
            android:orientation="vertical">

            <LinearLayout style="@style/Layout.InfoItem">

                <TextView
                    style="@style/Text.14sp.333333"
                    android:text="@string/post_video_title_text" />

                <TextView
                    style="@style/wrap_wrap"
                    android:text="*"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    android:id="@+id/tv_video_title"
                    style="@style/Text.InfoItem.Select"
                    android:hint="@string/post_video_title_hint"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.videoTitle}" />
            </LinearLayout>

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14" />

            <LinearLayout style="@style/Layout.InfoItem">

                <TextView
                    style="@style/Text.14sp.333333"
                    android:text="@string/post_video_type_text" />

                <TextView
                    style="@style/wrap_wrap"
                    android:text="*"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    style="@style/wrap_wrap"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    android:id="@+id/tv_video_type"
                    style="@style/Text.InfoItem.Select"
                    android:hint="@string/post_video_type_hint"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.selectVideoType.type}" />
            </LinearLayout>

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14" />

            <LinearLayout
                style="@style/Layout.InfoItem"
                android:visibility="@{viewModel.selectVideoType.recruit?View.VISIBLE:View.GONE}">

                <TextView
                    style="@style/Text.14sp.333333"
                    android:text="@string/post_video_job_salary" />

                <TextView
                    style="@style/wrap_wrap"
                    android:text="*"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    style="@style/wrap_wrap"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    android:id="@+id/tv_video_job_salary"
                    style="@style/Text.InfoItem.Select"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.jobSalary.name}" />
            </LinearLayout>

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14"
                android:visibility="@{viewModel.selectVideoType.recruit?View.VISIBLE:View.GONE}" />

            <LinearLayout
                style="@style/Layout.InfoItem"
                android:visibility="@{viewModel.selectVideoType.recruit?View.VISIBLE:View.GONE}">

                <TextView
                    style="@style/Text.14sp.333333"
                    android:text="@string/post_video_job_address" />

                <TextView
                    style="@style/wrap_wrap"
                    android:text="*"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    style="@style/wrap_wrap"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    android:id="@+id/tv_video_job_address"
                    style="@style/Text.InfoItem.Select"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.jobAddress.provinceAndCityText+viewModel.jobAddressDetails}" />
            </LinearLayout>

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14"
                android:visibility="@{viewModel.selectVideoType.recruit?View.VISIBLE:View.GONE}" />

            <LinearLayout style="@style/Layout.InfoItem">

                <TextView
                    style="@style/Text.14sp.333333"
                    android:text="@string/post_video_desc_text" />

                <TextView
                    style="@style/wrap_wrap"
                    android:textColor="@color/common_ec535b" />

                <TextView
                    android:id="@+id/tv_video_desc"
                    style="@style/Text.InfoItem.Select"
                    android:hint="@string/post_video_desc_hint"
                    android:onClick="@{onClickListener}"
                    android:text="@{viewModel.videoContent}" />
            </LinearLayout>

        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_publish"
            style="@style/Text.14sp.FFFFFF"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/bg_round_btn_selector"
            android:enabled="@{!CheckUtils.isNullOrEmpty(viewModel.videoTitle)&amp;&amp;(viewModel.selectVideoType.id==3?viewModel.jobSalary!=null&amp;&amp;(!CheckUtils.isNullOrEmpty(viewModel.jobAddressDetails)):viewModel.selectVideoType!=null)}"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:text="@string/post_video_post_btn_text" />
    </LinearLayout>
</layout>