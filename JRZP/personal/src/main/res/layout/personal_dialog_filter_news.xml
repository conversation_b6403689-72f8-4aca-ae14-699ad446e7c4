<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_filter"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/common_e8e8e8" />

    <LinearLayout
        android:id="@+id/ll_filter_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_reset"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/common_reset"
            android:textColor="@color/common_888888"
            android:textSize="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/cl_ff7405"
            android:gravity="center"
            android:text="@string/common_confirm"
            android:textColor="@color/common_white"
            android:textSize="@dimen/dp_16" />
    </LinearLayout>

</LinearLayout>