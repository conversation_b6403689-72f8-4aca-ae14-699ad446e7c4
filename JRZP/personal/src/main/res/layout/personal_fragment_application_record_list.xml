<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/common_include_refresh_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom_delete_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:background="@color/common_fbfbfb"
        android:visibility="gone">

        <View
            android:id="@+id/v_top_line"
            style="@style/Line.Horizontal"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_selected"
            style="@style/Text.15sp.888888"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="@dimen/dp_12" />

        <TextView
            android:id="@+id/tv_delete"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_selected"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/common_dp_42"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_12"
            android:background="@drawable/common_bg_basic_btn_selector"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/common_delete"
            android:textColor="@color/common_button_text_selector" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>