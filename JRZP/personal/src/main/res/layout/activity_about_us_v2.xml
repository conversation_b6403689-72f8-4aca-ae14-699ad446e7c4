<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.aboutus.AboutUsV2ViewModel" />
    </data>

    <LinearLayout
        style="@style/match_match"
        android:background="@drawable/bg_ffffff"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/setting_about_us" />

        <ImageView
            style="@style/wrap_wrap"
            android:layout_marginTop="@dimen/dp_40"
            android:src="@drawable/personal_ic_app_logo" />

        <TextView
            android:id="@+id/tv_app_version"
            style="@style/Text.15sp.888888"
            android:layout_marginTop="@dimen/dp_10"
            android:text="1.0.0" />

        <View
            style="@style/Line.Horizontal.Light"
            android:layout_height="@dimen/dp_8"
            android:layout_marginTop="@dimen/common_dp_60" />

        <LinearLayout
            style="@style/Layout.InfoItem"
            android:onClick="@{()->viewModel.callServicesTel()}">

            <TextView
                style="@style/Text.15sp.333333"
                android:text="@string/common_service_phone" />

            <TextView
                android:id="@+id/tv_hotline"
                style="@style/Text.15sp.888888"
                android:layout_marginStart="@dimen/dp_10"
                android:text="@{viewModel.aboutAppInfo.tel}" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
            style="@style/Layout.InfoItem"
            android:onClick="@{()->viewModel.jumpToCompanyWebsite()}">

            <TextView
                style="@style/Text.15sp.333333"
                android:text="@string/common_company_url" />

            <TextView
                android:id="@+id/tv_url"
                style="@style/Text.15sp.888888"
                android:layout_marginStart="@dimen/dp_10"
                android:text="@{viewModel.aboutAppInfo.web}" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
            android:id="@+id/ll_zizhi"
            style="@style/Layout.InfoItem">

            <TextView
                style="@style/Text.15sp.333333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/common_company_zizhi" />

            <ImageView
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/dp_12"
                android:src="@drawable/common_ic_next" />

        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

    </LinearLayout>
</layout>