<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.resumetop.ResumeTopViewModelV2" />
  </data>

  <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.core.widget.NestedScrollView
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_weight="1"
      android:fillViewport="true">

      <LinearLayout
        style="@style/match_match"
        android:orientation="vertical">

        <ImageView
          style="@style/match_wrap"
          android:adjustViewBounds="true"
          android:src="@drawable/c_img_resume_top_banner" />

        <LinearLayout
          style="@style/match_match"
          android:background="@drawable/c_bg_resume_top_content"
          android:orientation="vertical">

          <TextView
            style="@style/Text.14sp.333333"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            android:background="@drawable/bg_ffffff_radius_10"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_16"
            android:text="@{HtmlUtils.fromHtml(@string/c_resume_top_days_remaining_format(viewModel.resumeTopDaysRemaining))}" />

          <LinearLayout
            style="@style/match_wrap"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_marginBottom="@dimen/common_dp_60"
            android:background="@drawable/bg_ffffff_radius_10"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_16">

            <com.bxkj.common.widget.ZPTextView
              style="@style/Text.14sp.333333"
              android:text="@string/c_resume_top_service_select_title" />

            <androidx.recyclerview.widget.RecyclerView
              android:id="@+id/recycler_service"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_16"
              bind:items="@{viewModel.serviceList}" />

          </LinearLayout>

        </LinearLayout>

      </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <FrameLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_gravity="bottom"
      android:background="@drawable/c_bg_resume_top_bottom"
      android:paddingTop="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_16">

      <TextView
        style="@style/Text.18sp.333333"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_32"
        android:onClick="@{()->viewModel.createOrder()}"
        android:background="@drawable/c_bg_resume_top_payment"
        android:gravity="center"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:text="@{@string/c_resume_top_payment_format(viewModel.selectedService.price)}" />
    </FrameLayout>

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      android:background="@drawable/bg_00000000"
      app:left_img="@drawable/ic_back_white" />

  </FrameLayout>
</layout>