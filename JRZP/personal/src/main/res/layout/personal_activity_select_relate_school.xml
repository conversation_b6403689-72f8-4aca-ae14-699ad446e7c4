<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_match"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/personal_select_relate_school_page_title" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48">

            <com.bxkj.common.widget.MyEditText
                android:id="@+id/et_keyword"
                style="@style/Text.14sp.333333"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_32"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@drawable/bg_f4f4f4"
                android:drawableStart="@drawable/ic_search"
                android:drawablePadding="@dimen/dp_4"
                android:hint="@string/personal_select_relate_school_search_hint"
                android:paddingStart="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_8"
                android:text="@={viewModel.searchKeyword}" />
        </FrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_selected_school"
            style="@style/match_wrap"
            android:paddingStart="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_6"
            android:visibility="@{viewModel.selectedSchool.size()>0?View.VISIBLE:View.GONE}"
            bind:items="@{viewModel.selectedSchool}" />

        <include
            android:id="@+id/include_school_list"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.schoolListViewModel}" />

        <View style="@style/Line.Horizontal" />

        <TextView
            style="@style/Button.Basic.Round"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_8"
            android:enabled="@{viewModel.selectedSchool.size()>0}"
            android:onClick="@{()->viewModel.confirm()}"
            android:text="@{@string/personal_select_relate_school_selected_format(viewModel.selectedSchool.size())}" />
    </LinearLayout>
</layout>