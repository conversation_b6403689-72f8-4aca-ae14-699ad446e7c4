<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.DiscussData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_18"
    android:paddingTop="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_18"
    android:paddingBottom="@dimen/dp_12">

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_32"
      android:layout_height="@dimen/dp_32"
      bind:imgUrl="@{data.photo}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:shapeAppearance="@style/roundedCornerImageStyle.Avatar" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.14sp.333333.Bold"
      android:layout_marginStart="@dimen/dp_8"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.nickName}"
      app:layout_constrainedWidth="true"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_city"
      style="@style/Text.10sp.888888"
      android:layout_width="@dimen/dp_0"
      android:text="@{data.cityName}"
      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <androidx.constraintlayout.widget.Barrier
      android:id="@+id/barrier_user_info_bottom"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      app:barrierDirection="bottom"
      app:constraint_referenced_ids="iv_avatar,tv_city" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.15sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_12"
      android:text="@{data.title}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/barrier_user_info_bottom" />

    <TextView
      android:id="@+id/tv_views_count"
      style="@style/Text.10sp.333333"
      android:layout_marginEnd="@dimen/dp_16"
      android:drawableStart="@drawable/personal_ic_qa_view_count"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical"
      android:text="@{String.valueOf(data.view)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_comment_count"
      app:layout_constraintEnd_toStartOf="@id/tv_comment_count"
      app:layout_constraintTop_toTopOf="@id/tv_comment_count" />

    <TextView
      android:id="@+id/tv_comment_count"
      style="@style/Text.10sp.333333"
      android:layout_marginTop="@dimen/dp_12"
      android:drawableStart="@drawable/personal_ic_qa_comment_count"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical"
      android:text="@{String.valueOf(data.plCount)}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>