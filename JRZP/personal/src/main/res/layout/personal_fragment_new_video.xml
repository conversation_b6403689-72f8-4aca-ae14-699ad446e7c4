<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <LinearLayout
      android:id="@+id/ll_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52"
      android:background="@color/cl_ff7405"
      android:orientation="horizontal">

      <LinearLayout
        android:id="@+id/ll_search_bar"
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_6"
        android:layout_weight="1"
        android:background="@drawable/bg_ffffff_radius_4"
        android:gravity="center_vertical"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12">

        <ImageView
          style="@style/wrap_wrap"
          android:src="@drawable/ic_home_search" />

        <com.bxkj.common.widget.marquee.MarqueeView
          android:id="@+id/marquee_hot_keyword"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:layout_marginStart="@dimen/dp_12" />

      </LinearLayout>

      <TextView
        android:id="@+id/tv_post"
        style="@style/Text.12sp.FFFFFF"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:drawableTop="@drawable/ic_home_camera"
        android:onClick="@{onClickListener}"
        android:text="@string/personal_publish" />

    </LinearLayout>

    <FrameLayout
      android:id="@+id/fl_content"
      style="@style/match_match" />

  </LinearLayout>
</layout>