<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.comment.data.CommentItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <de.hdodenhof.circleimageview.CircleImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_36"
      android:layout_height="@dimen/dp_36"
      android:layout_marginTop="@dimen/dp_16"
      app:civ_border_color="@color/common_f4f4f4"
      app:civ_border_width="@dimen/dp_1"
      bind:imgUrl="@{data.photo}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_nick_name"
      style="@style/Text.14sp.4d8fcc"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_4"
      android:layout_marginTop="@dimen/dp_18"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{data.nickName}"
      app:layout_constraintEnd_toStartOf="@id/tv_like"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_like"
      style="@style/Text.12sp.767676"
      android:drawableStart="@{data.isZan==0?@drawable/ic_like:@drawable/ic_liked}"
      android:drawablePadding="@dimen/dp_3"
      android:gravity="center_vertical"
      android:text="@{data.zanCount==0?@string/empty:String.valueOf(data.zanCount)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_nick_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_nick_name" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.16sp.000000"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_10"
      android:text="@{HtmlUtils.fromHtml(data.content)}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_nick_name"
      app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

    <TextView
      android:id="@+id/tv_release_time"
      style="@style/Text.11sp.333333"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.date}"
      app:layout_constraintStart_toStartOf="@id/tv_content"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <TextView
      android:id="@+id/tv_tag"
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@string/common_point"
      android:textColor="@color/cl_333333"
      app:layout_constraintBottom_toBottomOf="@id/tv_release_time"
      app:layout_constraintStart_toEndOf="@id/tv_release_time"
      app:layout_constraintTop_toTopOf="@id/tv_release_time" />

    <TextView
      android:id="@+id/tv_reply_count"
      style="@style/Text.11sp.333333"
      android:layout_marginStart="@dimen/dp_8"
      android:background="@{data.hfCount&gt;0?@drawable/bg_f3f5f8_round:@drawable/bg_empty}"
      android:paddingStart="@{data.hfCount&gt;0?@dimen/dp_12:@dimen/dp_0}"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{@string/comment_reply_counts_format(data.hfCount&gt;0?String.valueOf(data.hfCount):@string/empty)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_release_time"
      app:layout_constraintStart_toEndOf="@id/tv_tag"
      app:layout_constraintTop_toTopOf="@id/tv_release_time" />

    <LinearLayout
      android:id="@+id/ll_reply"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/dp_8"
      android:background="@color/common_fafafa"
      android:orientation="vertical"
      android:paddingStart="@dimen/dp_8"
      android:paddingTop="@dimen/dp_10"
      android:paddingEnd="@dimen/dp_8"
      android:paddingBottom="@dimen/dp_10"
      android:visibility="@{data.hfCount>0?View.VISIBLE:View.GONE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_content"
      app:layout_constraintTop_toBottomOf="@id/tv_reply_count">

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_child_reply"
        style="@style/match_wrap" />

      <TextView
        style="@style/Text.12sp.4d8fcc"
        android:text="@{@string/common_view_all_reply_format(data.hfCount)}" />

    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_6"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/ll_reply"
      app:layout_goneMarginTop="@dimen/dp_8" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>