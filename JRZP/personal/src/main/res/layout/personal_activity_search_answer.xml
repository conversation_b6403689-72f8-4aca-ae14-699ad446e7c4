<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.searchanswer.SearchAnswerViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_back" />

            <com.bxkj.common.widget.MyEditText
                android:id="@+id/et_content"
                android:layout_width="0dp"
                android:layout_height="@dimen/common_dp_32"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_2"
                android:drawableStart="@drawable/common_ic_search"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center_vertical"
                android:hint="@string/invite_user_search_hint"
                android:imeOptions="actionSearch"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_12"
                android:text="@={viewModel.searchContent}"
                android:textSize="@dimen/sp_14" />

            <TextView
                style="@style/Text.15sp"
                android:layout_marginEnd="@dimen/dp_12"
                android:enabled="@{viewModel.searchContent.length()>0}"
                android:onClick="@{()->viewModel.startSearch()}"
                android:text="@string/search"
                android:textColor="@{viewModel.searchContent.length()>0?@color/cl_333333:@color/common_e8e8e8}" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Light" />

        <include
            layout="@layout/include_mvvm_refresh_layout"
            android:visibility="@{viewModel.showSearchResult?View.VISIBLE:View.GONE}"
            app:listViewModel="@{viewModel.searchResultListViewModel}" />
    </LinearLayout>
</layout>