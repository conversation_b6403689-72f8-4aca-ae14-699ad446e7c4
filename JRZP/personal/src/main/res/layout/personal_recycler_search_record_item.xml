<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_40">

    <View
        style="@style/Line.Horizontal"
        android:layout_marginStart="@dimen/dp_12" />

    <TextView
        android:id="@+id/tv_search_record_item"
        style="@style/Text.15sp.888888"
        android:layout_width="0dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_weight="1"
        android:drawablePadding="@dimen/dp_10"
        android:drawableStart="@drawable/personal_ic_search_record"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_delete_record"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_delete_record"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_12"
        android:src="@drawable/common_ic_small_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>


