<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.bxkj.common.util.HtmlUtils" />

        <variable
            name="data"
            type="com.bxkj.personal.data.VideoItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:paddingBottom="@dimen/dp_16"
        android:paddingTop="@dimen/dp_16"
        style="@style/match_wrap">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/news_video_cover_width"
            android:layout_height="@dimen/news_video_cover_height"
            android:layout_marginStart="@dimen/dp_14"
            android:scaleType="centerCrop"
            bind:imgUrl="@{data.pic}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/Text.10sp.FFFFFF"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_6"
            android:background="@color/cl_ff7405"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            android:text="@{data.typeName}"
            app:layout_constraintBottom_toBottomOf="@id/iv_cover"
            app:layout_constraintStart_toStartOf="@id/iv_cover" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_14"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{HtmlUtils.fromHtml(data.title)}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_cover"
            app:layout_constraintTop_toTopOf="@id/iv_cover" />

        <TextView
            android:id="@+id/tv_author"
            style="@style/Text.14sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_14"
            android:text="@{data.userName}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.12sp.999999"
            android:text="@{data.date}"
            app:layout_constraintBottom_toBottomOf="@id/iv_cover"
            app:layout_constraintStart_toStartOf="@id/tv_title" />

        <TextView
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@{@string/video_list_play_count_format(data.count)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_date"
            app:layout_constraintStart_toEndOf="@id/tv_date" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>