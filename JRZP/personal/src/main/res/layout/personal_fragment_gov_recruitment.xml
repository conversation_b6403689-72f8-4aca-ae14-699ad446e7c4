<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.fragment.home.GovRecruitmentViewModel" />
    </data>

    <LinearLayout style="@style/match_match"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:background="@color/cl_ff7405"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/common_ic_white_back" />

            <LinearLayout
                android:id="@+id/ll_search_bar"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/dp_6"
                android:layout_marginEnd="@dimen/common_dp_20"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_weight="1"
                android:background="@drawable/bg_ffffff_radius_4"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingEnd="@dimen/dp_12"
                android:paddingStart="@dimen/dp_12">

                <ImageView
                    style="@style/wrap_wrap"
                    android:src="@drawable/ic_home_search" />

                <com.bxkj.common.widget.marquee.MarqueeView
                    android:id="@+id/marquee_hot_keyword"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tv_search_job"
                    style="@style/Text.16sp.767676"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:onClick="@{onClickListener}"
                    android:text="@string/search_job_hint"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/iv_scan"
                    style="@style/wrap_wrap"
                    android:layout_marginStart="@dimen/dp_12"
                    android:onClick="@{onClickListener}"
                    android:src="@drawable/ic_scan_red" />

            </LinearLayout>

            <!--      <TextView-->
            <!--        android:id="@+id/tv_post"-->
            <!--        style="@style/Text.12sp.FFFFFF"-->
            <!--        android:layout_gravity="center_vertical"-->
            <!--        android:layout_marginStart="@dimen/dp_14"-->
            <!--        android:layout_marginEnd="@dimen/dp_14"-->
            <!--        android:drawableTop="@drawable/ic_home_camera"-->
            <!--        android:onClick="@{onClickListener}"-->
            <!--        android:text="@string/personal_publish" />-->

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_indicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36">

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/indicator"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp_4"
                android:layout_marginStart="@dimen/dp_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/v_bottom_line"
                style="@style/Line.Horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_news_list"
            style="@style/match_match" />

    </LinearLayout>
</layout>