<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="data"
            type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
    </data>


    <LinearLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_marginBottom="@dimen/dp_8"
        android:orientation="vertical">

        <include layout="@layout/layout_conversation_time_tag" />

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="horizontal"
            android:visibility="@{data.sender?View.GONE:View.VISIBLE}">

            <de.hdodenhof.circleimageview.CircleImageView
                android:layout_width="@dimen/conversation_msg_avatar_size"
                android:layout_height="@dimen/conversation_msg_avatar_size"
                bind:imgUrl="@{data.myAvatar}" />

            <LinearLayout
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/common_dp_60"
                android:layout_marginStart="@dimen/dp_6"
                android:background="@drawable/chat_bg_feature_item"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:padding="@dimen/dp_12">

                <TextView
                    android:id="@+id/tv_content"
                    style="@style/Text.16sp.333333.Bold"
                    android:layout_width="match_parent"
                    android:drawablePadding="@dimen/dp_8"
                    android:drawableStart="@drawable/chat_ic_msg_exchange_wechat"
                    android:gravity="center_vertical"
                    android:text="@{CheckUtils.isNullOrEmpty(data.content)?@string/chat_invite_send_resume:data.content}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    style="@style/match_wrap"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/v_split">

                    <TextView
                        android:id="@+id/tv_reject"
                        style="@style/Text.14sp.333333"
                        android:layout_weight="1"
                        android:background="@drawable/chat_bg_btn_option_cancel"
                        android:enabled="@{data.requestUndisposed()}"
                        android:gravity="center"
                        android:paddingBottom="@dimen/dp_8"
                        android:paddingTop="@dimen/dp_8"
                        android:text="@string/chat_request_reject"
                        android:textColor="@color/chat_button_text_selector" />

                    <TextView
                        android:id="@+id/tv_accept"
                        style="@style/Text.14sp.333333"
                        android:layout_marginStart="@dimen/dp_10"
                        android:layout_weight="1"
                        android:background="@drawable/chat_bg_btn_option_confirm"
                        android:enabled="@{data.requestUndisposed()}"
                        android:gravity="center"
                        android:paddingBottom="@dimen/dp_8"
                        android:paddingTop="@dimen/dp_8"
                        android:text="@string/chat_request_agree"
                        android:textColor="@color/chat_button_text_selector" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</layout>