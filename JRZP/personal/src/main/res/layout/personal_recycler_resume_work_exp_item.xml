<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.WorkExpItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <ImageView
            android:id="@+id/iv_date_tag"
            android:layout_width="@dimen/common_dp_5"
            android:layout_height="@dimen/common_dp_5"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginTop="@dimen/dp_22"
            android:src="@drawable/personal_ic_work_exp_date_tag"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            style="@style/Line.Vertical"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/iv_date_tag"
            app:layout_constraintStart_toStartOf="@id/iv_date_tag"
            app:layout_constraintTop_toBottomOf="@id/iv_date_tag" />

        <ImageView
            android:id="@+id/iv_edit"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:scaleType="centerInside"
            android:src="@drawable/personal_ic_recommend_job_edit"
            app:layout_constraintBottom_toBottomOf="@id/iv_date_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_date_tag" />

        <TextView
            android:id="@+id/tv_work_time"
            style="@style/Text.16sp.888888"
            android:layout_marginStart="@dimen/dp_10"
            android:text="@{data.dateRangeText}"
            app:layout_constraintBottom_toBottomOf="@id/iv_date_tag"
            app:layout_constraintStart_toEndOf="@id/iv_date_tag"
            app:layout_constraintTop_toTopOf="@id/iv_date_tag" />

        <TextView
            android:id="@+id/tv_company_and_position"
            style="@style/Text.16sp.333333"
            android:layout_marginTop="@dimen/common_dp_5"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.companyAndJobText}"
            app:layout_constraintStart_toStartOf="@id/tv_work_time"
            app:layout_constraintTop_toBottomOf="@id/tv_work_time" />

        <TextView
            android:id="@+id/tv_job_responsibility_title"
            style="@style/Text.14sp.888888"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/personal_job_responsibility"
            app:layout_constraintStart_toStartOf="@id/tv_company_and_position"
            app:layout_constraintTop_toBottomOf="@id/tv_company_and_position" />

        <TextView
            android:id="@+id/tv_job_responsibility"
            style="@style/Text.16sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_10"
            android:ellipsize="end"
            android:maxLines="2                       "
            android:text="@{data.des}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_job_responsibility_title"
            app:layout_constraintTop_toBottomOf="@id/tv_job_responsibility_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>