<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.personal.data.NewsItemData" />
  </data>

  <!--资讯列表视频分类-->
  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.17sp.000000"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_16"
      android:ellipsize="end"
      android:lineSpacingExtra="@dimen/dp_3"
      android:maxLines="2"
      android:text="@{data.title}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
      android:id="@+id/fl_video_group"
      android:layout_width="match_parent"
      android:layout_height="194dp"
      android:layout_marginTop="@dimen/dp_6"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title">

      <com.bxkj.personal.weight.videoplaylayout.VideoPlayLayout
        android:id="@+id/fl_video_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="@{data.subType==4?View.GONE:View.VISIBLE}"
        app:cover="@{data.listMedia[0].url}"
        app:showPlayCount="false"
        bind:videoUrl="@{data.listMedia[1].url}" />

      <ImageView
        android:id="@+id/iv_cover"
        style="@style/match_match"
        android:scaleType="centerCrop"
        android:visibility="@{data.subType==4?View.VISIBLE:View.GONE}"
        bind:imgUrl="@{data.listMedia[0].url}" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_gravity="center"
        android:src="@drawable/ic_play_video"
        android:visibility="@{data.subType==4?View.VISIBLE:View.GONE}" />
    </FrameLayout>

    <TextView
      android:id="@+id/tv_author"
      style="@style/Text.12sp.999999"
      android:layout_marginTop="@dimen/dp_8"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.subType==4?data.companyName:data.userName}"
      app:layout_constrainedWidth="true"
      app:layout_constraintEnd_toStartOf="@id/tv_comment_count"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintHorizontal_chainStyle="packed"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/fl_video_group" />

    <TextView
      android:id="@+id/tv_comment_count"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{data.subType==4?@string/home_news_video_play_count_format(data.count):@string/home_news_comment_count_format(data.commentsCount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_author"
      app:layout_constraintEnd_toStartOf="@id/tv_publish_time"
      app:layout_constraintStart_toEndOf="@id/tv_author" />

    <TextView
      android:id="@+id/tv_publish_time"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:text="@{data.createTime}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_comment_count"
      app:layout_constraintEnd_toStartOf="@id/tv_sign_up"
      app:layout_constraintStart_toEndOf="@id/tv_comment_count"
      app:layout_goneMarginEnd="@dimen/dp_12" />

    <TextView
      android:id="@+id/tv_sign_up"
      style="@style/Text.10sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_8"
      android:background="@color/common_fa3b3a"
      android:paddingStart="@dimen/dp_8"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_8"
      android:paddingBottom="@dimen/dp_2"
      android:text="@{HtmlUtils.fromHtml(@string/home_news_recruitment_type_text)}"
      android:visibility="@{data.subType==3?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_publish_time"
      app:layout_constraintEnd_toStartOf="@id/tv_tag"
      app:layout_constraintTop_toTopOf="@id/tv_publish_time" />

    <TextView
      android:id="@+id/tv_tag"
      style="@style/Text.10sp.FFFFFF"
      android:background="@color/cl_ff7405"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_2"
      android:text="@{data.subTypeName}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.subTypeName)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_publish_time"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_publish_time" />

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_8"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_author" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>