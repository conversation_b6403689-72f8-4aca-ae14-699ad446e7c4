<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.common.widget.filterpopup.FilterOptionData" />
    </data>

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_f4f4f4_to_fe6600_radius_2"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:padding="@dimen/dp_8"
        android:text="@{data.name}"
        android:textColor="@color/common_333333_to_ffffff_selector" />
</layout>