package com.bxkj.personal.ui.activity.lastjob

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityLatestJobBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant

/**
 * Description: 最新职位
 * Author:45457
 **/
@Route(path = LatestJobNavigation.PATH)
class LatestJobActivity : BaseDBActivity<PersonalActivityLatestJobBinding, LatestJobViewModel>() {

    override fun getViewModelClass(): Class<LatestJobViewModel> = LatestJobViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_latest_job

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupLatestJobList()

        subscribeViewModelEvent()

        viewModel.start()
    }

    private fun subscribeViewModelEvent() {
        viewModel.toCreateResumeCommand.observe(this, EventObserver {
            MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
        })
    }

    private fun setupLatestJobList() {
        viewBinding.includeLastJob.recyclerContent.apply {
            layoutManager = LinearLayoutManager(this@LatestJobActivity)
            addItemDecoration(
                LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_8)).drawHeader(true)
                    .drawFoot(true).build()
            )
        }
        val adapter = SimpleDBListAdapter<JobData>(this, R.layout.personal_recycler_latest_job_item).apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    data.get(position)?.let {
                        if (v.id == R.id.tv_contact) {
                            viewModel.sendResumePreCheck(it)
                        } else {
                            startActivity(JobDetailsActivityV2.newIntent(this@LatestJobActivity, it.id))
                        }
                    }
                }
            }, R.id.tv_contact)
        }
        viewModel.latestJobListViewModel.setAdapter(adapter)
    }
}