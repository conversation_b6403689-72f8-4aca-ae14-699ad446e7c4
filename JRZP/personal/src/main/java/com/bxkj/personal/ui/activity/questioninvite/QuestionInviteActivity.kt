package com.bxkj.personal.ui.activity.questioninvite

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.personal.R
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.personal.R.array
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.personal.databinding.PersonalActivityQuestionInviteBinding
import com.bxkj.personal.ui.activity.searchquestion.SearchQuestionActivity
import com.bxkj.personal.ui.fragment.questionrecommend.QuestionRecommendFragment
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.questioninvite
 * @Description: 问题邀请
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class QuestionInviteActivity :
  BaseDBActivity<PersonalActivityQuestionInviteBinding, BaseViewModel>(), View.OnClickListener {
  companion object {

    private const val EXTRA_LINK_QUESTION_ID = "LINK_QUESTION_ID"
    const val EXTRA_TO_RECOMMEND_PAGE = "TO_RECOMMEND_PAGE"

    fun newIntent(
      context: Context,
      linkQuestionId: Int? = CommonApiConstants.NO_ID,
      toRecommendPage: Boolean? = false
    ): Intent {
      return Intent(context, QuestionInviteActivity::class.java)
        .apply {
          putExtra(EXTRA_LINK_QUESTION_ID, linkQuestionId)
          putExtra(EXTRA_TO_RECOMMEND_PAGE, toRecommendPage)
        }
    }
  }

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_question_invite

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this
    setupIndicator()
    setupViewPager()
  }

  override fun onClick(v: View?) {
    v?.let {
      if (it.id == R.id.iv_back) {
        finish()
      } else {
        startActivity(SearchQuestionActivity.newIntent(this))
      }
    }
  }

  private fun setupViewPager() {
    val questionPagerAdapter =
      CommonPagerAdapter(
        supportFragmentManager, arrayListOf(
          QuestionRecommendFragment.newInstance(QuestionRecommendFragment.TYPE_INVITE),
          QuestionRecommendFragment.newInstance(
            QuestionRecommendFragment.TYPE_RECOMMEND,
            intent.getIntExtra(EXTRA_LINK_QUESTION_ID, CommonApiConstants.NO_ID)
          )
        )
      )
    viewBinding.vpContent.adapter = questionPagerAdapter
    viewBinding.vpContent.offscreenPageLimit = 2
    if (intent.getBooleanExtra(EXTRA_TO_RECOMMEND_PAGE, false)) {
      viewBinding.vpContent.currentItem = 1
    }
  }

  private fun setupIndicator() {
    val commonNavigator = CommonNavigator(this)
    commonNavigator.isAdjustMode = true
    commonNavigator.adapter = MagicIndicatorAdapter(
      resources.getStringArray(array.question_type)
    )
      .apply {
        setOnTabClickListener(object :
          OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    viewBinding.indicator.navigator = commonNavigator
    ViewPagerHelper.bind(viewBinding.indicator, viewBinding.vpContent)
  }

}