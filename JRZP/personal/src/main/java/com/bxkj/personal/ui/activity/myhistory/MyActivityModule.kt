package com.bxkj.personal.ui.activity.myhistory

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.personal.ui.fragment.collection.CollectionFragment
import com.bxkj.personal.ui.fragment.comment.CommentFragment
import com.bxkj.personal.ui.fragment.like.LikeFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.my
 * @Description:
 * <AUTHOR>
 * @date 2020/2/12
 * @version V1.0
 */
@Module
abstract class MyActivityModule {

  @PerFragment
  @ContributesAndroidInjector
  abstract fun collectionFragment(): CollectionFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun commentFragment(): CommentFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun likeFragment(): LikeFragment
}