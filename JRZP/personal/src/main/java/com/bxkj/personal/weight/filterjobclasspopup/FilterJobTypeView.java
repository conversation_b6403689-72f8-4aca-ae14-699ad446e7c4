package com.bxkj.personal.weight.filterjobclasspopup;

import android.content.Context;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;

import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.recyclerutil.RecycleViewGridDivider;
import com.bxkj.personal.R;

import java.util.List;

/**
 * 职位分类筛选
 */
public class FilterJobTypeView extends ConstraintLayout {

    private JobFirstClassAdapter mJobFirstClassAdapter;
    private JobSecondClassAdapter mJobSecondClassAdapter;

    public FilterJobTypeView(Context context) {
        this(context, null);
    }

    public FilterJobTypeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterJobTypeView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.personal_layout_filter_by_job_class, this);

        mJobFirstClassAdapter = new JobFirstClassAdapter(getContext(), null, R.layout.personal_recycler_job_first_class_item);
        RecyclerView recyclerFirstClass = findViewById(R.id.recycler_first_class);
        recyclerFirstClass.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerFirstClass.setAdapter(mJobFirstClassAdapter);
        mJobFirstClassAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                mJobSecondClassAdapter.resetSelectPosition();
                mAreaItemClickListener.onFirstClassItemClicked(position);
            }
        });

        mJobSecondClassAdapter = new JobSecondClassAdapter(getContext(), null, R.layout.personal_recycler_job_second_class_item);
        RecyclerView recyclerSecondClass = findViewById(R.id.recycler_second_class);
        recyclerSecondClass.setLayoutManager(new GridLayoutManager(getContext(), 2));
        recyclerSecondClass.addItemDecoration(new RecycleViewGridDivider(DensityUtils.dp2px(getContext(), 10), ContextCompat.getColor(getContext(), R.color.common_white), true));
        recyclerSecondClass.setAdapter(mJobSecondClassAdapter);
        mJobSecondClassAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                mAreaItemClickListener.onSecondClassClicked(position);
            }
        });
    }

    /**
     * 设置一级数据
     *
     * @param optionsDataList
     */
    public void setFirstTypeData(List<JobTypeData> optionsDataList) {
        mJobFirstClassAdapter.setData(optionsDataList);
        mJobFirstClassAdapter.notifyDataSetChanged();
    }

    /**
     * 设置二级分类数据
     *
     * @param optionsDataList
     */
    public void setSecondTypeData(List<JobTypeData> optionsDataList) {
        mJobSecondClassAdapter.getLayoutManager().scrollToPosition(0);
        mJobSecondClassAdapter.setData(optionsDataList);
        mJobSecondClassAdapter.notifyDataSetChanged();
    }

    public List<JobTypeData> getFirstTypeData() {
        return mJobFirstClassAdapter.getData();
    }

    public List<JobTypeData> getSecondTypeData() {
        return mJobSecondClassAdapter.getData();
    }

    private FilterJobClassPopup.OnItemClickListener mAreaItemClickListener;

    public void setOnItemClickListener(FilterJobClassPopup.OnItemClickListener onItemClickListener) {
        mAreaItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onFirstClassItemClicked(int position);

        void onSecondClassClicked(int positions);
    }
}
