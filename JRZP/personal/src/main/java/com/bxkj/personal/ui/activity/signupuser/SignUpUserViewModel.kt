package com.bxkj.personal.ui.activity.signupuser

import android.app.Application
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/3/27
 * @version: V1.0
 */
class SignUpUserViewModel @Inject constructor(application: Application) : BaseViewModel() {

    val refreshPassedPageCommand = LiveEvent<Void>()
    val refreshOutPageCommand = LiveEvent<Void>()
}