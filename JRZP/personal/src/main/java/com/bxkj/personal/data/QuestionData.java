package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2020/2/25
 */
public class QuestionData extends BaseObservable implements Parcelable {
  /**
   * id : 3
   * type : 1
   * title : 这是一篇测试问题
   * content : 如何评价米家喷墨打印机？
   * count : 0
   * userID : 228609
   * userName : 吸猫的鱼
   * userPhoto : https://c-ssl.duitang.com/uploads/item/201802/21/20180221223815_xkkyq.thumb.700_0.jpg
   * userIntroduction : 测试测试
   * isStow : false
   * isFollowDW : false
   * isFollowUser : false
   * isGzIsLike : false
   * dwName : 安徽美术出版社
   * dwID : 6
   * dwTypeID : 1
   * dwTypeName : 出版社、报社
   * dwcityID : 10393
   * dwCity : 合肥
   * listMedia : null
   * listComments : null
   * commentsCount : 2
   * createTime : 2019.12.03 18:27:27
   */

  private int id;
  private int type;
  private String title;
  private String content;
  private int count;
  private int userID;
  private String userName;
  private String userPhoto;
  private String userIntroduction;
  private boolean isStow;
  private boolean isFollowDW;
  private boolean isFollowUser;
  private boolean isLike;
  private String dwName;
  private int dwID;
  private int dwTypeID;
  private String dwTypeName;
  private int dwcityID;
  private String dwCity;
  private Object listMedia;
  private Object listComments;
  private int commentsCount;
  private String createTime;
  private String shareContent;

  protected QuestionData(Parcel in) {
    id = in.readInt();
    type = in.readInt();
    title = in.readString();
    content = in.readString();
    count = in.readInt();
    userID = in.readInt();
    userName = in.readString();
    userPhoto = in.readString();
    userIntroduction = in.readString();
    isStow = in.readByte() != 0;
    isFollowDW = in.readByte() != 0;
    isFollowUser = in.readByte() != 0;
    isLike = in.readByte() != 0;
    dwName = in.readString();
    dwID = in.readInt();
    dwTypeID = in.readInt();
    dwTypeName = in.readString();
    dwcityID = in.readInt();
    dwCity = in.readString();
    commentsCount = in.readInt();
    createTime = in.readString();
    shareContent = in.readString();
  }

  public static final Creator<QuestionData> CREATOR = new Creator<QuestionData>() {
    @Override
    public QuestionData createFromParcel(Parcel in) {
      return new QuestionData(in);
    }

    @Override
    public QuestionData[] newArray(int size) {
      return new QuestionData[size];
    }
  };

  public String getShareContent() {
    return CheckUtils.isNullOrEmpty(content) ? title : content;
  }

  public void setShareContent(String shareContent) {
    this.shareContent = shareContent;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getUserID() {
    return userID;
  }

  public void setUserID(int userID) {
    this.userID = userID;
  }

  public String getUserName() {
    return CheckUtils.isNullOrEmpty(userName) ? CommonApiConstants.ACCOUNT_CLEAR_TIPS : userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getUserPhoto() {
    return userPhoto;
  }

  public void setUserPhoto(String userPhoto) {
    this.userPhoto = userPhoto;
  }

  public String getUserIntroduction() {
    return userIntroduction;
  }

  public void setUserIntroduction(String userIntroduction) {
    this.userIntroduction = userIntroduction;
  }

  @Bindable
  public boolean isIsStow() {
    return isStow;
  }

  public void setIsStow(boolean isStow) {
    this.isStow = isStow;
    notifyPropertyChanged(BR.isStow);
  }

  public boolean isIsFollowDW() {
    return isFollowDW;
  }

  public void setIsFollowDW(boolean isFollowDW) {
    this.isFollowDW = isFollowDW;
  }

  public boolean isIsFollowUser() {
    return isFollowUser;
  }

  public void setIsFollowUser(boolean isFollowUser) {
    this.isFollowUser = isFollowUser;
  }

  public boolean isIsLike() {
    return isLike;
  }

  public void setIsLike(boolean isLike) {
    this.isLike = isLike;
  }

  public String getDwName() {
    return dwName;
  }

  public void setDwName(String dwName) {
    this.dwName = dwName;
  }

  public int getDwID() {
    return dwID;
  }

  public void setDwID(int dwID) {
    this.dwID = dwID;
  }

  public int getDwTypeID() {
    return dwTypeID;
  }

  public void setDwTypeID(int dwTypeID) {
    this.dwTypeID = dwTypeID;
  }

  public String getDwTypeName() {
    return dwTypeName;
  }

  public void setDwTypeName(String dwTypeName) {
    this.dwTypeName = dwTypeName;
  }

  public int getDwcityID() {
    return dwcityID;
  }

  public void setDwcityID(int dwcityID) {
    this.dwcityID = dwcityID;
  }

  public String getDwCity() {
    return dwCity;
  }

  public void setDwCity(String dwCity) {
    this.dwCity = dwCity;
  }

  public Object getListMedia() {
    return listMedia;
  }

  public void setListMedia(Object listMedia) {
    this.listMedia = listMedia;
  }

  public Object getListComments() {
    return listComments;
  }

  public void setListComments(Object listComments) {
    this.listComments = listComments;
  }

  public int getCommentsCount() {
    return commentsCount;
  }

  public void setCommentsCount(int commentsCount) {
    this.commentsCount = commentsCount;
  }

  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public void addCollect() {
    setIsStow(true);
  }

  public void removeCollect() {
    setIsStow(false);
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(id);
    dest.writeInt(type);
    dest.writeString(title);
    dest.writeString(content);
    dest.writeInt(count);
    dest.writeInt(userID);
    dest.writeString(userName);
    dest.writeString(userPhoto);
    dest.writeString(userIntroduction);
    dest.writeByte((byte) (isStow ? 1 : 0));
    dest.writeByte((byte) (isFollowDW ? 1 : 0));
    dest.writeByte((byte) (isFollowUser ? 1 : 0));
    dest.writeByte((byte) (isLike ? 1 : 0));
    dest.writeString(dwName);
    dest.writeInt(dwID);
    dest.writeInt(dwTypeID);
    dest.writeString(dwTypeName);
    dest.writeInt(dwcityID);
    dest.writeString(dwCity);
    dest.writeInt(commentsCount);
    dest.writeString(createTime);
    dest.writeString(shareContent);
  }
}
