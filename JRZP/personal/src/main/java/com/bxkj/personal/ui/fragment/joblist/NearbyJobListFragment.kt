package com.bxkj.personal.ui.fragment.joblist

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.PagingViewHolder
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.util.location.ZPLocation
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CRecyclerJobItemBinding
import com.bxkj.personal.databinding.PersonalFragmentNearbyJobListBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.fragment.homev3.GeekHomeFragmentV3
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 *
 * @author: sanjin
 * @date: 2022/8/17
 */
class NearbyJobListFragment :
  BaseDBFragment<PersonalFragmentNearbyJobListBinding, NearbyJobListViewModel>(), JobListPage {

  private var _nearbyJobListAdapter: SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>? = null

  override fun getViewModelClass(): Class<NearbyJobListViewModel> =
    NearbyJobListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_nearby_job_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupJobList()

    subscribeViewModelEvent()

    checkHasLocationPermission()
  }

  private fun subscribeViewModelEvent() {
    viewModel.refreshListCommand.observe(this, EventObserver {
      _nearbyJobListAdapter?.refresh()
    })

    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })
  }

  private fun checkHasLocationPermission() {
    if (XXPermissions.isGranted(requireActivity(), Permission.ACCESS_FINE_LOCATION)) {
      LocationManager.requestLocation(
        requireActivity(),
        object : LocationManager.OnLocationListener {
          override fun onSuccess(location: ZPLocation) {
            viewBinding.pslContent.show(LoadingPageState::class.java)
            viewModel.getNearbyJobByLocation(location)
          }

          override fun onFailed() {
            showNoLocatePermissionLayout()
          }
        })
    } else {
      showNoLocatePermissionLayout()
      finishParentLoadState()
    }
  }

  private fun showNoLocatePermissionLayout() {
    viewBinding.pslContent.show(
      ErrorPageState::class.java,
      object : OnStateSetUpListener<ErrorPageState> {
        override fun onStateSetUp(pageState: ErrorPageState) {
          pageState.apply {
            setImage(R.drawable.ic_no_location_permission)
            setContent(getString(R.string.news_no_location_permission_tips))
            setNextOptionText(getString(R.string.news_open_location_btn_text))
            setNextOptionClickListener {
              getLocationAndRefreshJobList()
            }
          }
        }
      })
  }

  private fun getLocationAndRefreshJobList() {
    activity?.let { notNullActivity ->
      viewBinding.pslContent.show(LoadingPageState::class.java)
      LocationManager.getLocationInfo(
        notNullActivity,
        getString(R.string.permission_tips_title),
        getString(R.string.home_location_permission_desc),
        object : LocationManager.OnLocationListener {
          override fun onSuccess(location: ZPLocation) {
            viewModel.getNearbyJobByLocation(location)
          }

          override fun onFailed() {
            viewBinding.pslContent.show(
              ErrorPageState::class.java,
              object : OnStateSetUpListener<ErrorPageState> {
                override fun onStateSetUp(pageState: ErrorPageState) {
                  pageState.apply {
                    setImage(R.drawable.ic_no_location_permission)
                    setContent(getString(R.string.news_no_location_permission_tips))
                    setNextOptionText(getString(R.string.news_open_location_btn_text))
                    setNextOptionClickListener { showNoLocatePermissionLayout() }
                  }
                }
              })
          }
        })
    } ?: { showToast("组件加载异常，请重启应用后重试") }
  }

  override fun refreshPage() {
    checkHasLocationPermission()
  }

  private fun setupJobList() {
    _nearbyJobListAdapter =
      object :SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>(
        R.layout.c_recycler_job_item,
        JobData.DiffCallback()
      ){
        override fun onBindViewHolder(
          holder: PagingViewHolder<CRecyclerJobItemBinding>,
          position: Int
        ) {
          super.onBindViewHolder(holder, position)
          holder.viewBinding.tvRealJobTag.visibility = View.GONE
        }
      }.apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getItemBean(position)?.let {
              if (v.id == R.id.tv_application) {
                viewModel.sendResumePreCheck(it)
              } else {
                startActivity(JobDetailsActivityV2.newIntent(requireContext(), it.id))
              }
            }
          }
        }, R.id.tv_application)
        addLoadStateListener { loadStateFlow ->
          loadStateFlow.handleState({
            finishParentLoadState()
            viewBinding.pslContent.hidden()
          }, {
            finishParentLoadState()
            viewBinding.pslContent.show(
              EmptyPageState::class.java,
              object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                  pageState.setContent("未查到数据")
                }
              })
          }, {
            finishParentLoadState()
            viewBinding.pslContent.show(
              ErrorPageState::class.java,
              object : OnStateSetUpListener<ErrorPageState> {
                override fun onStateSetUp(pageState: ErrorPageState) {
                  pageState.setContent(it.message.getOrDefault())
                  pageState.setNextOptionClickListener { refresh() }
                }
              })
          })
        }
      }
    viewBinding.recyclerContent.apply {
      closeDefaultAnim()
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_6))
          .drawFoot(true)
          .build()
      )
      adapter = _nearbyJobListAdapter?.withLoadStateFooter(LoadStateFooterAdapter {
        _nearbyJobListAdapter?.refresh()
      })
    }

    lifecycleScope.launch {
      viewModel.nearbyJobListFlow.collectLatest {
        _nearbyJobListAdapter?.submitData(it)
      }
    }
  }

  private fun finishParentLoadState() {
    if (parentFragment is GeekHomeFragmentV3) {
      (parentFragment as GeekHomeFragmentV3).finishRefresh()
    }
  }
}