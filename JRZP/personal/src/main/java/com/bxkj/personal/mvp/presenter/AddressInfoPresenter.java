package com.bxkj.personal.mvp.presenter;


import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.mvp.contract.AddressInfoContract;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: AddressId
 * @TODO: TODO
 * @date 2018/3/27
 */

public class AddressInfoPresenter extends AddressInfoContract.Presenter {

  private static final String TAG = AddressInfoPresenter.class.getSimpleName();
  private PersonalApi mPersonalApi;

  @Inject
  public AddressInfoPresenter(PersonalApi accountApi) {
    mPersonalApi = accountApi;
  }

  @Override
  public void getAddressInfoByCityName(int addressType, String addressName) {
    mPersonalApi.getAddressInfoByAddressName(addressType, addressName, CommonApiConstants.NO_ID)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            if (baseResponse.getData() != null) {
              mView.getAddressInfoSuccess((AreaOptionsData) baseResponse.getData());
            }
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }
}
