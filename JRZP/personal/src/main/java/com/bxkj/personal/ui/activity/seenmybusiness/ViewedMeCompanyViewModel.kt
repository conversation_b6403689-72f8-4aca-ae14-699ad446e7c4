package com.bxkj.personal.ui.activity.seenmybusiness

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.personal.data.SeenMeBusinessData
import com.bxkj.personal.data.source.CompanyRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class ViewedMeCompanyViewModel @Inject constructor(
    private val _companyRepo: CompanyRepo,
    private val chatRepo: ChatRepo
) : BaseViewModel() {

    val seenMeBusinessListViewModel = RefreshListViewModel()

    init {
        seenMeBusinessListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                _companyRepo.getSeenMeBusinessList(getSelfUserID(), currentPage, 20)
                    .handleResult({
                        seenMeBusinessListViewModel.autoAddAll(it)
                    }, {
                        if (it.isNoDataError) {
                            seenMeBusinessListViewModel.noMoreData()
                        } else {
                            seenMeBusinessListViewModel.loadError()
                        }
                    })
            }
        }
    }

    fun start() {
        seenMeBusinessListViewModel.refresh()
    }

    fun setAllMsgHasRead() {
        viewModelScope.launch {
            showLoading()
            _companyRepo.setAllSeenMeMsgHasRead()
                .handleResult({
                    seenMeBusinessListViewModel.refresh()
                    RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_RECEIVE_NEW_MSG))
                    showToast("设置成功")
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun setupMsgHasRead(msgId: Int) {
        viewModelScope.launch {
            chatRepo.setupMsgHasRead(msgId)
                .handleResult({
                    if (seenMeBusinessListViewModel.data?.filter { (it as SeenMeBusinessData).isViewed }?.size.getOrDefault() == 0) {
                        RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_RECEIVE_NEW_MSG))
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }
}