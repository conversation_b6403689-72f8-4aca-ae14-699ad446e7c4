package com.bxkj.personal.ui.activity.feedbackmsgdetails;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.personal.R;
import com.bxkj.personal.data.FeedbackMsgItemData;
import com.bxkj.personal.mvp.contract.InvitationDeliveryContract;
import com.bxkj.personal.mvp.presenter.InvitationDeliveryPresenter;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.feedbackmsgdetails
 * @Description: 投递反馈详情
 * @TODO: TODO
 * @date 2018/8/30
 */
public class FeedbackMsgDetailsActivity extends BaseDaggerActivity
  implements FeedbackMsgDetailsContract.View, InvitationDeliveryContract.View {

  private static final String MSG_DETAILS = "msg_details";

  @Inject
  FeedbackMsgDetailsPresenter mFeedbackMsgDetailsPresenter;
  @Inject
  InvitationDeliveryPresenter mInvitationDeliveryPresenter;

  private TextView tvDate;
  private TextView tvContent;
  private RelativeLayout rlConfirmBar;
  private TextView tvConfirm;
  private RelativeLayout rlCancelOrAcceptBar;
  private TextView tvCancel;
  private TextView tvAccept;

  private FeedbackMsgItemData mFeedbackMsgItemData;

  public static void start(Context context, FeedbackMsgItemData feedBackMsgItemData) {
    Intent starter = new Intent(context, FeedbackMsgDetailsActivity.class);
    Bundle bundle = new Bundle();
    bundle.putParcelable(MSG_DETAILS, feedBackMsgItemData);
    starter.putExtras(bundle);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_feedback_msg_details;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mFeedbackMsgDetailsPresenter);
    presenters.add(mInvitationDeliveryPresenter);
    return presenters;
  }

  @Override
  protected void initIntent(Intent intent) {
    mFeedbackMsgItemData = intent.getParcelableExtra(MSG_DETAILS);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(mFeedbackMsgItemData.getCompany().getName());
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mFeedbackMsgDetailsPresenter.updateFeedbackMsgState(mFeedbackMsgItemData.getId());

    //        tvDate.setText(mFeedbackMsgItemData.getDate());
    //        if (mFeedbackMsgItemData.getRelId() != CommonApiConstants.NO_ID) {
    //            setContextAndClickListener();
    //        } else {
    //            tvContent.setText(mFeedbackMsgItemData.getContent());
    //        }
    //
    //        if (mFeedbackMsgItemData.getType() == 4) {         //邀請投遞
    //            if (mFeedbackMsgItemData.getState() == 0) { //未处理
    //                rlCancelOrAcceptBar.setVisibility(View.VISIBLE);
    //                tvCancel.setText(getString(R.string.personal_refusing_to_deliver));
    //                tvAccept.setText(getString(R.string.personal_deliver_now));
    //            } else {  //已处理
    //                rlConfirmBar.setVisibility(View.VISIBLE);
    //                tvConfirm.setText(mFeedbackMsgItemData.getState() == 1 ? getString(R.string.personal_refuse_to_deliver_tips) : getString(R.string.personal_accept_deliver_tips));
    //            }
    //        } else if (mFeedbackMsgItemData.getType() == 5) {      //邀請面試
    //            if (mFeedbackMsgItemData.getState() == 0) {     //未处理
    //                rlCancelOrAcceptBar.setVisibility(View.VISIBLE);
    //                tvCancel.setText(getString(R.string.personal_refusing_an_interview));
    //                tvAccept.setText(getString(R.string.personal_accept_an_interview));
    //            } else {  //已处理
    //                rlConfirmBar.setVisibility(View.VISIBLE);
    //                tvConfirm.setText(mFeedbackMsgItemData.getState() == 1 ? getString(R.string.personal_refuse_interview_tips) : getString(R.string.personal_accept_interview_tips));
    //            }
    //        }
  }

  /**
   * 设置消息内容和点击职位跳转事件
   */
  private void setContextAndClickListener() {
    SpannableStringBuilder builder = new SpannableStringBuilder(mFeedbackMsgItemData.getContent());
    builder.append(getString(R.string.personal_check_the_job_position));
    ClickableSpan clickableSpan = new ClickableSpan() {
      @Override
      public void onClick(View view) {
        //                startActivity(JobDetailsActivityV2.newIntent(FeedbackMsgDetailsActivity.this, mFeedbackMsgItemData.getRelId()));
      }
    };
    builder.setSpan(clickableSpan, builder.length() - 4, builder.length(),
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(getMColor(R.color.cl_ff7405));
    builder.setSpan(foregroundColorSpan, builder.length() - 4, builder.length(),
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    tvContent.setMovementMethod(LinkMovementMethod.getInstance());
    tvContent.setText(builder);
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_cancel) {
      if (mFeedbackMsgItemData.getType() == 4) {
        mInvitationDeliveryPresenter.refuseDeliveryInvitation(getMUserID(),
          mFeedbackMsgItemData.getOtherid(), CommonApiConstants.NO_ID);
      } else {
        mFeedbackMsgDetailsPresenter.refuseInterviewInvitation(getMUserID(),
          mFeedbackMsgItemData.getOtherid());
      }
    } else {
      if (mFeedbackMsgItemData.getType() == 4) {
        mInvitationDeliveryPresenter.acceptDeliveryInvitation(getMUserID(),
          mFeedbackMsgItemData.getOtherid(), CommonApiConstants.NO_ID);
      } else {
        mFeedbackMsgDetailsPresenter.acceptInterviewInvitation(getMUserID(),
          mFeedbackMsgItemData.getOtherid());
      }
    }
  }

  @Override
  public void acceptSuccess() {
    showToast(getString(R.string.common_successful));
    finish();
  }

  @Override
  public void acceptDeliverySuccess(int position) {
    showToast(getString(R.string.common_successful));
    finish();
  }

  @Override
  public void refuseDeliverySuccess(int position) {
    showToast(getString(R.string.common_successful));
    finish();
  }

  private void bindView(View bindSource) {
    tvDate = bindSource.findViewById(R.id.tv_date);
    tvContent = bindSource.findViewById(R.id.tv_content);
    rlConfirmBar = bindSource.findViewById(R.id.rl_confirm_bar);
    tvConfirm = bindSource.findViewById(R.id.tv_confirm);
    rlCancelOrAcceptBar = bindSource.findViewById(R.id.rl_cancel_or_accept_bar);
    tvCancel = bindSource.findViewById(R.id.tv_cancel);
    tvAccept = bindSource.findViewById(R.id.tv_accept);
    bindSource.findViewById(R.id.tv_cancel).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_accept).setOnClickListener(v -> onViewClicked(v));
  }
}
