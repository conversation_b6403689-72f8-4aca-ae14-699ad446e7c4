package com.bxkj.personal.ui.fragment.campusrecruit

import android.os.Bundle
import android.view.View
import android.view.View.GONE
import android.view.View.OnClickListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup.OnMultiSelectListener
import com.bxkj.common.widget.filterpopup.FilterView
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.personal.R
import com.bxkj.personal.data.CampusRecruitData
import com.bxkj.personal.databinding.PersonalFragmentCampusRecruitListBinding
import com.bxkj.personal.ui.activity.campusrecruitdetails.CampusRecruitDetailsActivity
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Description: 校招列表
 */
class CampusRecruitListFragment :
    BaseDBFragment<PersonalFragmentCampusRecruitListBinding, CampusRecruitListViewModel>(), OnClickListener{

    companion object {

        fun newInstance(): Fragment {
            return CampusRecruitListFragment()
        }
    }

    private var _filterCateDownPopup: DropDownPopup? = null
    private var _filterCityDownPopup: DropDownPopup? = null
    private var _filterCategoryView: FilterView? = null
    private var _filterCityView: FilterView? = null

    private var _campusRecruitListAdapter: SimplePageDataAdapter<CampusRecruitData, PersonalFragmentCampusRecruitListBinding>? =
        null

    override fun getViewModelClass(): Class<CampusRecruitListViewModel> =
        CampusRecruitListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_fragment_campus_recruit_list

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        setupFilterDropDownPopup()
        setupContentList()
        setupRefreshLayout()

        subscribeViewModelEvent()

        viewModel.start()
    }

    override fun onClick(v: View?) {
        v?.let {
            when (v.id) {
                R.id.tv_category -> {
                    _filterCateDownPopup?.showAsDropDown()
                }

                R.id.tv_area -> {
                    _filterCityDownPopup?.showAsDropDown()
                }

                else -> {
                }
            }
        }
    }

    private fun subscribeViewModelEvent() {
        (parentFragment as? CampusRecruitFragment)?.let {
            it.searchKeyword.observe(this, Observer { keyword ->
                viewModel.setSearchKeyword(keyword)
                refreshList()
            })
        }

        viewModel.jobCateList.observe(this) {
            _filterCategoryView?.setData(listOf(FilterOptionsGroup(0, it)))
        }

        viewModel.cityList.observe(this) {
            _filterCityView?.setData(
                listOf(
                    FilterOptionsGroup(
                        0,
                        it,
                        openMultiSelect = true,
                        onMultiSelectListener = object : OnMultiSelectListener<FilterOptionData> {
                            override fun onMultiSelect(filterOption: List<FilterOptionData>) {
                                viewModel.changeJobCity(filterOption.map { it.id })
                            }
                        })
                )
            )
        }
    }

    private fun setupFilterDropDownPopup() {
        _filterCategoryView = FilterView.Builder(requireContext())
            .setHeight(dip(300))
            .setBottomBarVisible(GONE)
            .setItemClickedDismiss(true)
            .setOnFilterConfirmListener { selected ->
                _filterCateDownPopup?.close()
                viewModel.changeJobCategory(selected[0].getOrDefault())
                refreshList()
            }
            .build()

        _filterCityView = FilterView.Builder(requireContext())
            .setHeight(dip(300))
            .setOnFilterConfirmListener {
                _filterCityDownPopup?.close()
                refreshList()
            }
            .build()

        _filterCateDownPopup = DropDownPopup(requireActivity(), viewBinding.llFilterBar).apply {
            isOutsideTouchable = true
            addContentViews(_filterCategoryView)
            setOnItemExpandStatusChangeListener { _, opened ->
                viewBinding.tvCategory.isSelected = opened
            }
        }

        _filterCityDownPopup = DropDownPopup(requireActivity(), viewBinding.llFilterBar).apply {
            isOutsideTouchable = true
            addContentViews(_filterCityView)
            setOnItemExpandStatusChangeListener { _, opened ->
                viewBinding.tvArea.isSelected = opened
            }
        }
    }

    private fun setupRefreshLayout() {
        viewBinding.srlContent.setOnRefreshListener {
            _campusRecruitListAdapter?.refresh()
        }
    }

    private fun setupContentList() {
        viewBinding.pslContainer.show(LoadingPageState::class.java)
        _campusRecruitListAdapter =
            SimplePageDataAdapter<CampusRecruitData, PersonalFragmentCampusRecruitListBinding>(
                R.layout.personal_list_campus_recurit_item,
                CampusRecruitData.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getItemBean(position)?.let {
                            startActivity(CampusRecruitDetailsActivity.newInstance(requireContext(), it.id))
                        }
                    }
                })
                addLoadStateListener { loadStateFlow ->
                    loadStateFlow.handleState(notLoading = {
                        viewBinding.srlContent.finishRefresh()
                        viewBinding.pslContainer.hidden()
                    }, noData = {
                        viewBinding.pslContainer.show(
                            EmptyPageState::class.java,
                            object : OnStateSetUpListener<EmptyPageState> {
                                override fun onStateSetUp(pageState: EmptyPageState) {
                                    pageState.setContent("未查到数据")
                                }
                            })
                    }, error = {
                        viewBinding.pslContainer.show(
                            ErrorPageState::class.java,
                            object : OnStateSetUpListener<ErrorPageState> {
                                override fun onStateSetUp(pageState: ErrorPageState) {
                                    pageState.setContent(it.message.getOrDefault())
                                    pageState.setNextOptionClickListener { refreshList() }
                                }
                            })
                    })
                }
            }

        viewBinding.recyclerCampusRecruit.apply {
            closeDefaultAnim()
            layoutManager = LinearLayoutManager(requireContext())
            adapter = _campusRecruitListAdapter?.withLoadStateFooter(LoadStateFooterAdapter {
                _campusRecruitListAdapter?.retry()
            })
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_8))
                    .build()
            )
        }

        lifecycleScope.launch {
            viewModel.campusRecruitListFlow.collectLatest {
                _campusRecruitListAdapter?.submitData(it)
            }
        }
    }

    private fun refreshList() {
        viewBinding.pslContainer.show(LoadingPageState::class.java)
        _campusRecruitListAdapter?.refresh()
    }
}