package com.bxkj.personal.ui.activity.editinfo

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/25
 * @version: V1.0
 */
class EditInfoNavigation {
  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/editinfo"

    const val PAGE_TITLE = "pageTitle"
    const val EDIT_HINT = "editHint"
    const val EDIT_CONTENT = "editContent"
    const val MAX_LENGTH = "maxNumber"

    const val EXTRA_RESULT_TEXT = "resultText"

    fun navigate(
      pageTitle: String,
      hint: String = "",
      content: String = "",
      maxLength: Int = 2000
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(PAGE_TITLE, pageTitle)
        .withString(EDIT_HINT, hint)
        .withString(EDIT_CONTENT, content)
        .withInt(MAX_LENGTH, maxLength)
    }
  }
}