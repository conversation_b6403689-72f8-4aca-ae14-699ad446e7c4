package com.bxkj.personal.mvp.presenter;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.mvp.contract.InvitationDeliveryContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: InvitationDelivery
 * @TODO: TODO
 * @date 2018/3/27
 */

public class InvitationDeliveryPresenter extends InvitationDeliveryContract.Presenter {

    private static final String TAG = InvitationDeliveryPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public InvitationDeliveryPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void acceptDeliveryInvitation(int userId, int otherId, int position) {
        mPersonalApi.acceptDeliveryInvitation(userId, otherId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.acceptDeliverySuccess(position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void refuseDeliveryInvitation(int userId, int otherId, int position) {
        mPersonalApi.refuseDeliveryInvitation(userId, otherId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.refuseDeliverySuccess(position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
