package com.bxkj.personal.ui.activity.myresume.workexp;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.workexp
 * @Description: WorkExp
 * @TODO: TODO
 * @date 2018/3/27
 */

public class WorkExpPresenter extends WorkExpContract.Presenter {

  private static final String TAG = WorkExpPresenter.class.getSimpleName();
  private PersonalApi mPersonalApi;

  @Inject
  public WorkExpPresenter(PersonalApi personalApi) {
    mPersonalApi = personalApi;
  }

  @Override
  public void getWorkExpDetails(int userId, int workExpId) {
    mView.showLoading();
    mPersonalApi.getWorkExpDetails(userId, workExpId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(BaseResponse baseResponse) {
          mView.hiddenLoading();
          mView.getWorkExpDetailsSuccess((WorkExpData) baseResponse.getData());
        }

        @Override
        protected void onError(RespondThrowable respondThrowable) {
          mView.hiddenLoading();
          mView.onError(respondThrowable.getErrMsg());
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }
      });
  }

  @Override
  public void addWorkExp(int userId, int resumeId, WorkExpData workExpData) {
    if (checkWorkExp(workExpData)) {
      mView.showLoading();
      mPersonalApi.addWorkExp(userId, resumeId, workExpData.getDate1(), workExpData.getDate2(),
          workExpData.getConame(), workExpData.getTradeid()
          , workExpData.getJob(), workExpData.getDes(), workExpData.getProid(),
          workExpData.getSizeid(), workExpData.getCoaddress(), workExpData.getPart(),
          workExpData.getReason())
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.addWorkExpSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }

          @Override
          public void onComplete() {
            super.onComplete();
            mView.hiddenLoading();
          }
        });
    }
  }

  private boolean checkWorkExp(WorkExpData workExpData) {
    if (CheckUtils.isNullOrEmpty(workExpData.getDate1())) {
      mView.onError("未选择开始时间");
      return false;
    } else if (CheckUtils.isNullOrEmpty(workExpData.getDate2())) {
      mView.onError("未选择结束时间");
      return false;
    } else if (CheckUtils.isNullOrEmpty(workExpData.getConame())) {
      mView.onError("未填写公司名称");
      return false;
    } else if (CheckUtils.isNullOrEmpty(workExpData.getJob())) {
      mView.onError("未填写职位名称");
      return false;
    } else if (CheckUtils.isNullOrEmpty(workExpData.getDes())) {
      mView.onError("未填写工作描述");
      return false;
    } else {
      return true;
    }
  }

  @Override
  public void updateWorkExp(int userId, int workExpId, WorkExpData workExpData) {
    if (checkWorkExp(workExpData)) {
      mView.showLoading();
      mPersonalApi.updateWorkExp(userId, workExpId, workExpData.getDate1(), workExpData.getDate2(),
          workExpData.getConame(), workExpData.getTradeid()
          , workExpData.getJob(), workExpData.getDes(), workExpData.getProid(),
          workExpData.getSizeid(), workExpData.getCoaddress(), workExpData.getPart(),
          workExpData.getReason())
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.updateWorkExpSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }

          @Override
          public void onComplete() {
            super.onComplete();
            mView.hiddenLoading();
          }
        });
    }
  }

  @Override
  public void deleteWorkExp(int userId, int workExpId) {
    mView.showLoading();
    mPersonalApi.deleteWorkExp(userId, workExpId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(BaseResponse baseResponse) {
          mView.deleteWorkExpSuccess();
        }

        @Override
        protected void onError(RespondThrowable respondThrowable) {
          mView.onError(respondThrowable.getErrMsg());
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }

        @Override
        public void onComplete() {
          super.onComplete();
          mView.hiddenLoading();
        }
      });
  }
}
