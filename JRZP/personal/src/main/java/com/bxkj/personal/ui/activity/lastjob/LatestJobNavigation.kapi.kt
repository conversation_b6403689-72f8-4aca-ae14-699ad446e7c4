package com.bxkj.personal.ui.activity.lastjob

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * Description:
 * Author:45457
 **/
class LatestJobNavigation {

    companion object {

        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/latestjob"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}