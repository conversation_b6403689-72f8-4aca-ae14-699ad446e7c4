package com.bxkj.personal.data;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.JobTypeData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 一键找工作信息
 * @TODO: TODO
 * @date 2018/4/13
 */

public class QuickFindJobInfo {


    /**
     * id : 1
     * jobId : 23,40
     * jobName : 软件工程师，网站编辑
     * areaId : 28615,28619
     * areaName : 滨江区，余杭区
     * moneyId : 7
     * moneyName : 8000-12000元
     * objState : 0
     * objDate : 2018/4/11 21:08:35
     * count : 0
     */

    private int id;
    private String jobId;
    private String jobName;
    private String areaId;
    private String areaName;
    private int moneyId;
    private String moneyName;
    private int objState;
    private String objDate;
    private int count;
    private List<JobTypeData> jobTypeDataList;
    private List<AreaOptionsData> areaItemDataList;

    public List<JobTypeData> getJobClassDataList() {
        return jobTypeDataList;
    }

    public void setJobClassDataList(List<JobTypeData> jobTypeDataList) {
        this.jobTypeDataList = jobTypeDataList;
    }

    public List<AreaOptionsData> getAreaItemDataList() {
        return areaItemDataList;
    }

    public void setAreaItemDataList(List<AreaOptionsData> areaItemDataList) {
        this.areaItemDataList = areaItemDataList;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public int getMoneyId() {
        return moneyId;
    }

    public void setMoneyId(int moneyId) {
        this.moneyId = moneyId;
    }

    public String getMoneyName() {
        return moneyName;
    }

    public void setMoneyName(String moneyName) {
        this.moneyName = moneyName;
    }

    public int getObjState() {
        return objState;
    }

    public void setObjState(int objState) {
        this.objState = objState;
    }

    public String getObjDate() {
        return objDate;
    }

    public void setObjDate(String objDate) {
        this.objDate = objDate;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
