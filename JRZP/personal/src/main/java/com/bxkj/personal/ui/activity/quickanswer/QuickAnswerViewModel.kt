package com.bxkj.personal.ui.activity.quickanswer

import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.handleResult
import com.bxkj.personal.R
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.QuestionRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/17
 * @version: V1.0
 */
class QuickAnswerViewModel @Inject constructor(application: Application
                                               , private val mAccountRepo: AccountRepo
                                               , private val mQuestionRepo: QuestionRepo) : BaseViewModel() {

    val answerHistoryListViewModel = RefreshListViewModel()
    val searchKeyword = MutableLiveData<String>()

    val toAddQuickAnswerCommand = LiveEvent<Void>()

    private var _searchKeyword: String = CommonApiConstants.NO_TEXT

    init {
        setupAnswerHistoryListViewModel()
    }

    private fun setupAnswerHistoryListViewModel() {
        answerHistoryListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mAccountRepo.getAnswerHistory(getSelfUserID(), _searchKeyword, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE)
                        .handleResult({
                            answerHistoryListViewModel.autoAddAll(it)
                        }, {
                            if (it.isNoDataError) {
                                answerHistoryListViewModel.noMoreData()
                            } else {
                                answerHistoryListViewModel.loadError()
                            }
                        })
            }
        }
    }

    fun start() {
        answerHistoryListViewModel.refresh()
    }

    fun startSearch(it: String) {
        _searchKeyword = it
        answerHistoryListViewModel.refresh(true)
    }

    fun toAddQuickAnswer() {
        toAddQuickAnswerCommand.call()
    }

    fun addQuickAnswer(quickAnswer: String) {
        viewModelScope.launch {
            showLoading()
            mQuestionRepo.addQuickAnswer(getSelfUserID(), quickAnswer)
                    .handleResult({
                        showToast(R.string.quick_answer_add_success_tips)
                        answerHistoryListViewModel.refresh()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
        }
    }
}