package com.bxkj.personal.ui.activity.schoolrecruitdetails;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import androidx.annotation.Nullable;
import com.bxkj.common.base.mvvm.BaseDBActivity;
import com.bxkj.common.util.HtmlUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.databinding.PersonalActivitySchoolRecruitDetailsBinding;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolrecruitdetails
 * @Description: 校园招聘详情
 * @TODO: TODO
 * @date 2019/6/13
 */
public class SchoolRecruitDetailsActivity
        extends BaseDBActivity<PersonalActivitySchoolRecruitDetailsBinding, SchoolRecruitDetailsViewModel> {

    public static final String EXTRA_SCHOOL_RECRUIT_ID = "SCHOOL_RECRUIT_ID";

    public static final String EXTRA_POSITION = "POSITION";

    public static final String EXTRA_JOB_FAIR_FOLLOW_STATUS = "JOB_FAIR_FOLLOW_STATUS";

    public static final String EXTRA_IS_EXPIRED = "IS_EXPIRED";

    public static final int RESULT_FOLLOW_CHANGE = RESULT_FIRST_USER + 1;

    public static Intent newIntent(Context context, int position, int jobFairId, boolean isExpired) {
        Intent intent = new Intent(context, SchoolRecruitDetailsActivity.class);
        intent.putExtra(EXTRA_POSITION, position);
        intent.putExtra(EXTRA_SCHOOL_RECRUIT_ID, jobFairId);
        intent.putExtra(EXTRA_IS_EXPIRED, isExpired);
        return intent;
    }

    @Override
    protected void initPage(@Nullable Bundle savedInstanceState) {
        getViewBinding().setViewModel(getViewModel());
        setupSkeletonScreen();

        subscribeDetailsChange();
        getViewModel().start(getIntent());
        subscribeViewModelCommand();
    }

    @Override
    protected Class<SchoolRecruitDetailsViewModel> getViewModelClass() {
        return SchoolRecruitDetailsViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.personal_activity_school_recruit_details;
    }

    private void setupSkeletonScreen() {
        SkeletonScreen skeletonScreen = Skeleton.bind(getViewBinding().scrollContent)
                .load(R.layout.personal_activity_school_recruit_skeleton)
                .color(R.color.common_f7f9fb)
                .duration(1500)
                .show();

        getViewBinding().webDetails.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (newProgress == 100) {
                    skeletonScreen.hide();
                }
            }
        });
    }

    private void subscribeViewModelCommand() {

    }

    private void subscribeDetailsChange() {
        getViewModel().getSchoolRecruitDetailsData().observe(this, schoolRecruitDetailsData -> {
            if (schoolRecruitDetailsData != null) {
                getViewBinding().webDetails.loadDataWithBaseURL(null,
                        HtmlUtils.fixRichText(schoolRecruitDetailsData.getContent()), "text/html", "utf-8", null);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        getViewBinding().webDetails.resumeTimers();
        getViewBinding().webDetails.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        getViewBinding().webDetails.pauseTimers();
        getViewBinding().webDetails.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (getViewBinding().webDetails != null) {
            getViewBinding().webDetails.destroy();
        }
    }

    @Override
    public void finish() {
        getIntent().putExtra(EXTRA_JOB_FAIR_FOLLOW_STATUS, getViewModel().followTheJobFair());
        setResult(RESULT_FOLLOW_CHANGE, getIntent());
        super.finish();
    }
}
