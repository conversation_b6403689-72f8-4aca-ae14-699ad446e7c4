package com.bxkj.personal.ui.activity.searchnews

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchHistoryListAdapter
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivitySearchVideoBinding
import com.bxkj.staggeredlist.VideoListType
import com.bxkj.staggeredlist.ui.list.StaggeredVideoListNavigation
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.flexbox.FlexboxLayoutManager

/**
 * 搜索视频
 * @author: YangXin
 * @date: 2021/5/27
 */

private const val VIDEO_CONTENT_FRAGMENT_TAG = "VIDEO_FRAGMENT"

class SearchVideoActivity :
  BaseDBActivity<PersonalActivitySearchVideoBinding, SearchVideoViewModel>(), OnClickListener {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, SearchVideoActivity::class.java)
    }
  }

  private var mSearchHistoryListAdapter: SearchHistoryListAdapter? = null

  override fun getViewModelClass(): Class<SearchVideoViewModel> = SearchVideoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_search_video

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupSearchResultFragment()
    setupSearchHistoryListAdapter()
    setupEditSearchClickListener()
    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun setupSearchResultFragment() {
    supportFragmentManager.beginTransaction()
      .add(R.id.fl_search_result, StaggeredVideoListNavigation.create(VideoListType.RECOMMEND),
        VIDEO_CONTENT_FRAGMENT_TAG)
      .commit()
  }

  private fun subscribeViewModelEvent() {
    viewModel.openEditHistory.observe(this, {
      mSearchHistoryListAdapter?.switchEditModel(it)
    })

    viewModel.startSearchCommand.observe(this, { keyword ->
      SystemUtil.hideSoftKeyboard(this)
      supportFragmentManager.findFragmentByTag(VIDEO_CONTENT_FRAGMENT_TAG)?.let {
        if (it is StaggeredVideoListNavigation.Content) {
          it.onStartSearch(keyword)
        }
      }
    })

    viewModel.searchContent.observe(this, Observer {
      if (it.isNullOrBlank()) {
        viewModel.hideSearchResult()
      }
    })
  }

  /**
   * 设置搜索按钮点击事件
   */
  private fun setupEditSearchClickListener() {
    viewBinding.etSearchContent.setOnEditorActionListener { v, actionId, event ->
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        viewModel.startSearch()
        return@setOnEditorActionListener true
      }
      return@setOnEditorActionListener false
    }
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.tv_cancel -> {
          finish()
        }
        R.id.tv_clear_history -> {
          viewModel.clearHistory()
        }
      }
    }
  }

  /**
   * 设置搜索历史适配器
   */
  private fun setupSearchHistoryListAdapter() {
    mSearchHistoryListAdapter = SearchHistoryListAdapter().apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getData()?.let {
            if (getEditMode()) {
              viewModel.deleteHistory(it[position])
            } else {
              viewModel.startSearchByKeyword(it[position].keyword)
            }
          }
        }
      })
    }

    viewBinding.recyclerHistory.closeDefaultAnim()
    viewBinding.recyclerHistory.isNestedScrollingEnabled = false
    viewBinding.recyclerHistory.layoutManager = FlexboxLayoutManager(this, FlexDirection.ROW)
    viewBinding.recyclerHistory.addItemDecoration(
      FlexboxItemDecoration(this).apply {
        setDrawable(ContextCompat.getDrawable(this@SearchVideoActivity, R.drawable.divider_8))
        setOrientation(FlexboxItemDecoration.BOTH)
      }
    )
    viewBinding.recyclerHistory.adapter = mSearchHistoryListAdapter
  }

}