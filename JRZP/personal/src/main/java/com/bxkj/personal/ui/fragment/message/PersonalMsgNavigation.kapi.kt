package com.bxkj.personal.ui.fragment.message

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/25
 * @version: V1.0
 */
class PersonalMsgNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/message"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}