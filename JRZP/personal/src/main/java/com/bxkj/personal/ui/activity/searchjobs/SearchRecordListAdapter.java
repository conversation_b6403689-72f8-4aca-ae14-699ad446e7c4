package com.bxkj.personal.ui.activity.searchjobs;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.shop.ui.search
 * @Description: 搜索历史记录适配器
 * @TODO: TODO
 * @date 2018/3/18
 */

public class SearchRecordListAdapter extends SuperAdapter<String> {
    public SearchRecordListAdapter(Context context, List<String> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, String s, int position) {
        TextView tvRecordName = holder.findViewById(R.id.tv_search_record_item);
        ImageView ivDeleteRecord = holder.findViewById(R.id.iv_delete_record);
        tvRecordName.setText(s);
        tvRecordName.setOnClickListener(v -> {
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(v, position);
            }
        });
        ivDeleteRecord.setOnClickListener(v -> {
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(v, position);
            }
        });
    }
}
