package com.bxkj.personal.ui.activity.createresumestepfour

import android.app.Activity
import android.content.Intent
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bigkoo.pickerview.builder.OptionsPickerBuilder
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bigkoo.pickerview.listener.OnOptionsSelectListener
import com.bigkoo.pickerview.view.OptionsPickerView
import com.bigkoo.pickerview.view.TimePickerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.BaseDaggerActivity
import com.bxkj.common.constants.RouterConstants
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.TitleBarManager
import com.bxkj.common.util.picker.PickerUtils
import com.bxkj.common.util.recyclerutil.RecycleViewDivider
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.jrzp.user.videorelate.ui.MyVideoRelateNavigation
import com.bxkj.personal.R
import com.bxkj.personal.data.EduBackgroundData
import com.bxkj.personal.data.EduBackgroundItemData
import com.bxkj.personal.mvp.contract.EducationContract
import com.bxkj.personal.mvp.contract.ProfessionalContract
import com.bxkj.personal.mvp.presenter.EducationPresenter
import com.bxkj.personal.ui.activity.main.MainActivity
import com.bxkj.personal.ui.activity.myresume.edubackground.EduBackgroundActivity
import com.bxkj.personal.ui.activity.myresumelist.MyResumeListActivity
import com.bxkj.personal.ui.activity.selectresume.SelectResumeActivity
import com.bxkj.video.VideoType
import com.bxkj.video.ui.addinfolinkvideos.AddInfoLinkVideosNavigation
import io.reactivex.disposables.Disposable
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumestepfour
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/28
 * @version V1.0
 */
class CreateResumeStepFourActivity : BaseDaggerActivity(), CreateResumeStepFourContract.View,
  ProfessionalContract.View, EducationContract.View, View.OnClickListener {

  @Inject
  lateinit var mCreateResumeStepFourPresenter: CreateResumeStepFourPresenter

  @Inject
  lateinit var mEducationPresenter: EducationPresenter

  private lateinit var mEduBackgroundData: EduBackgroundData
  private lateinit var mResumeEduBgAdapter: ResumeEduBgAdapter

  private lateinit var mStartDatePicker: TimePickerView
  private lateinit var mEndDatePicker: TimePickerView
  private lateinit var mProfessionalPicker: OptionsPickerView<PickerOptionsData>
  private lateinit var mEducationPicker: OptionsPickerView<FilterOptionData>
  private lateinit var mProfessionalData: MutableList<PickerOptionsData>
  private lateinit var mEducationData: MutableList<FilterOptionData>

  private lateinit var mDisposable: Disposable

  private var mResumeId: Int = 0
  private var mClickedComplete: Boolean = false
  private var mCreateResumeFrom = 0

  companion object {

    private const val TO_EDU_BG_ITEM_CODE = 1
    private const val TO_ADD_RESUME_LINK_VIDEOS = 2

    fun newIntent(activity: Activity, resumeId: Int): Intent {
      val intent = Intent(activity, CreateResumeStepFourActivity::class.java)
      intent.putExtra(RouterConstants.RESUME_ID, resumeId)
      return intent
    }
  }

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>): MutableList<BasePresenter<BaseView>> {
    presenters.add(CheckUtils.cast(mCreateResumeStepFourPresenter))
    presenters.add(CheckUtils.cast(mEducationPresenter))
    return presenters
  }

  override fun getLayoutId(): Int = R.layout.personal_activity_create_resume_step_four

  override fun initIntent(intent: Intent) {
    mResumeId = intent.getIntExtra(RouterConstants.RESUME_ID, 0)
    mCreateResumeFrom = intent.getIntExtra(RouterConstants.CREATE_RESUME_FROM, 0)
  }

  override fun initTitleBar(titleBarManager: TitleBarManager?) {
    titleBarManager!!.setTitle(getString(R.string.personal_education_background))
  }

  override fun initPage() {
    mEduBackgroundData = EduBackgroundData()
    handleRxMsg()

    initEduBgRecycler()
    initStartAndEndDatePicker()
    initEducationPicker()

    mCreateResumeStepFourPresenter.getResumeEduBg(mUserID, mResumeId)

    setupOnClickListener()
  }

  private fun setupOnClickListener() {
    findViewById<TextView>(R.id.tv_start_time).setOnClickListener(this)
    findViewById<TextView>(R.id.tv_end_time).setOnClickListener(this)
    findViewById<TextView>(R.id.tv_education).setOnClickListener(this)
    findViewById<TextView>(R.id.tv_save).setOnClickListener(this)
    findViewById<TextView>(R.id.tv_complete).setOnClickListener(this)
  }

  override fun onClick(v: View) {
    SystemUtil.hideSoftKeyboard(this)
    when (v.id) {
      R.id.tv_start_time -> mStartDatePicker.show()
      R.id.tv_end_time -> mEndDatePicker.show()
      R.id.tv_education -> mEducationPicker.show()
      R.id.tv_save -> {
        mClickedComplete = false
        saveEduBg()
      }
      R.id.tv_complete -> {
        complete()
      }
    }
  }

  private fun handleRxMsg() {
    mDisposable = RxBus.get().toObservable(RxBus.Message::class.java)
      .subscribe {
        if (it.code == RxMsgCode.ACTION_FROM_REGISTER_USER) {
//                        mActionForRegisterUser = true
        }
      }
  }

  private fun initEduBgRecycler() {
    mResumeEduBgAdapter =
      ResumeEduBgAdapter(this, null, R.layout.personal_recycler_create_resume_work_exp_item)
    mResumeEduBgAdapter.setOnItemClickListener(object : SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        val eduBgId = mResumeEduBgAdapter.data[position].id
        if (v.id == R.id.tv_delete) {
          ActionDialog.Builder()
            .setTitle(getString(R.string.resume_delete_edu_bg_tips))
            .setOnConfirmClickListener {
              mCreateResumeStepFourPresenter.deleteResumeEduBg(mUserID, eduBgId, position)
            }.build().show(supportFragmentManager, ActionDialog.TAG)
        } else {
          startActivityForResult(
            EduBackgroundActivity.newIntent(
              this@CreateResumeStepFourActivity,
              EduBackgroundActivity.UPDATE_TYPE,
              mResumeId,
              eduBgId
            ), TO_EDU_BG_ITEM_CODE
          )
        }
      }
    })
    val recyclerEduBg = findViewById<RecyclerView>(R.id.recycler_edu_bg)

    recyclerEduBg.adapter = mResumeEduBgAdapter
    recyclerEduBg.layoutManager = LinearLayoutManager(this)
    recyclerEduBg.addItemDecoration(
      RecycleViewDivider(
        this,
        LinearLayoutManager.HORIZONTAL,
        1,
        getMColor(R.color.common_f4f4f4)
      )
    )
  }

  /**
   * 初始化开始和结束时间选择器
   */
  private fun initStartAndEndDatePicker() {
    val startCalendar = Calendar.getInstance()
    startCalendar.set(1900, 0, 1)
    mStartDatePicker = PickerUtils.applyMyConfig(TimePickerBuilder(this) { date, _ ->
      val startDate = SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(date)
      findViewById<TextView>(R.id.tv_start_time).text = startDate
      mEduBackgroundData.date1 = startDate
    }
      .setDate(Calendar.getInstance())
      .setType(booleanArrayOf(true, true, false, false, false, false))
      .setRangDate(startCalendar, Calendar.getInstance()))
      .build()

    mEndDatePicker = PickerUtils.applyMyConfig(TimePickerBuilder(this) { date, _ ->
      val endDate = SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(date)
      findViewById<TextView>(R.id.tv_end_time).text = endDate
      mEduBackgroundData.date2 = endDate
    }
      .setDate(Calendar.getInstance())
      .setType(booleanArrayOf(true, true, false, false, false, false))
      .setRangDate(startCalendar, Calendar.getInstance()))
      .build()
  }

  private fun initEducationPicker() {
    mEducationData = ArrayList()
    mEducationPicker = PickerUtils.applyMyConfig(
      OptionsPickerBuilder(this, OnOptionsSelectListener { options1, _, _, _ ->
        if (mEducationData.isNotEmpty()) {
          mEduBackgroundData.quaid = mEducationData[options1].id
          findViewById<TextView>(R.id.tv_education).text = mEducationData[options1].name
        }
      })
    ).build()
    mEducationPresenter.getEducation()
  }

  private fun complete() {
    mClickedComplete = true
    if (CheckUtils.isNullOrEmpty(mResumeEduBgAdapter.data)) {
      saveEduBg()
    } else {
      if (mEduBackgroundData.isEmpty) {
        toAddResumeLinkVideos()
      } else {
        saveEduBg()
      }
    }
  }

  private fun saveEduBg() {
    mEduBackgroundData.school = findViewById<EditText>(R.id.et_school_name).text.toString()
    mEduBackgroundData.proName2 = findViewById<EditText>(R.id.et_professional_title).text.toString()
    mCreateResumeStepFourPresenter.addResumeEduBg(mUserID, mResumeId, mEduBackgroundData)
  }

  override fun deleteEduBgSuccess(position: Int) {
    mResumeEduBgAdapter.removeAt(position)
  }

  override fun getResumeEduBgSuccess(eduBackgroundItemDataList: MutableList<EduBackgroundItemData>?) {
    mResumeEduBgAdapter.reset(eduBackgroundItemDataList)
    if (mClickedComplete) {
      toAddResumeLinkVideos()
    }
  }

  /**
   * 添加简历关联视频
   */
  private fun toAddResumeLinkVideos() {
    AddInfoLinkVideosNavigation.navigate(mResumeId, VideoType.VIDEO_TYPE_RESUME).startForResult(
      this,
      TO_ADD_RESUME_LINK_VIDEOS
    )
  }

  /**
   * 显示创建简历成功提示
   */
  private fun showCreateResumeSuccessTips() {
    when (mCreateResumeFrom) {
      //来源为登录或注册
      RouterConstants.CREATE_RESUME_FROM_REGISTER_OR_LOGIN -> startActivity(
        Intent(
          this,
          MainActivity::class.java
        )
      )
      //来源为投递简历
      RouterConstants.CREATE_RESUME_FROM_APPLICATION -> {
        val intent = Intent(this, SelectResumeActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
      }
      //来源为简历列表
      RouterConstants.CREATE_RESUME_FROM_RESUME_LIST -> {
        val intent = Intent(this, MyResumeListActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
      }
      //来源为视频关联
      RouterConstants.CREATE_RESUME_FROM_VIDEO_RELATE -> {
        MyVideoRelateNavigation.navigate().withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
          .start(this)
      }
      RouterConstants.CREATE_RESUME_FROM_NORMAL -> {
        val intent = MainActivity.newIntent(this)
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivity(intent)
      }
    }
    finish()
  }

  override fun getProfessionalListSuccess(pickerOptionsData: MutableList<PickerOptionsData>) {
    mProfessionalData.addAll(pickerOptionsData)
    mProfessionalPicker.setPicker(mProfessionalData)
  }

  override fun getEducationSuccess(filterOptionDataList: MutableList<FilterOptionData>) {
    mEducationData.addAll(filterOptionDataList)
    mEducationPicker.setPicker(mEducationData)
  }

  override fun addEduBgSuccess() {
    clearData()
    mCreateResumeStepFourPresenter.getResumeEduBg(mUserID, mResumeId)
  }

  private fun clearData() {
    mEduBackgroundData = EduBackgroundData()
    findViewById<TextView>(R.id.tv_start_time).text = ""
    findViewById<TextView>(R.id.tv_end_time).text = ""
    findViewById<TextView>(R.id.et_school_name).setText("")
    findViewById<TextView>(R.id.et_professional_title).setText("")
    findViewById<TextView>(R.id.tv_education).text = ""
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_EDU_BG_ITEM_CODE) {
      if (resultCode == Activity.RESULT_OK) {
        mCreateResumeStepFourPresenter.getResumeEduBg(mUserID, mResumeId)
      }
    } else if (requestCode == TO_ADD_RESUME_LINK_VIDEOS) {
      showCreateResumeSuccessTips()
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    if (!mDisposable.isDisposed) {
      mDisposable.dispose()
    }
  }

}