package com.bxkj.personal.ui.fragment.study

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.StudyNewsItemData
import com.bxkj.personal.data.source.NewsRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
class StudyViewModel @Inject constructor(private val mNewsRepo: NewsRepo) : BaseViewModel() {

    val studyNewsListViewModel = RefreshListViewModel()

    init {
        studyNewsListViewModel.setOnLoadDataListener { currentPage ->
            mNewsRepo.getStudyNewsList(currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE, CommonApiConstants.NO_ID, CommonApiConstants.NO_TEXT
                    , object : ResultDataCallBack<List<StudyNewsItemData>> {
                override fun onSuccess(data: List<StudyNewsItemData>?) {
                    studyNewsListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001 || respondThrowable.errCode == 30005) {
                        studyNewsListViewModel.noMoreData()
                    } else {
                        studyNewsListViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start() {
        studyNewsListViewModel.refresh()
    }

    fun loadMore() {
        studyNewsListViewModel.refreshLayoutViewModel.loadMore()
    }

}