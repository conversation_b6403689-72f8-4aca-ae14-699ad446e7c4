package com.bxkj.personal.data;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/11/27
 */
public class RechargeDiscountItemData {

    /**
     * price : 200
     * integral : 100
     */

    private int price;
    private int integral;

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public static class ItemDiffCallBack extends DiffUtil.ItemCallback<RechargeDiscountItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull RechargeDiscountItemData oldItem, @NonNull RechargeDiscountItemData newItem) {
            return oldItem.equals(newItem);
        }

        @Override
        public boolean areContentsTheSame(@NonNull RechargeDiscountItemData oldItem, @NonNull RechargeDiscountItemData newItem) {
            return oldItem.price == newItem.price;
        }
    }
}
