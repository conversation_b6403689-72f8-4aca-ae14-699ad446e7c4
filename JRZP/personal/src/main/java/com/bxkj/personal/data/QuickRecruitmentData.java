package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 速聘数据
 * @TODO: TODO
 * @date 2018/5/5
 */

public class QuickRecruitmentData {


    /**
     * id : 4
     * name : 传菜员4
     * num : 0
     * coname : 滨兴小厨
     * lxr : null
     * phone : null
     * email : null
     * qq : null
     * sheng : 0
     * shi : 0
     * xian : 0
     * xianName : 滨江区
     * jie : 0
     * jieName : 浦沿街道
     * address : null
     * date : null
     * shdate : null
     * jsdate : 2018/6/2 0:00:00
     * des : null
     * htc : 0
     * pwd : null
     * status : 0
     * fmsg : null
     */

    private int id;
    private String name;
    private int num;
    private String coname;
    private Object lxr;
    private Object phone;
    private Object email;
    private Object qq;
    private int sheng;
    private int shi;
    private int xian;
    private String xianName;
    private int jie;
    private String jieName;
    private Object address;
    private Object date;
    private Object shdate;
    private String jsdate;
    private Object des;
    private int htc;
    private Object pwd;
    private int status;
    private Object fmsg;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getConame() {
        return coname;
    }

    public void setConame(String coname) {
        this.coname = coname;
    }

    public Object getLxr() {
        return lxr;
    }

    public void setLxr(Object lxr) {
        this.lxr = lxr;
    }

    public Object getPhone() {
        return phone;
    }

    public void setPhone(Object phone) {
        this.phone = phone;
    }

    public Object getEmail() {
        return email;
    }

    public void setEmail(Object email) {
        this.email = email;
    }

    public Object getQq() {
        return qq;
    }

    public void setQq(Object qq) {
        this.qq = qq;
    }

    public int getSheng() {
        return sheng;
    }

    public void setSheng(int sheng) {
        this.sheng = sheng;
    }

    public int getShi() {
        return shi;
    }

    public void setShi(int shi) {
        this.shi = shi;
    }

    public int getXian() {
        return xian;
    }

    public void setXian(int xian) {
        this.xian = xian;
    }

    public String getXianName() {
        return xianName;
    }

    public void setXianName(String xianName) {
        this.xianName = xianName;
    }

    public int getJie() {
        return jie;
    }

    public void setJie(int jie) {
        this.jie = jie;
    }

    public String getJieName() {
        return jieName;
    }

    public void setJieName(String jieName) {
        this.jieName = jieName;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public Object getDate() {
        return date;
    }

    public void setDate(Object date) {
        this.date = date;
    }

    public Object getShdate() {
        return shdate;
    }

    public void setShdate(Object shdate) {
        this.shdate = shdate;
    }

    public String getJsdate() {
        return jsdate;
    }

    public void setJsdate(String jsdate) {
        this.jsdate = jsdate;
    }

    public Object getDes() {
        return des;
    }

    public void setDes(Object des) {
        this.des = des;
    }

    public int getHtc() {
        return htc;
    }

    public void setHtc(int htc) {
        this.htc = htc;
    }

    public Object getPwd() {
        return pwd;
    }

    public void setPwd(Object pwd) {
        this.pwd = pwd;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getFmsg() {
        return fmsg;
    }

    public void setFmsg(Object fmsg) {
        this.fmsg = fmsg;
    }
}
