package com.bxkj.personal.ui.activity.orderhistory

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.personal.ui.fragment.orderhistory.OrderHistoryFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Project: jrzp
 * @Package com.bxkj.personal.ui.activity.orderhistory
 * @Description:
 * <AUTHOR>
 * @date 2020/2/16
 * @version V1.0
 */
@Module
abstract class OrderHistoryModule {

    @PerFragment
    @ContributesAndroidInjector
    abstract fun orderHistoryFragment(): OrderHistoryFragment
}