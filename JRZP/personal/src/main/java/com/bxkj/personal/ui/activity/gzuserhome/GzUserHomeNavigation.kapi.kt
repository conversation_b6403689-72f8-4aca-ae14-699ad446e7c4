package com.bxkj.personal.ui.activity.gzuserhome

import androidx.core.os.bundleOf
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class GzUserHomeNavigation {
  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/gzuserhome"

    const val EXTRA_QUERY_USER_ID = "QUERY_USER_ID"
    const val EXTRA_QUERY_COMPANY_ID = "QUERY_COMPANY_ID"
    const val EXTRA_JUMP_TO_NOTICE = "JUMP_TO_NOTICE"
    const val EXTRA_AFFECT_POSITION = "AFFECT_POSITION"
    const val EXTRA_FOLLOW_STATUS = "FOLLOW_STATUS"
    const val EXTRA_TO_QUESTION_PAGE = "TO_QUESTION_PAGE"
    const val EXTRA_TARGET_PAGE_INDEX = "TARGET_PAGE_INDEX"
    const val EXTRA_IS_ORG = "IS_ENTERPRISE"

    fun navigate(
      queryUserId: Int, queryCompanyId: Int? = CommonApiConstants.NO_ID,
      jumpToNotice: Boolean? = false, affectPosition: Int? = 0, toQuestionPage: Boolean? = false,
      targetPageIndex: Int = CommonApiConstants.NO_ID, isOrg: Boolean? = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH).with(
        bundleOf(
          EXTRA_QUERY_USER_ID to queryUserId,
          EXTRA_QUERY_COMPANY_ID to queryCompanyId,
          EXTRA_JUMP_TO_NOTICE to jumpToNotice,
          EXTRA_AFFECT_POSITION to affectPosition,
          EXTRA_TO_QUESTION_PAGE to toQuestionPage,
          EXTRA_TARGET_PAGE_INDEX to targetPageIndex,
          EXTRA_IS_ORG to isOrg
        )
      )
    }
  }
}