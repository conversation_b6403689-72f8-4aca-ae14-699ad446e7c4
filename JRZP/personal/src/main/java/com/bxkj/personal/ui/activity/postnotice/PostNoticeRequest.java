package com.bxkj.personal.ui.activity.postnotice;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.postnotice
 * @Description:
 * @date 2020/1/6
 */
public class PostNoticeRequest {

    /**
     * type : 1
     * title : 这是一篇测试资讯
     * userID : 228609
     * dwID : 6
     * content : 从前有座山， 山里有个庙， 庙里有个老和尚在讲故事，讲的什么呀，从前有座山，山里有座庙，庙里有个盆，盆里有个钵， 钵里有个碗，碗里有个匙， 匙里有个花生仁，我吃了，你谗了，我的故事讲完了
     * fujian :
     * from : 网络
     * fromUrl : www.baidu.com
     * author : 无名
     * bianji : 今日招聘
     * keys : 测试
     * listMedia : images_server/shejiao/228609/20191218134946_7352.jpg
     * mediaType : 1
     */

    private int type;
    private String title;
    private int userID;
    private int dwID;
    private String content;
    private String fujian;
    private String from;
    private String fromUrl;
    private String author;
    private String bianji;
    private String keys;
    private String listMedia;
    private int mediaType;
    private int typeGuoqi;

    public int getTypeGuoqi() {
        return typeGuoqi;
    }

    public void setTypeGuoqi(int typeGuoqi) {
        this.typeGuoqi = typeGuoqi;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getUserID() {
        return userID;
    }

    public void setUserID(int userID) {
        this.userID = userID;
    }

    public int getDwID() {
        return dwID;
    }

    public void setDwID(int dwID) {
        this.dwID = dwID;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFujian() {
        return fujian;
    }

    public void setFujian(String fujian) {
        this.fujian = fujian;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getFromUrl() {
        return fromUrl;
    }

    public void setFromUrl(String fromUrl) {
        this.fromUrl = fromUrl;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getBianji() {
        return bianji;
    }

    public void setBianji(String bianji) {
        this.bianji = bianji;
    }

    public String getKeys() {
        return keys;
    }

    public void setKeys(String keys) {
        this.keys = keys;
    }

    public String getListMedia() {
        return listMedia;
    }

    public void setListMedia(String listMedia) {
        this.listMedia = listMedia;
    }

    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }
}
