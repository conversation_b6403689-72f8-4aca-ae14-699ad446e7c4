package com.bxkj.personal.ui.activity.study

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityStudyBinding
import com.bxkj.personal.ui.fragment.study.StudyFragment

/**
 *
 * @author: sanjin
 * @date: 2022/9/1
 */
class StudyActivity : BaseDBActivity<PersonalActivityStudyBinding, BaseViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, StudyActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_study

    override fun initPage(savedInstanceState: Bundle?) {

        setupContentFragment()
    }

    private fun setupContentFragment() {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fl_content, StudyFragment.newInstance())
            .commit()
    }
}