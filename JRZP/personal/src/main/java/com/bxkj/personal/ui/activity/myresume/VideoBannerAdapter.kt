package com.bxkj.personal.ui.activity.myresume

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.video.data.VideoData
import com.youth.banner.adapter.BannerAdapter

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/13
 * @version: V1.0
 */
class VideoBannerAdapter constructor(list: List<VideoData>?) : BannerAdapter<VideoData, SuperViewHolder>(list) {
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): SuperViewHolder {
        //兼容databinding
        val viewDataBinding = DataBindingUtil.inflate<ViewDataBinding>(
                LayoutInflater.from(parent.context),
                R.layout.recycler_video_banner_item,
                parent,
                false
        )
        return if (viewDataBinding != null) {
            SuperViewHolder(viewDataBinding)
        } else {
            SuperViewHolder(
                LayoutInflater.from(parent.context).inflate(layout.recycler_video_banner_item, parent, false)
            )
        }
    }

    override fun onBindView(holder: SuperViewHolder, dataRecruitment: VideoData?, position: Int, size: Int) {
        holder.bind(BR.data, dataRecruitment)
    }
}