package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 资讯详情
 * @TODO: TODO
 * @date 2018/4/27
 */

public class NewsDetailsData {


    /**
     * id : 18191
     * title : 2018年4月27日长春高新人才市场高新区综合性人才招聘会
     * date : 2018/3/9 23:55:29
     * htc : 48
     * content : <p>4月27日（周五）高新区综合性人才招聘会</p><p>参会须知：</p><p>参会单位凭营业执照副本复印件、参会回执、招聘简章word(无表格)电子版（含电话、传真、e-mail）现场办理招聘手续。</p><p>求职人员一律免费参加。</p><p>地点：前进大街3055号（吉林大学南校区南行300米）</p><p>乘车路线：乘轻轨3号线、239、149路车到&ldquo;吉林铁通前进站&rdquo;（卫星路与前进大街交汇）下车，换乘13路，222路火炬路下车即是；乘154、315路车修正路下车，南行500米即是；乘193路前进大街下车即</p>
     * bianji : 今日招聘
     * from : 人力资源中心
     * shi : 4601
     * shiName : 长春
     * ksdate : 2018/4/27 0:00:00
     * jsdate : 2018/4/27 0:00:00
     * address :  前进大街3055号（吉林大学南校区南行300米）
     * type1 : 2
     * type2 : 0
     */

    private int id;
    private String title;
    private String date;
    private int htc;
    private String content;
    private String bianji;
    private String from;
    private int shi;
    private String shiName;
    private String ksdate;
    private String jsdate;
    private String address;
    private int type1;
    private int type2;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getHtc() {
        return htc;
    }

    public void setHtc(int htc) {
        this.htc = htc;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBianji() {
        return bianji;
    }

    public void setBianji(String bianji) {
        this.bianji = bianji;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public int getShi() {
        return shi;
    }

    public void setShi(int shi) {
        this.shi = shi;
    }

    public String getShiName() {
        return shiName;
    }

    public void setShiName(String shiName) {
        this.shiName = shiName;
    }

    public String getKsdate() {
        return ksdate;
    }

    public void setKsdate(String ksdate) {
        this.ksdate = ksdate;
    }

    public String getJsdate() {
        return jsdate;
    }

    public void setJsdate(String jsdate) {
        this.jsdate = jsdate;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getType1() {
        return type1;
    }

    public void setType1(int type1) {
        this.type1 = type1;
    }

    public int getType2() {
        return type2;
    }

    public void setType2(int type2) {
        this.type2 = type2;
    }
}
