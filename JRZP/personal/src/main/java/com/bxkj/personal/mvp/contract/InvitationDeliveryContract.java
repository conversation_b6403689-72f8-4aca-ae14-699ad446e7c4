package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: InvitationDelivery
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface InvitationDeliveryContract {
    interface View extends BaseView {
        void acceptDeliverySuccess(int position);

        void refuseDeliverySuccess(int position);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void acceptDeliveryInvitation(int userId, int otherId, int position);

        public abstract void refuseDeliveryInvitation(int userId, int otherId, int position);
    }
}
