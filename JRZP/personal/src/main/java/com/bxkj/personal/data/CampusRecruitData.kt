package com.bxkj.personal.data

import android.os.Parcelable
import androidx.recyclerview.widget.DiffUtil
import kotlinx.parcelize.Parcelize

@Parcelize
open class CampusRecruitData(
    var id: Int = 0,
    var uid: Int = 0,
    var title: String? = "",
    var content: String? = "",
    var createTime: String? = "",
    var dwTime: String? = "",
    var shengstr: String? = "",
    var jobstr: String? = "",
    var comLogo: String? = "",
    var isStow: Int = 0,
    var comName: String? = "",
) : Parcelable {

    fun getConvertJobsText(): Array<String> {
        return jobstr?.split("，")?.toTypedArray() ?: emptyArray()
    }

    fun getAppendJobText(): String {
        val jobTextBuilder = StringBuilder()
        jobstr?.split(",", "，")?.forEach {
            if (it.isNotEmpty()) {
                jobTextBuilder.append(it).append(" ")
            }
        }
        return jobTextBuilder.toString()
    }

    fun getAppendProvinceText(): String {
        val provinceTextBuilder = StringBuilder()
        shengstr?.split(",")?.forEach {
            if (it.isNotEmpty()) {
                provinceTextBuilder.append(it).append(" ")
            }
        }
        return provinceTextBuilder.toString()
    }

    class DiffCallback : DiffUtil.ItemCallback<CampusRecruitData>() {

        override fun areItemsTheSame(
            oldItem: CampusRecruitData,
            newItem: CampusRecruitData
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: CampusRecruitData,
            newItem: CampusRecruitData
        ): Boolean {
            return oldItem.title == newItem.title
        }
    }
}