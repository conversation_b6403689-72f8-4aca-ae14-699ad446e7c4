package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.BR;

/**
 * @date 2018/5/10
 */

public class WorkExpData extends BaseObservable {

  private int id;
  private int uid;
  private int resid;
  private String date1;
  private String date2;
  private int tradeid = -1;
  private String tradeName;
  private int proid;
  private String proName;
  private int sizeid;
  private String sizeName;
  private String coname;
  private String coaddress;
  private String part;
  private String job;
  private String des;
  private String reason;

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public int getResid() {
    return resid;
  }

  public void setResid(int resid) {
    this.resid = resid;
  }

  @Bindable
  public String getDate1() {
    return date1;
  }

  public void setDate1(String date1) {
    this.date1 = date1;
    notifyPropertyChanged(BR.date1);
  }

  @Bindable
  public String getDate2() {
    return date2;
  }

  public void setDate2(String date2) {
    this.date2 = date2;
    notifyPropertyChanged(BR.date2);
  }

  public int getTradeid() {
    return tradeid;
  }

  public void setTradeid(int tradeid) {
    this.tradeid = tradeid;
  }

  public String getTradeName() {
    return tradeName;
  }

  public void setTradeName(String tradeName) {
    this.tradeName = tradeName;
  }

  public int getProid() {
    return proid;
  }

  public void setProid(int proid) {
    this.proid = proid;
  }

  public String getProName() {
    return proName;
  }

  public void setProName(String proName) {
    this.proName = proName;
  }

  public int getSizeid() {
    return sizeid;
  }

  public void setSizeid(int sizeid) {
    this.sizeid = sizeid;
  }

  public String getSizeName() {
    return sizeName;
  }

  public void setSizeName(String sizeName) {
    this.sizeName = sizeName;
  }

  public String getConame() {
    return coname;
  }

  public void setConame(String coname) {
    this.coname = coname;
  }

  public String getCoaddress() {
    return coaddress;
  }

  public void setCoaddress(String coaddress) {
    this.coaddress = coaddress;
  }

  public String getPart() {
    return part;
  }

  public void setPart(String part) {
    this.part = part;
  }

  public String getJob() {
    return job;
  }

  public void setJob(String job) {
    this.job = job;
  }

  @Bindable
  public String getDes() {
    return des;
  }

  public void setDes(String des) {
    this.des = des;
    notifyPropertyChanged(BR.des);
  }

  public String getReason() {
    return reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public String getEmptyTips() {
    if (CheckUtils.isNullOrEmpty(date1)) {
      return "请选择开始时间";
    }
    if (CheckUtils.isNullOrEmpty(date2)) {
      return "请选择结束时间";
    }
    if (CheckUtils.isNullOrEmpty(coname)) {
      return "请输入公司名称";
    }
    if (CheckUtils.isNullOrEmpty(job)) {
      return "请输入职位";
    }
    if (CheckUtils.isNullOrEmpty(des)) {
      return "请输入工作描述";
    }
    return "";
  }

  public boolean isEmpty() {
    if (CheckUtils.isNullOrEmpty(date1)
      && CheckUtils.isNullOrEmpty(date2)
      && CheckUtils.isNullOrEmpty(coname)
      && tradeid == -1
      && CheckUtils.isNullOrEmpty(job)
      && CheckUtils.isNullOrEmpty(des)) {
      return true;
    }
    return false;
  }
}
