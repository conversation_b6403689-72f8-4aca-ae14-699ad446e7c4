package com.bxkj.personal.mvp.presenter;


import com.bxkj.common.network.ZPCommonApi;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.personal.mvp.contract.GetFilterOptionsContract;
import com.bxkj.personal.api.PersonalApi;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: GetMoreRequirements
 * @TODO: TODO
 * @date 2018/3/27
 */

public class GetFilterOptionsPresenter extends GetFilterOptionsContract.Presenter {

    private static final String TAG = GetFilterOptionsPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;
    private ZPCommonApi mZPCommonApi;

    @Inject
    public GetFilterOptionsPresenter(PersonalApi personalApi, ZPCommonApi ZPCommonApi) {
        mPersonalApi = personalApi;
        mZPCommonApi = ZPCommonApi;
    }

    @Override
    public void getNaturesOfCompany() {
        mPersonalApi.getNaturesOfCompany()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<FilterOptionData> result= (List<FilterOptionData>) baseResponse.getDataList();
                        result.add(0,new FilterOptionData(0,"不限"));
                        mView.getNaturesOfCompanySuccess(result);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void getWorkingExp() {
        mPersonalApi.getWorkingExp()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<FilterOptionData> result= (List<FilterOptionData>) baseResponse.getDataList();
                        result.add(0,new FilterOptionData(0,"不限"));
                        mView.getWorkingExpSuccess(result);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void getSalaryRange() {
        mPersonalApi.getSalaryRange()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<FilterOptionData> result= (List<FilterOptionData>) baseResponse.getDataList();
                        result.add(0,new FilterOptionData(0,"不限"));
                        mView.getSalaryRangeSuccess(result);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void getAreaList(int type, int parentId) {
        mZPCommonApi.getAreaList(type, parentId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getAreaListSuccess(type, (List<AreaOptionsData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
