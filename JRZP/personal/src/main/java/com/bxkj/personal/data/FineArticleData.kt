package com.bxkj.personal.data

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.kotlin.fixImgUrl
import com.bxkj.personal.BR
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 *
 * @author: sanjin
 * @date: 2022/8/31
 */
@Parcelize
data class FineArticleData(
    val id: Int,
    val uid: Int,
    val title: String,
    @SerializedName("content", alternate = ["Content"])
    var content: String? = "",
    val createTime: String,
    val pic: String,
    val newsfrom: String,
    @get:Bindable
    var commentsCount: Int,
    val likesCount: Int,
    @SerializedName("nickName")
    val authorName: String,
    @SerializedName("photo")
    val authorAvatar: String,
    var isStow: Boolean,
    @get:Bindable
    @SerializedName("isZan")
    var liked: Int? = 0,
    var noHtmlTagContent: String,
    @get:Bindable
    var zanCount: Int
) : BaseObservable(), Parcelable {

    fun getFixAvatar(): String {
        return authorAvatar.fixImgUrl()
    }

    fun updateLikeState(like: Boolean) {
        liked = if (like) 1 else 0
        if (like) {
            zanCount += 1
        } else {
            zanCount -= 1
        }
        notifyPropertyChanged(BR.liked)
        notifyPropertyChanged(BR.zanCount)
    }

    fun updateCommentCount(count: Int) {
        commentsCount = count
        notifyPropertyChanged(BR.commentsCount)
    }
}