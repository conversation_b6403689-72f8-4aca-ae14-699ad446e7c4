package com.bxkj.personal.ui.activity.selectresume;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.ResumeItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.selectresume
 * @Description: SelectResume
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface SelectResumeContract {
    interface View extends BaseView {
        void getAvailableResumeSuccess(List<ResumeItemData> resumeItemDataList);

        void noResume();

        void submitResumeSuccess();

        void resumeInfoNotPrefect(int resumeId);

        void userInfoCompleted();

        void userInfoNotCompleted();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getAvailableResumeList(int userId, int jobId);

        public abstract void submitResume(int userId, int companyUserId, int jobId, int resumeId, int type,int from);

        public abstract void checkUserInfoCompleted(int userId);
    }
}
