package com.bxkj.personal.data;

import com.bxkj.common.util.TimeUtils;

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
public class StudyNewsItemData {

    private int id;
    private String title;
    private int commentsCount;
    private int count;
    private String createTime;
    private int typeId;
    private String typeName;
    private int typeId2;
    private String typeName2;
    private String subTypeName;
    private String content;
    private String pic;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getCommentsCount() {
        return commentsCount;
    }

    public void setCommentsCount(int commentsCount) {
        this.commentsCount = commentsCount;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public int getTypeId2() {
        return typeId2;
    }

    public void setTypeId2(int typeId2) {
        this.typeId2 = typeId2;
    }

    public String getTypeName2() {
        return typeName2;
    }

    public void setTypeName2(String typeName2) {
        this.typeName2 = typeName2;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getSubTypeName() {
        return subTypeName;
    }

    public void setSubTypeName(String subTypeName) {
        this.subTypeName = subTypeName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTimeDiff() {
        return TimeUtils.formatTimeDiff(createTime, "yyyy/MM/dd HH:mm");
    }
}
