package com.bxkj.personal.ui.fragment.bbs

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalDialogPostBbsNewsBinding

class PostBBSNewsDialog constructor(
    private var postQa: (() -> Unit)? = null,
    private var postMoment: (() -> Unit)? = null
) : BaseDBDialogFragment<PersonalDialogPostBbsNewsBinding, BaseViewModel>() {

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_dialog_post_bbs_news

    override fun initPage() {
        viewBinding.llPostQuestion.setOnClickListener {
            postQa?.invoke()
        }
        viewBinding.llPostMoment.setOnClickListener {
            postMoment?.invoke()
        }
        viewBinding.ivClose.setOnClickListener {
            dismiss()
        }
    }

    override fun enableBottomSheet(): <PERSON><PERSON>an {
        return true
    }
}