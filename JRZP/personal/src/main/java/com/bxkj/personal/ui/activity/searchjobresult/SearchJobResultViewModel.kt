package com.bxkj.personal.ui.activity.searchjobresult

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.widget.filterpopup.FilterGroupTitle
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup
import com.bxkj.jrzp.support.feature.api.FeatureRepository
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.UserResumeData
import com.bxkj.personal.data.source.AddressInfoRepo
import com.bxkj.personal.data.source.JobRepo
import com.bxkj.personal.data.source.PickerOptionsRepo
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class SearchJobResultViewModel @Inject constructor(
  private val _jobRepo: JobRepo,
  private val _My_resumeRepo: MyResumeRepo,
  private val _addressRepo: AddressInfoRepo,
  private val _infoCheckRepo: FeatureRepository,
  private val _pickerOptionsRepo: PickerOptionsRepo,
  private val _commonRepo: CommonRepository
) : BaseViewModel() {

  val searchText = MutableLiveData<String>()

  val jobListViewModel = RefreshListViewModel()

  val toCreateResumeCommand = MutableLiveData<VMEvent<Unit>>()

  val areaList = MutableLiveData<List<AreaOptionsData>>()

  val jobFirstTypeList = MutableLiveData<List<JobTypeData>>()

  val jobSecondTypeList = MutableLiveData<List<JobTypeData>>()

  val filterMoreOptionsLoadedEvent = MutableLiveData<VMEvent<ArrayList<Any>>>()

  private var _filterJobParams: FilterJobParams? = null

  private val _filterMoreOptionsList = ArrayList<Any>()

  init {
    jobListViewModel.setOnLoadDataListener { pageIndex ->
      _filterJobParams?.let { filterParams ->
        viewModelScope.launch {
          _jobRepo.getJobListFullOptions(filterParams, pageIndex, 16)
            .handleResult({
              jobListViewModel.autoAddAll(it)
            }, {
              if (it.isNoDataError) {
                jobListViewModel.noMoreData()
              } else {
                jobListViewModel.loadError()
              }
            })
        }
      }
    }
  }

  fun start(filterJobParams: FilterJobParams?) {
    searchText.value = filterJobParams?.title
    _filterJobParams = filterJobParams ?: FilterJobParams()
      .apply {
        cityId = UserUtils.getUserSelectedCityId()
      }
    jobListViewModel.refresh()
    getFilterSalaryRange()
  }

  fun setSearchKeyword(keyword: String) {
    searchText.value = keyword
    _filterJobParams?.title = keyword
    jobListViewModel.refresh(true)
  }

  fun sendResumePreCheck(job: JobData) {
    viewModelScope.launch {
      showLoading()
      _My_resumeRepo.getUserResumeList(getSelfUserID())
        .handleResult({ resultList ->
          if (resultList.isNullOrEmpty()) {
            toCreateResumeCommand.value = VMEvent(Unit)
          } else {
            val userResume = resultList[0]
            sendResume(job, userResume)
          }
        }, { err ->
          if (err.isNoDataError) {
            toCreateResumeCommand.value = VMEvent(Unit)
          } else {
            showToast(err.errMsg)
          }
        }, {
          hideLoading()
        })
    }
  }

  fun setJobListSort(sort: Int) {
    _filterJobParams?.sort = sort
    jobListViewModel.refresh(true)
  }

  fun setJobListArea(areaId: Int) {
    _filterJobParams?.areaId = areaId
    jobListViewModel.refresh(true)
  }

  fun setJobListCity(cityId: Int) {
    _filterJobParams?.cityId = cityId
    jobListViewModel.refresh(true)
  }

  fun changeFilterAreaCity(cityId: Int) {
    _addressRepo.getAddressList(
      CommonApiConstants.GET_AREA_TYPE,
      cityId,
      object : ResultDataCallBack<List<AreaOptionsData>> {
        override fun onSuccess(data: List<AreaOptionsData>?) {
          data?.let {
            areaList.value = ArrayList(data).apply {
              add(
                0,
                AreaOptionsData(0, "全${UserUtils.getUserSelectedCityName()}")
              )
            }
          }
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  fun setJobListFirstType(id: Int) {
    _filterJobParams?.jobFirstTypeId = id
    loadJobSecondTypeList(id)
  }

  fun setJobListSecondType(id: Int) {
    _filterJobParams?.jobSecondTypeId = id
    jobListViewModel.refresh(true)
  }

  fun setJobListMoreOptions(
    salaryRange: Int,
    companyNature: Int,
    workExp: Int,
    edu: Int,
    publishDate: Int,
    workNature: Int,
    specialReq: Int
  ) {
    _filterJobParams?.apply {
      salaryId = salaryRange
      natureOfCompanyId = companyNature
      workingExpId = workExp
      degreeId = edu
      timeId = publishDate
      natureOfJobId = workNature
      teshutype = specialReq
    }
    jobListViewModel.refresh(true)
  }

  fun loadJobFirstTypeList() {
    viewModelScope.launch {
      _infoCheckRepo.getJobType(1, 0)
        .handleResult({
          it?.let {
            jobFirstTypeList.value = it
            loadJobSecondTypeList(it[0].id)
          }
        }, {
          showToast("职位分类加载失败")
        })
    }
  }

  private fun loadJobSecondTypeList(firstTypeId: Int) {
    viewModelScope.launch {
      _infoCheckRepo.getJobType(2, firstTypeId)
        .handleResult({
          jobSecondTypeList.value = it
        }, {
          showToast("职位分类加载失败")
        })
    }
  }

  private fun getFilterSalaryRange() {
    viewModelScope.launch {
      _pickerOptionsRepo.getSalaryRange()
        .handleResult({
          _filterMoreOptionsList.add(FilterGroupTitle("月薪范围"))
          _filterMoreOptionsList.add(
            FilterOptionsGroup(
              FilterOptionData.SALARY,
              ArrayList(it).apply {
                add(
                  0,
                  FilterOptionData(0, "不限")
                )
              })
          )
          getFilterCompanyNature()
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun getFilterCompanyNature() {
    viewModelScope.launch {
      _pickerOptionsRepo.getNaturesOfCompany()
        .handleResult({
          _filterMoreOptionsList.add(FilterGroupTitle("公司性质"))
          _filterMoreOptionsList.add(
            FilterOptionsGroup(
              FilterOptionData.NATURE_OF_COMPANY,
              ArrayList(it).apply {
                find { item->item.id==0 }?.name="不限"
              }
            )
          )
          getFilterWorkExp()
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun getFilterWorkExp() {
    viewModelScope.launch {
      _commonRepo.getWorkExp()
        .handleResult({
          _filterMoreOptionsList.add(FilterGroupTitle("工作经验"))
          _filterMoreOptionsList.add(
            FilterOptionsGroup(
              FilterOptionData.WORKING_EXP,
              ArrayList(it).apply {
                add(
                  0,
                  FilterOptionData(0, "不限")
                )
              }
            )
          )
          getFilterEduOptions()
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun getFilterEduOptions() {
    viewModelScope.launch {
      _commonRepo.getEduOptions()
        .handleResult({
          _filterMoreOptionsList.add(FilterGroupTitle("学历"))
          _filterMoreOptionsList.add(
            FilterOptionsGroup(
              FilterOptionData.EDU,
              ArrayList(it).apply {
                add(
                  0,
                  FilterOptionData(0, "不限")
                )
              }
            )
          )
          filterMoreOptionsLoadedEvent.value = VMEvent(_filterMoreOptionsList)
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun sendResume(job: JobData, userResume: UserResumeData) {
    viewModelScope.launch {
      showLoading()
      _jobRepo.sendResume(getSelfUserID(), job.uid, job.id, userResume.id)
        .handleResult({
          job.markJobApplied()
          showToast("申请成功")
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }
}