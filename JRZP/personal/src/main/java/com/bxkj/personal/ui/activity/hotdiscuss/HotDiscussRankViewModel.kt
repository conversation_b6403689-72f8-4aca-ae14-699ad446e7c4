package com.bxkj.personal.ui.activity.hotdiscuss

import androidx.activity.result.ActivityResult
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.DiscussData
import com.bxkj.personal.data.source.QuestionRepo
import com.elvishew.xlog.XLog
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/5
 */
class HotDiscussRankViewModel @Inject constructor(
    private val qaRepo: QuestionRepo
) : BaseViewModel() {

    val hotDiscussListViewModel = RefreshListViewModel()

    init {
        hotDiscussListViewModel.refreshLayoutViewModel.enableLoadMore(false)
        hotDiscussListViewModel.setOnLoadDataListener {
            viewModelScope.launch {
                qaRepo.getHotDiscuss(30)
                    .handleResult({
                        it?.let {
                            hotDiscussListViewModel.autoAddAll(it)
                        } ?: let {
                            hotDiscussListViewModel.loadError()
                        }
                    }, {
                        if (it.isNoDataError) {
                            hotDiscussListViewModel.noMoreData()
                        } else {
                            hotDiscussListViewModel.loadError()
                        }
                    })
            }
        }
    }

    fun start() {
        hotDiscussListViewModel.refresh()
    }

    fun handleDetailsPageResult(it: ActivityResult?) {
        it?.data?.let {
            val resultData =
                it.getParcelableExtra<DiscussData>(HotDiscussActivity.EXTRA_DISCUSS_INFO)
            resultData?.let {
                XLog.d(it)
                hotDiscussListViewModel.data?.find { item ->
                    (item is DiscussData) && (item.wdid == resultData.wdid)
                }?.let { find ->
                    (find as DiscussData).apply {
                        updateCommentCount(resultData.plCount.getOrDefault())
                        updateLikeCount(resultData.zanCount)
                    }
                }
            }
        }
    }
}