package com.bxkj.personal.ui.activity.resumedetails.itemviewbinder;

import android.content.Context;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.HtmlUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.ResumeBasicData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder
 * @Description: 求职意向
 * @TODO: TODO
 * @date 2018/8/14
 */
public class CareerObjectiveViewBinder implements ItemViewBinder<ResumeBasicData> {

  private Context mContext;

  public CareerObjectiveViewBinder(Context context) {
    mContext = context;
  }

  @Override
  public void onBindViewHolder(SuperViewHolder holder, ResumeBasicData item, int position) {
    //        holder.setText(R.id.tv_expected_industry, HtmlUtils.fromHtml(mContext.getString(R.string.resume_details_expected_industry_format, item.getTradeName())));
    holder.setText(R.id.tv_expected_career, HtmlUtils.fromHtml(
        mContext.getString(R.string.resume_details_expected_career_format,
            item.getWillMoneyText())));
    holder.setText(R.id.tv_career, HtmlUtils.fromHtml(
        mContext.getString(R.string.resume_details_career_format, item.getJobName2())));
    holder.setText(R.id.tv_work_nature, HtmlUtils.fromHtml(
        mContext.getString(R.string.resume_details_work_nature_format, item.getJnName())));
    holder.setText(R.id.tv_work_address, HtmlUtils.fromHtml(
        mContext.getString(R.string.resume_details_work_address_format,
            item.getShiName())));
    holder.setText(R.id.tv_available_time, HtmlUtils.fromHtml(
        mContext.getString(R.string.resume_details_available_time_format, item.getDaogangName())));
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_recycler_resume_career_objective;
  }
}
