package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/5/14
 */

public class MessageItemData {


    /**
     * id : 4
     * dyid : 0
     * uid : 0
     * ntid : 1
     * newsid : 0
     * look : 0
     * date : 2018/5/12 15:35:36
     * pageIndex : 0
     * pageSize : 0
     * news : {"id":53129,"title":"2018年5月9日西安市人才市场高校毕业生未就业专场招聘会","date":"2018/4/24 22:57:00","htc":0,"pic":null,"shi":0,"shiName":null,"from":"北京招聘会"}
     */

    private int id;
    private int dyid;
    private int uid;
    private int ntid;
    private int newsid;
    private int look;
    private String date;
    private int pageIndex;
    private int pageSize;
    private NewsBean news;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDyid() {
        return dyid;
    }

    public void setDyid(int dyid) {
        this.dyid = dyid;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getNtid() {
        return ntid;
    }

    public void setNtid(int ntid) {
        this.ntid = ntid;
    }

    public int getNewsid() {
        return newsid;
    }

    public void setNewsid(int newsid) {
        this.newsid = newsid;
    }

    public int getLook() {
        return look;
    }

    public void setLook(int look) {
        this.look = look;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public NewsBean getNews() {
        return news;
    }

    public void setNews(NewsBean news) {
        this.news = news;
    }

    public static class NewsBean {
        /**
         * id : 53129
         * title : 2018年5月9日西安市人才市场高校毕业生未就业专场招聘会
         * date : 2018/4/24 22:57:00
         * htc : 0
         * pic : null
         * shi : 0
         * shiName : null
         * from : 北京招聘会
         */

        private int id;
        private String title;
        private String date;
        private int htc;
        private Object pic;
        private int shi;
        private Object shiName;
        private String from;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public int getHtc() {
            return htc;
        }

        public void setHtc(int htc) {
            this.htc = htc;
        }

        public Object getPic() {
            return pic;
        }

        public void setPic(Object pic) {
            this.pic = pic;
        }

        public int getShi() {
            return shi;
        }

        public void setShi(int shi) {
            this.shi = shi;
        }

        public Object getShiName() {
            return shiName;
        }

        public void setShiName(Object shiName) {
            this.shiName = shiName;
        }

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }
    }
}
