package com.bxkj.personal.ui.fragment.campustalk

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.*
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.personal.data.CampusTalkData
import com.bxkj.personal.data.source.JobRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class CampusTalkViewModel @Inject constructor(
  private val _jobRepo: JobRepo
) : BaseViewModel() {

  val schoolList = MutableLiveData<List<FilterOptionData>>()
  val cityList = MutableLiveData<List<FilterOptionData>>()

  val refreshCampusTalkCommand = MutableLiveData<VMEvent<Unit>>()

  private var _searchKeyword = ""

  private var _filterSchoolId = ""
  private var _dateFlag = ""

  val campusTalkListFlow = Pager(PagingConfig(16, initialLoadSize = 16)) {
    object : PagingSource<Int, CampusTalkData>() {
      override fun getRefreshKey(state: PagingState<Int, CampusTalkData>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
          val anchorPage = state.closestPageToPosition(anchorPosition)
          anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
      }

      override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CampusTalkData> {
        val pageIndex = params.key.getOrDefault(1)
        var loadResult: LoadResult<Int, CampusTalkData> =
          LoadResult.Invalid()

        _jobRepo.getCampusTalkList(
          pageIndex,
          params.loadSize,
          _searchKeyword,
          _filterSchoolId,
          _dateFlag
        ).handleResult(
          {
            if (it?.dataList.isNullOrEmpty()) {
              loadResult = LoadResult.Error(RespondThrowable.getNoDataError())
            } else {
              loadResult = LoadResult.Page(
                it?.dataList ?: emptyList(),
                if (pageIndex == 1) null else pageIndex - 1,
                if (it?.dataList.isNullOrEmpty()) null else pageIndex + 1
              )
            }
          }, {
            loadResult = LoadResult.Error(it)
          }
        )
        return loadResult
      }
    }
  }.flow.cachedIn(viewModelScope)

  fun start() {
    if (UserUtils.getUserSelectedCityId() > 0) {
      refreshSchoolList()
    }
    getCampusTalkCityList()
  }

  fun refreshSchoolList() {
    viewModelScope.launch {
      _jobRepo.getCampusTalkSchoolList(UserUtils.getUserSelectedCityId())
        .handleResult({
          it?.dataList?.let { list ->
            schoolList.value = list
            _filterSchoolId = list.joinToString(",") { item -> item.id.toString() }
            refreshCampusTalkCommand.value = VMEvent(Unit)
          }
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun getCampusTalkCityList() {
    viewModelScope.launch {
      _jobRepo.getCampusRecruitCityList().handleResult({
        it?.dataList?.let { list ->
          cityList.value = list
        }
      }, {
        showToast(it.errMsg)
      })
    }
  }

  fun setFilterSchool(schoolList: List<FilterOptionData>) {
    val selectedSchoolList = if (schoolList.isNotEmpty()) schoolList else this.schoolList.value
    if (!selectedSchoolList.isNullOrEmpty()) {
      _filterSchoolId = selectedSchoolList.joinToString(",") { item -> item.id.toString() }
    }
    refreshCampusTalkCommand.value = VMEvent(Unit)
  }

  fun setFilterDate(date: String) {
    _dateFlag = date
    refreshCampusTalkCommand.value = VMEvent(Unit)
  }

  fun setSearchKeyword(keyword: String) {
    _searchKeyword = keyword
  }
}