package com.bxkj.personal.ui.activity.createresumesteptwo;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.CreateResumeData;
import com.bxkj.personal.data.ResumeDefaultData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumesteptwo
 * @Description: CreateReusmeStepTwo
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CreateResumeStepTwoContract {
    interface View extends BaseView {

        void getDefaultInfoSuccess(ResumeDefaultData resumeDefaultData);

        void createResumeSuccess(int resumeId);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {

        abstract void getCreateResumeDefaultInfo(int userId);

        abstract void createResume(CreateResumeData createResumeData);

    }
}
