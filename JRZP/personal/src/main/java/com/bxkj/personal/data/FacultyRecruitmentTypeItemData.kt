package com.bxkj.personal.data

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * <AUTHOR>
 * @date 2019/10/18
 * @version V1.0
 */
data class FacultyRecruitmentTypeItemData(
        var id: Int,
        var name: String
) {
    companion object{
        fun getDefault(): FacultyRecruitmentTypeItemData {
            return FacultyRecruitmentTypeItemData(0, "全部")
        }
    }
}