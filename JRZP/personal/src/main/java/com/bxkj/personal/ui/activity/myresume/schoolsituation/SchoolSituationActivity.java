package com.bxkj.personal.ui.activity.myresume.schoolsituation;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.picker.OneOptionPicker;
import com.bxkj.common.util.picker.PickerUtils;
import com.bxkj.common.widget.MyEditText;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.personal.R;
import com.bxkj.personal.data.SchoolSituationItemData;
import com.bxkj.personal.ui.activity.editinfo.EditInfoActivity;
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.schoolsituation
 * @Description: 在校情況
 * @TODO: TODO
 * @date 2018/5/12
 */

public class SchoolSituationActivity extends BaseDaggerActivity
  implements SchoolSituationContract.View {

  private static final String START_TYPE = "startType";
  private static final String RESUME_ID = "resumeId";
  private static final String DETAILS_ID = "skillId";

  public static final int CREATE_TYPE = 0x01;
  public static final int UPDATE_TYPE = 0x02;

  private static final int TO_EDIT_DESC_CODE = 0x01;

  @Inject
  SchoolSituationPresenter mSchoolSituationPresenter;

  private TextView tvType;
  private MyEditText etName;
  private TextView tvDesc;
  private TextView tvDeleteThis;

  private int mPageType;
  private int mResumeId;
  private int mDetailsId;

  private OptionsPickerView mSchoolSituationPicker;

  private SchoolSituationItemData mSchoolSituationItemData;

  public static Intent newIntent(Activity activity, int startType, int resumeId, int detailsId) {
    Intent intent = new Intent(activity, SchoolSituationActivity.class);
    intent.putExtra(START_TYPE, startType);
    intent.putExtra(RESUME_ID, resumeId);
    intent.putExtra(DETAILS_ID, detailsId);
    return intent;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_school_situation;
  }

  @Override
  protected void initPresenter() {
    mSchoolSituationPresenter.attachView(this);
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mPageType = getIntent().getIntExtra(START_TYPE, CommonApiConstants.NO_DATA);
    if (mPageType == CREATE_TYPE) {
      tvDeleteThis.setVisibility(View.GONE);
      mResumeId = getIntent().getIntExtra(RESUME_ID, CommonApiConstants.NO_DATA);
      mSchoolSituationItemData = new SchoolSituationItemData();
    } else {
      mDetailsId = getIntent().getIntExtra(DETAILS_ID, CommonApiConstants.NO_DATA);
      mSchoolSituationPresenter.getSchoolSituationDetails(getMUserID(), mDetailsId);
    }

    initSchoolSituationPicker();
  }

  private void initSchoolSituationPicker() {
    List<PickerOptionsData> pickerOptionsData = PickerUtils.parsePickerDataList(
      getResources().getStringArray(R.array.personal_school_situation));
    mSchoolSituationPicker = new OneOptionPicker(this)
      .setOptionsDataList(pickerOptionsData)
      .setOnConfirmListener(position -> tvType.setText(pickerOptionsData.get(position).getName()))
      .create();
  }


  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_school_situation))
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> submitInfo());
  }

  private void submitInfo() {
    mSchoolSituationItemData.setType(tvType.getText().toString());
    mSchoolSituationItemData.setName(etName.getText().toString());
    mSchoolSituationItemData.setRemark(tvDesc.getText().toString());
    if (mPageType == CREATE_TYPE) {
      tvDeleteThis.setVisibility(View.GONE);
      mSchoolSituationPresenter.addSchoolSituation(getMUserID(), mResumeId,
        mSchoolSituationItemData);
    } else {
      mSchoolSituationPresenter.updateSchoolSituation(getMUserID(), mDetailsId,
        mSchoolSituationItemData);
    }
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_type) {
      mSchoolSituationPicker.show();
    } else if (view.getId() == R.id.tv_desc) {
      startActivityForResult(
        EditInfoActivity.newIntent(this, getString(R.string.personal_language_desc),
          getString(R.string.personal_input_school_situation_desc_hint),
          tvDesc.getText().toString(), 2000), TO_EDIT_DESC_CODE);
    } else {
      new ActionDialog.Builder()
        .setTitle(getString(R.string.common_confirm_delete_information))
        .setContent(getString(R.string.common_unrecoverable_information_after_deletion))
        .setOnConfirmClickListener((dialog) -> mSchoolSituationPresenter.deleteSchoolSituation(
          getMUserID(), mDetailsId))
        .build()
        .show(getSupportFragmentManager(), ActionDialog.TAG);
    }
  }

  @Override
  public void getSchoolSituationDetailsSuccess(SchoolSituationItemData schoolSituationItemData) {
    mSchoolSituationItemData = schoolSituationItemData;
    tvType.setText(schoolSituationItemData.getType());
    etName.setText(schoolSituationItemData.getName());
    tvDesc.setText(schoolSituationItemData.getRemark());
  }

  @Override
  public void addSchoolSituationSuccess() {
    showToast(getString(R.string.common_save_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void updateSchoolSituationSuccess() {
    showToast(getString(R.string.common_save_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void deleteSchoolSituationSuccess() {
    showToast(getString(R.string.common_delete_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_EDIT_DESC_CODE && resultCode == RESULT_OK && data != null) {
      String resultText = data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT);
      tvDesc.setText(resultText);
    }
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mSchoolSituationPresenter.detachView();
  }

  private void bindView(View bindSource) {
    tvType = bindSource.findViewById(R.id.tv_type);
    etName = bindSource.findViewById(R.id.et_name);
    tvDesc = bindSource.findViewById(R.id.tv_desc);
    tvDeleteThis = bindSource.findViewById(R.id.tv_delete_this);
    bindSource.findViewById(R.id.tv_type).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_desc).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_delete_this).setOnClickListener(v -> onViewClicked(v));
  }
}
