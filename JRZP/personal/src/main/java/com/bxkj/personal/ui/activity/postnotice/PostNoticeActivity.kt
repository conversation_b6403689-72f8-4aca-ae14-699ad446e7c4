package com.bxkj.personal.ui.activity.postnotice

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.lifecycle.Observer
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.qmui.AndroidBug5497Workaround
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityPostNoticeBinding
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.postnotice
 * @Description:
 * <AUTHOR>
 * @date 2020/1/2
 * @version V1.0
 */
@Route(path = PostNoticeNavigation.PATH)
class PostNoticeActivity : BaseDBActivity<PersonalActivityPostNoticeBinding, PostNoticeViewModel>(),
  View.OnClickListener {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, PostNoticeActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<PostNoticeViewModel> = PostNoticeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_post_notice

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this
    AndroidBug5497Workaround.assistActivity(this)
    setupRichEditor()

    viewBinding.titleBar.setRightOptionClickListener {
      viewModel.postNotice(viewBinding.richEditor.html)
    }

    subscribeViewModelEvent()

    initView()

    viewModel.setupPageParams(
      intent.getIntExtra(
        PostNoticeNavigation.EXTRA_POST_TYPE,
        CommonApiConstants.NO_ID
      ),
      intent.getIntExtra(
        PostNoticeNavigation.EXTRA_INFO_ID,
        CommonApiConstants.NO_ID
      ),
      intent.getBooleanExtra(
        PostNoticeNavigation.EXTRA_TO_USER_HOME,
        false
      )
    )
  }

  private fun initView() {
    if (intent.getIntExtra(PostNoticeNavigation.EXTRA_INFO_ID, CommonApiConstants.NO_ID) != 0) {
      viewBinding.titleBar.setTitle("修改公告")
      viewBinding.titleBar.setRightText("修改")
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.noticeInfo.observe(this, Observer {
      viewBinding.richEditor.html = it.content
    })

    viewModel.picUploadSuccessEvent.observe(this, Observer {
      viewBinding.richEditor.insertImage(it)
    })

    viewModel.updateSuccessEvent.observe(this, Observer {
      showToast(R.string.common_update_success)
      finish()
    })

    viewModel.postSuccessEvent.observe(this, Observer {
      UserHomeNavigation.navigate(it.userID).start()
      finish()
    })
  }

  private fun setupRichEditor() {
    viewBinding.richEditor.setHintColor("#999999")
    viewBinding.richEditor.setFontSize(7)
    viewBinding.richEditor.setPadding(12, 16, 12, 16)
    viewBinding.richEditor.setHint(getString(R.string.post_notice_content_hint))

    viewBinding.richEditor.setOnFocusChangeListener { v, hasFocus ->
      if (hasFocus) {
        viewBinding.llBottomBar.visibility = View.VISIBLE
      } else {
        viewBinding.llBottomBar.visibility = View.GONE
      }
    }

    viewBinding.richEditor.setOnTouchListener { v, event ->
      if (event.action == MotionEvent.ACTION_MOVE) {
        SystemUtil.hideSoftKeyboard(this)
      }
      return@setOnTouchListener super.onTouchEvent(event)
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_upload_photo -> {
          PermissionUtils.requestPermission(
            this,
            getString(R.string.permission_tips_title),
            getString(R.string.permission_select_img_tips),
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                PictureSelector.create(this@PostNoticeActivity)
                  .openGallery(SelectMimeType.ofImage())
                  .setSelectionMode(SelectModeConfig.SINGLE)
                  .setSandboxFileEngine(SandboxFileEngine.getInstance())
                  .setCompressEngine(ImageCompressEngine.getInstance())
                  .setImageEngine(GlideEngine.getInstance())
                  .setImageSpanCount(4)
                  .forResult(PictureConfig.CHOOSE_REQUEST)
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel_operation))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE
          )
        }

        R.id.iv_last -> {
          viewBinding.richEditor.undo()
        }

        R.id.iv_next -> {
          viewBinding.richEditor.redo()
        }

        R.id.iv_bold -> {
          SystemUtil.showSoftKeyboardForView(viewBinding.richEditor)
          viewBinding.richEditor.setBold()
        }

        R.id.iv_underline -> {
          SystemUtil.showSoftKeyboardForView(viewBinding.richEditor)
          viewBinding.richEditor.setUnderline()
        }

        R.id.iv_title -> {
          SystemUtil.showKeyboard(viewBinding.richEditor)
          viewBinding.richEditor.setHeading(4)
        }

        R.id.iv_unorderedlist -> {
          SystemUtil.showKeyboard(viewBinding.richEditor)
          viewBinding.richEditor.setBullets()
        }

        R.id.iv_orderedlist -> {
          viewBinding.richEditor.setNumbers()
        }
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST && resultCode == Activity.RESULT_OK && data != null) {
      viewModel.uploadPicture(data.getSelectedFirstMediaPath())
    }
  }
}