package com.bxkj.personal.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.ecommon.BR

/**
 * Description:
 * Author:45457
 **/
data class CampusRecruitJobBean(
    var uid: Int,
    var id: Int,
    var name: String? = "",
    var city: String? = "",
    var jaid: Int? = 0,
    var IsRes: Int? = 0
) : BaseObservable() {

    /**
     * 是否已经投递过简历
     */
    @Bindable
    fun getHasSend(): Boolean {
        return IsRes == 1
    }

    /**
     * 标记已经投递过简历
     */
    fun markSend() {
        IsRes = 1
        notifyPropertyChanged(BR.hasSend)
    }

    class DiffCallback : DiffUtil.ItemCallback<CampusRecruitJobBean>() {

        override fun areItemsTheSame(oldItem: CampusRecruitJobBean, newItem: CampusRecruitJobBean): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: CampusRecruitJobBean, newItem: CampusRecruitJobBean): Boolean {
            return oldItem.name == newItem.name
        }
    }
}