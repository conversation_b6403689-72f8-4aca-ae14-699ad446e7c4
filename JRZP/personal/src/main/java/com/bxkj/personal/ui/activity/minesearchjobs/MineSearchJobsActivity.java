package com.bxkj.personal.ui.activity.minesearchjobs;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.picker.OneOptionPicker;
import com.bxkj.common.util.picker.PickerUtils;
import com.bxkj.common.util.router.Router;
import com.bxkj.common.util.router.RouterNavigation;
import com.bxkj.common.widget.MyEditText;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.common.widget.popup.OptionsPickerPopup;
import com.bxkj.jrzp.support.db.SearchSqlLiteHelper;
import com.bxkj.personal.R;
import com.bxkj.personal.mvp.contract.EducationContract;
import com.bxkj.personal.mvp.contract.GetFilterOptionsContract;
import com.bxkj.personal.mvp.contract.JobCountContract;
import com.bxkj.personal.mvp.presenter.EducationPresenter;
import com.bxkj.personal.mvp.presenter.GetFilterOptionsPresenter;
import com.bxkj.personal.mvp.presenter.JobCountPresenter;
import com.bxkj.personal.ui.activity.searchjobresult.FilterJobParams;
import com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultActivityV2;
import com.bxkj.personal.ui.activity.selectarea.SelectAreaActivity;
import com.therouter.router.Route;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;

/**
 * @Description: 根据条件搜索职位
 * @TODO: TODO
 * @date 2018/5/28
 */
@Route(path = SearchJobNavigation.PATH)
public class MineSearchJobsActivity extends BaseDaggerActivity
  implements GetFilterOptionsContract.View, JobCountContract.View, EducationContract.View {

  private static final int TO_SELECT_AREA_CODE = 1;
  private static final int TO_SELECT_EXPECT_INDUSTRY_CODE = 2;

  @Inject
  GetFilterOptionsPresenter mGetFilterOptionsPresenter;
  @Inject
  JobCountPresenter mJobCountPresenter;
  @Inject
  EducationPresenter mEducationPresenter;

  private LinearLayout llMoreOptions;
  private TextView tvExpandOrCollapse;
  private TextView tvPublishDate;
  private TextView tvExpectSalary;
  private TextView tvCompanyNature;
  private TextView tvLocation;
  private TextView tvArea;
  private MyEditText etSearchJob;
  private RecyclerView recyclerSearchRecord;
  private RelativeLayout rlSearchTool;
  private TextView tvExpectIndustry;
  private TextView tvWorkExp;
  private TextView tvEducation;
  private TextView tvWorkingNature;

  private OptionsPickerView mPublishDatePicker;
  private OptionsPickerPopup mWorkExpPicker;
  private OptionsPickerPopup mEducationPicker;
  private OptionsPickerView mWorkNaturePicker;
  private OptionsPickerPopup mExpectSalaryPicker;
  private OptionsPickerPopup mCompanyNaturePicker;

  private List<FilterOptionData> mExpectSalaryList;
  private List<FilterOptionData> mWorkExpList;
  private List<FilterOptionData> mEducationList;
  private List<FilterOptionData> mCompanyNatureList;

  private FilterJobParams mFilterParameters;
  private SearchSqlLiteHelper mSearchSqlLiteHelper;
  private List<String> mRecordTitleList;
  private List<MineSearchJobsRecordItem> mRecordList;
  private MineSearchJobsRecordAdapter mMineSearchJobsRecordAdapter;

  public static Intent newIntent(Activity activity) {
    Intent intent = new Intent(activity, MineSearchJobsActivity.class);
    return intent;
  }

  @Override
  protected void initPresenter() {
    mGetFilterOptionsPresenter.attachView(this);
    mJobCountPresenter.attachView(this);
    mEducationPresenter.attachView(this);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_condition_search_jobs;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mFilterParameters = new FilterJobParams();
    initSearchRecord();
    initWorkExpPicker();
    initEducationPicker();
    initPublishDatePicker();
    initSalaryPicker();
    initWorkNaturePicker();
    initCompanyNaturePicker();
  }

  //搜索历史记录
  private void initSearchRecord() {
    mRecordList = new ArrayList<>();
    mMineSearchJobsRecordAdapter = new MineSearchJobsRecordAdapter(this, null,
      R.layout.personal_recycler_mine_search_job_record_item);
    mMineSearchJobsRecordAdapter.setOnItemClickListener((view, position) -> {
      String searchText = mMineSearchJobsRecordAdapter.getData().get(position).getTitle();
      etSearchJob.setText(searchText);
      startActivity(
        SearchJobResultActivityV2.newIntent(this, FilterJobParams.getSearchParams(searchText)));
    });
    recyclerSearchRecord.setLayoutManager(new LinearLayoutManager(this));
    recyclerSearchRecord.setAdapter(mMineSearchJobsRecordAdapter);
    mSearchSqlLiteHelper =
      new SearchSqlLiteHelper(this, SearchSqlLiteHelper.MINE_SEARCH_JOB_RECORD_TABLE_NAME);
  }

  //学历picker
  private void initEducationPicker() {
    mEducationPicker = new OptionsPickerPopup(this)
      .setOnConfirmClickListener((firstPosition, secondPosition, thirdPosition) -> {
        if (!CheckUtils.isNullOrEmpty(mEducationList)) {
          tvEducation.setText(mEducationList.get(firstPosition).getName());
          mFilterParameters.setDegreeId(mEducationList.get(firstPosition).getId());
        }
      });
    mEducationPresenter.getEducation();
  }

  //工作性质picker
  private void initWorkNaturePicker() {
    List<PickerOptionsData> publishDateList = PickerUtils.parsePickerDataList(
      getResources().getStringArray(R.array.personal_search_jobs_work_nature));
    mWorkNaturePicker = new OneOptionPicker(this)
      .setOptionsDataList(publishDateList)
      .setOnConfirmListener(position -> {
        tvWorkingNature.setText(publishDateList.get(position).getName());
        //if (position == 0) {
        //  mFilterParameters.setNatureOfJobId(7);
        //} else {
        //  mFilterParameters.setNatureOfJobId(9);
        //}
        int natureID = publishDateList.get(position).getId();
        mFilterParameters.setNatureOfJobId(natureID == 0 ? 0 : natureID + 7);
      }).create();
  }

  //工作经验picker
  private void initWorkExpPicker() {
    mWorkExpPicker = new OptionsPickerPopup(this)
      .setOnConfirmClickListener((firstPosition, secondPosition, thirdPosition) -> {
        if (!CheckUtils.isNullOrEmpty(mWorkExpList)) {
          tvWorkExp.setText(mWorkExpList.get(firstPosition).getName());
          mFilterParameters.setWorkingExpId(mWorkExpList.get(firstPosition).getId());
        }
      });
    mGetFilterOptionsPresenter.getWorkingExp();
  }

  //发布时间picker
  private void initPublishDatePicker() {
    List<PickerOptionsData> publishDateList = PickerUtils.parsePickerDataList(
      getResources().getStringArray(R.array.personal_jobs_publish_date));
    mPublishDatePicker = new OneOptionPicker(this)
      .setOptionsDataList(publishDateList)
      .setOnConfirmListener(position -> {
        tvPublishDate.setText(publishDateList.get(position).getName());
        mFilterParameters.setTimeId(publishDateList.get(position).getId());
      }).create();
  }

  //初始化薪资筛选picker
  private void initSalaryPicker() {
    mExpectSalaryPicker = new OptionsPickerPopup(this)
      .setOnConfirmClickListener((firstPosition, secondPosition, thirdPosition) -> {
        if (!CheckUtils.isNullOrEmpty(mExpectSalaryList)) {
          tvExpectSalary.setText(mExpectSalaryList.get(firstPosition).getName());
          mFilterParameters.setSalaryId(mExpectSalaryList.get(firstPosition).getId());
        }
      });
    mGetFilterOptionsPresenter.getSalaryRange();
  }

  //初始化公司性质picker
  private void initCompanyNaturePicker() {
    mCompanyNaturePicker = new OptionsPickerPopup(this)
      .setOnConfirmClickListener((firstPosition, secondPosition, thirdPosition) -> {
        if (!CheckUtils.isNullOrEmpty(mCompanyNatureList)) {
          tvCompanyNature.setText(mCompanyNatureList.get(firstPosition).getName());
          mFilterParameters.setNatureOfCompanyId(mCompanyNatureList.get(firstPosition).getId());
        }
      });
    mGetFilterOptionsPresenter.getNaturesOfCompany();
  }

  @Override
  protected void onResume() {
    super.onResume();
    tvLocation.setText(UserUtils.getUserSelectedCityName());
    mFilterParameters.setCityId(UserUtils.getUserSelectedCityId());
    getRecordListData();
  }

  private void getRecordListData() {
    mRecordList.clear();
    mRecordTitleList = mSearchSqlLiteHelper.getAllData();
    rlSearchTool.setVisibility(
      CheckUtils.isNullOrEmpty(mRecordTitleList) ? View.GONE : View.VISIBLE);
    Collections.reverse(mRecordTitleList);
    for (String recordItem : mRecordTitleList) {
      mJobCountPresenter.getJobsCountByTitle(UserUtils.getUserSelectedCityId(), recordItem);
    }
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_job_search));
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.ll_select_area) {
      startActivityForResult(SelectAreaActivity.newIntent(this, true), TO_SELECT_AREA_CODE);
    } else if (view.getId() == R.id.tv_expect_industry) {
      Router.getInstance().to(RouterNavigation.JobSelectActivity.URL)
        .withInt(RouterNavigation.TO_ACTIVITY_TYPE, RouterNavigation.JobSelectActivity.RESULT)
        .withInt(RouterNavigation.JobSelectActivity.MAX_SELECT_COUNT, 1)
        .startForResult(this, TO_SELECT_EXPECT_INDUSTRY_CODE);
    } else if (view.getId() == R.id.tv_expand_or_collapse) {
      changOptionsExpandStatus();
    } else if (view.getId() == R.id.tv_publish_date) {
      mPublishDatePicker.show();
    } else if (view.getId() == R.id.tv_expect_salary) {
      mExpectSalaryPicker.show();
    } else if (view.getId() == R.id.tv_work_exp) {
      mWorkExpPicker.show();
    } else if (view.getId() == R.id.tv_education) {
      mEducationPicker.show();
    } else if (view.getId() == R.id.tv_working_nature) {
      mWorkNaturePicker.show();
    } else if (view.getId() == R.id.tv_company_nature) {
      mCompanyNaturePicker.show();
    } else if (view.getId() == R.id.tv_search_job) {
      startSearch(etSearchJob.getText().toString());
    } else {
      if (!CheckUtils.isNullOrEmpty(mRecordList)) {
        new ActionDialog.Builder()
          .setTitle(getString(R.string.common_delete))
          .setContent(getString(R.string.common_confirm_clear_search_record))
          .setOnConfirmClickListener((dialog) -> {
            mSearchSqlLiteHelper.deleteAllData();
            mMineSearchJobsRecordAdapter.clear();
            rlSearchTool.setVisibility(View.GONE);
          }).build().show(getSupportFragmentManager(), ActionDialog.TAG);
      }
    }
  }

  private void startSearch(String searchText) {
    insertRecord(searchText);
    mFilterParameters.setTitle(searchText);
    startActivity(SearchJobResultActivityV2.newIntent(this, mFilterParameters));
  }

  private void insertRecord(String searchText) {
    if (!CheckUtils.isNullOrEmpty(searchText)) {
      //关键字中的特殊字符转义
      String sqlKeyword = CheckUtils.sqliteEscape(searchText);
      if (mSearchSqlLiteHelper.hasData(sqlKeyword)) {
        mSearchSqlLiteHelper.deleteItemList(sqlKeyword);
      }
      mSearchSqlLiteHelper.addItemList(sqlKeyword);
    }
  }

  private void changOptionsExpandStatus() {
    if (llMoreOptions.isShown()) {
      llMoreOptions.setVisibility(View.GONE);
      tvExpandOrCollapse.setText(getString(R.string.personal_select_more_options));
      tvExpandOrCollapse.setCompoundDrawablesWithIntrinsicBounds(0, 0,
        R.drawable.personal_ic_search_job_expand_options, 0);
    } else {
      llMoreOptions.setVisibility(View.VISIBLE);
      tvExpandOrCollapse.setText(getString(R.string.personal_collapse_more_options));
      tvExpandOrCollapse.setCompoundDrawablesWithIntrinsicBounds(0, 0,
        R.drawable.personal_ic_search_job_collapse_options, 0);
    }
  }

  @Override
  public void getNaturesOfCompanySuccess(List<FilterOptionData> filterOptionDataList) {
    mCompanyNatureList = filterOptionDataList;
    mCompanyNaturePicker.setFirstOptions(filterOptionDataList);
  }

  @Override
  public void getWorkingExpSuccess(List<FilterOptionData> filterOptionDataList) {
    mWorkExpList = filterOptionDataList;
    mWorkExpPicker.setFirstOptions(filterOptionDataList);
  }

  @Override
  public void getSalaryRangeSuccess(List<FilterOptionData> filterOptionDataList) {
    mExpectSalaryList = filterOptionDataList;
    mExpectSalaryPicker.setFirstOptions(filterOptionDataList);
  }

  @Override
  public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {

  }

  @Override
  public void getEducationSuccess(List<FilterOptionData> filterOptionDataList) {
    mEducationList = filterOptionDataList;
    mEducationPicker.setFirstOptions(filterOptionDataList);
  }

  @Override
  public void getJobCountSuccess(String title, int count) {
    mRecordList.add(new MineSearchJobsRecordItem(title, count));
    if (mRecordList.size() == mRecordTitleList.size()) {
      mMineSearchJobsRecordAdapter.reset(mRecordList);
    }
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK && data != null) {
      if (requestCode == TO_SELECT_AREA_CODE) {
        AreaOptionsData areaItemData = data.getParcelableExtra(SelectAreaActivity.RESULT_DATA);
        tvLocation.setText(UserUtils.getUserSelectedCityName());
        tvArea.setText(areaItemData.getName());
        mFilterParameters.setAreaId(areaItemData.getId());
      } else {
        int resultFirstId =
          data.getIntExtra(RouterNavigation.JobSelectActivity.RESULT_FIRST_CLASS_ID,
            CommonApiConstants.NO_DATA);
        String resultFirstName =
          data.getStringExtra(RouterNavigation.JobSelectActivity.RESULT_FIRST_CLASS_NAME);
        List<JobTypeData> jobTypeData =
          data.getParcelableArrayListExtra(RouterNavigation.JobSelectActivity.RESULT_DATA);
        mFilterParameters.setJobFirstTypeId(resultFirstId);
        mFilterParameters.setJobSecondTypeId(jobTypeData.get(0).getId());
        tvExpectIndustry.setText(
          String.format("%s-%s", resultFirstName, jobTypeData.get(0).getName()));
      }
    }
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mGetFilterOptionsPresenter.detachView();
    mJobCountPresenter.detachView();
    mEducationPresenter.detachView();
  }

  private void bindView(View bindSource) {
    llMoreOptions = bindSource.findViewById(R.id.ll_more_options);
    tvExpandOrCollapse = bindSource.findViewById(R.id.tv_expand_or_collapse);
    tvPublishDate = bindSource.findViewById(R.id.tv_publish_date);
    tvExpectSalary = bindSource.findViewById(R.id.tv_expect_salary);
    tvCompanyNature = bindSource.findViewById(R.id.tv_company_nature);
    tvLocation = bindSource.findViewById(R.id.tv_location);
    tvArea = bindSource.findViewById(R.id.tv_area);
    etSearchJob = bindSource.findViewById(R.id.et_search_job);
    recyclerSearchRecord = bindSource.findViewById(R.id.recycler_search_record);
    rlSearchTool = bindSource.findViewById(R.id.rl_search_tool);
    tvExpectIndustry = bindSource.findViewById(R.id.tv_expect_industry);
    tvWorkExp = bindSource.findViewById(R.id.tv_work_exp);
    tvEducation = bindSource.findViewById(R.id.tv_education);
    tvWorkingNature = bindSource.findViewById(R.id.tv_working_nature);
    bindSource.findViewById(R.id.ll_select_area).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expect_industry).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_publish_date).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expect_salary).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_exp).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_education).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_working_nature).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_company_nature).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expand_or_collapse).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_search_job).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.iv_clear_search_record).setOnClickListener(v -> onViewClicked(v));
  }
}
