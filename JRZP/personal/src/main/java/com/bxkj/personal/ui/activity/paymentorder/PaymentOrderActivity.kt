package com.bxkj.personal.ui.activity.paymentorder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import com.alipay.sdk.app.PayTask
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityPaymentOrderBinding
import com.bxkj.personal.ui.activity.paymentresult.PaymentResultActivity
import com.bxkj.personal.ui.activity.paymentresult.PaymentStatus

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.paymentorder
 * @Description: 支付订单
 * <AUTHOR>
 * @date 2019/12/3
 * @version V1.0
 */
class PaymentOrderActivity : BaseDBActivity<PersonalActivityPaymentOrderBinding, PaymentOrderViewModel>() {

    companion object {
        const val EXTRA_ORDER_TYPE = "order_type"
        const val EXTRA_ORDER_AMOUNT = "order_price"
        const val EXTRA_ORDER_ID = "ORDER_ID"

        fun newIntent(context: Context, orderId: String, amount: Int): Intent {
            return Intent(context, PaymentOrderActivity::class.java)
                    .apply {
                        putExtra(EXTRA_ORDER_ID, orderId)
                        putExtra(EXTRA_ORDER_AMOUNT, amount)
                    }
        }

        fun newIntent(context: Context, orderType: Int, amount: Int): Intent {
            val intent = Intent(context, PaymentOrderActivity::class.java)
            intent.putExtra(EXTRA_ORDER_TYPE, orderType)
            intent.putExtra(EXTRA_ORDER_AMOUNT, amount)
            return intent
        }
    }

    override fun getViewModelClass(): Class<PaymentOrderViewModel> = PaymentOrderViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_payment_order

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        subscribeViewModelCommand()

        viewModel.start(intent)
    }

    private fun subscribeViewModelCommand() {
        viewModel.createOrderSuccessEvent.observe(this, Observer {
            viewModel.payOfAlipay(PayTask(this), it)
        })

        viewModel.paymentSuccessEvent.observe(this, Observer { orderId ->
            toPaymentResultByPaymentStatus(orderId, PaymentStatus.SUCCESS)
        })

        viewModel.checkPaymentResultCommand.observe(this, Observer { orderId ->
            toPaymentResultByPaymentStatus(orderId, PaymentStatus.UNKNOW)
        })
    }

    private fun toPaymentResultByPaymentStatus(orderId: String, paymentStatus: PaymentStatus) {
        startActivity(PaymentResultActivity.newIntent(this, orderId, paymentStatus))
    }
}