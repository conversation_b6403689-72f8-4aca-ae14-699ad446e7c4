package com.bxkj.personal.ui.activity.searchjobresult

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.convertWheelOptions
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.DropDownMenuView
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.filterpopup.*
import com.bxkj.jrzp.support.db.SearchSqlLiteHelper
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivitySearchJobResultV2Binding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.activity.searchjobs.SearchJobsActivity
import com.bxkj.personal.weight.filterareapopup.FilterAreaView
import com.bxkj.personal.weight.filterareapopup.FilterAreaView.OnAreaItemClickListener
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobClassPopup
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobTypeView

class SearchJobResultActivityV2 :
  BaseDBActivity<PersonalActivitySearchJobResultV2Binding, SearchJobResultViewModel>(),
  OnClickListener {

  companion object {

    private const val EXTRA_FILTER_PARAMS = "FILTER_PARAMS"

    @JvmStatic
    fun newIntent(content: Context, filterJobParams: FilterJobParams? = null): Intent {
      return Intent(content, SearchJobResultActivityV2::class.java)
        .apply {
          putExtra(EXTRA_FILTER_PARAMS, filterJobParams)
        }
    }
  }

  private var _filterDropDownPopup: DropDownPopup? = null

  private var _filterAreaView: FilterAreaView? = null
  private var _filterJobTypeView: FilterJobTypeView? = null
  private var _filterMoreOptionsView: FilterView? = null

  private val _searchJobLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == RESULT_OK && it.data != null) {
      viewModel.setSearchKeyword(
        it.data!!.getStringExtra(SearchJobsActivity.SEARCH_TEXT).getOrDefault()
      )
    }
  }

  override fun getViewModelClass(): Class<SearchJobResultViewModel> =
    SearchJobResultViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_search_job_result_v2

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    statusBarManager.titleBar(viewBinding.llTitleBar).statusBarDarkFont(true, 0.4f)
      .init()

    setupFilterPopup()

    subscribeViewModelEvent()
    subscribeCityChangeEvent()

    setupJobList()

    viewModel.start(intent.getParcelableExtra(EXTRA_FILTER_PARAMS))
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.tv_search_text -> {
          _searchJobLauncher.launch(
            SearchJobsActivity.newIntent(
              this, true,
              SearchSqlLiteHelper.SEARCH_JOB_RECORD_TABLE_NAME
            )
          )
        }

        R.id.tv_expect_industry -> {
          if (_filterJobTypeView?.firstTypeData.isNullOrEmpty()) {
            viewModel.loadJobFirstTypeList()
          }
          _filterDropDownPopup?.showItemAsDropDown(0)
        }

        R.id.tv_work_place -> {
          //未选择城市
          if (UserUtils.getUserSelectedCityId() == 0) {
            ActionDialog.Builder()
              .setTitle("提示")
              .setContent("选择期望城市后可切换区域")
              .setOnConfirmClickListener { dialog ->
                dialog.dismiss()
                startActivity(CityPickerActivity.newIntent(this))
              }
              .setCancelable(false).build().show(supportFragmentManager)
          } else {
            _filterAreaView?.setCurrentCity(UserUtils.getUserSelectedCityName())
            if (CheckUtils.isNullOrEmpty(_filterAreaView?.getAreaData())) {
              viewModel.changeFilterAreaCity(UserUtils.getUserSelectedCityId())
            }
            _filterDropDownPopup?.showItemAsDropDown(1)
          }
        }

        R.id.tv_sort -> {
          _filterDropDownPopup?.showItemAsDropDown(2)
        }

        R.id.tv_more_requirements -> {
          _filterDropDownPopup?.showItemAsDropDown(3)
        }

        else -> {}
      }
    }
  }

  private fun setupFilterPopup() {
    _filterDropDownPopup = DropDownPopup(this, viewBinding.clJobResultFilterBar).apply {
      setOnItemExpandStatusChangeListener { index, opened ->
        viewBinding.clJobResultFilterBar.getChildAt(index).isSelected = opened
      }
      addContentViews(
        getFilterJobTypeView(),
        getFilterAreaView(),
        getFilterSortView(),
        getFilterMoreOptionsView()
      )
    }
  }

  private fun getFilterAreaView(): FilterAreaView? {
    _filterAreaView = FilterAreaView(this).apply {
      setOnAreaItemClickListener(object : OnAreaItemClickListener {
        override fun onAreaItemClicked(position: Int) {
          _filterAreaView?.areaData?.get(position)?.let {
            viewModel.setJobListArea(it.id)
            _filterDropDownPopup?.close()
          }
        }

        override fun onChangeCityClicked() {
          startActivity(CityPickerActivity.newIntent(this@SearchJobResultActivityV2))
        }
      })

    }
    return _filterAreaView
  }

  private fun getFilterJobTypeView(): FilterJobTypeView? {
    _filterJobTypeView = FilterJobTypeView(this).apply {
      setOnItemClickListener(object : FilterJobClassPopup.OnItemClickListener {
        override fun onFirstClassItemClicked(position: Int) {
          _filterJobTypeView?.firstTypeData?.get(position)?.let {
            viewModel.setJobListFirstType(it.id)
          }
        }

        override fun onSecondClassClicked(position: Int) {
          _filterDropDownPopup?.close()
          _filterJobTypeView?.secondTypeData?.get(position)?.let {
            viewModel.setJobListSecondType(it.id)
          }
        }
      })
    }
    return _filterJobTypeView
  }

  private fun getFilterSortView(): DropDownMenuView {
    val positionSortArray = getResArray(R.array.position_sort)
    return DropDownMenuView(this).apply {
      setData(positionSortArray)
      setOnItemClickListener { view, position ->
        viewBinding.tvSort.text = positionSortArray[position]
        viewModel.setJobListSort(position)
        _filterDropDownPopup?.close()
      }
    }
  }

  private fun getFilterMoreOptionsView(): FilterView? {
    _filterMoreOptionsView = FilterView.Builder(this)
      .setOnFilterConfirmListener { positionHolderMap ->
        _filterDropDownPopup?.close()
        val workNaturePosition = positionHolderMap[FilterOptionData.WORK_NATURE].getOrDefault()
        _filterMoreOptionsView?.let { moreOptionsView ->
          viewModel.setJobListMoreOptions(
            (moreOptionsView.getOptionsGroupData(0) as List<FilterOptionData>)[positionHolderMap[FilterOptionData.SALARY].getOrDefault()].id,
            (moreOptionsView.getOptionsGroupData(1) as List<FilterOptionData>)[positionHolderMap[FilterOptionData.NATURE_OF_COMPANY].getOrDefault()].id,
            (moreOptionsView.getOptionsGroupData(2) as List<FilterOptionData>)[positionHolderMap[FilterOptionData.WORKING_EXP].getOrDefault()].id,
            (moreOptionsView.getOptionsGroupData(3) as List<FilterOptionData>)[positionHolderMap[FilterOptionData.EDU].getOrDefault()].id,
            positionHolderMap[FilterOptionData.PUBLISH_DATE].getOrDefault(),
            if (workNaturePosition == 0) 0 else workNaturePosition + 7,
            positionHolderMap[FilterOptionData.SPECIAL_REQ].getOrDefault()
          )
        }

      }.build()
    return _filterMoreOptionsView
  }

  private fun subscribeCityChangeEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe { message: RxBus.Message ->
          if (message.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
            _filterAreaView?.setCurrentCity(UserUtils.getUserSelectedCityName())
            viewModel.changeFilterAreaCity(UserUtils.getUserSelectedCityId())
            viewModel.setJobListCity(UserUtils.getUserSelectedCityId())
          }
        }
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })

    viewModel.areaList.observe(this) {
      _filterAreaView?.areaData = it
    }

    viewModel.jobFirstTypeList.observe(this) {
      _filterJobTypeView?.firstTypeData = it
    }

    viewModel.jobSecondTypeList.observe(this) {
      _filterJobTypeView?.secondTypeData = it
    }

    viewModel.filterMoreOptionsLoadedEvent.observe(this, EventObserver {
      it.add(FilterGroupTitle("发布时间"))
      it.add(
        FilterOptionsGroup(
          FilterOptionData.PUBLISH_DATE,
          resources.getStringArray(R.array.personal_jobs_publish_date).convertWheelOptions()
        )
      )
      it.add(FilterGroupTitle(getString(R.string.personal_working_nature)))
      it.add(
        FilterOptionsGroup(
          FilterOptionData.WORK_NATURE,
          resources.getStringArray(R.array.personal_search_jobs_work_nature).convertWheelOptions()
        )
      )
      it.add(FilterGroupTitle("特殊要求"))
      it.add(
        FilterOptionsGroup(
          FilterOptionData.SPECIAL_REQ,
          FilterUtils.parseFilterOptions(
            *resources.getStringArray(R.array.job_filter_special_req_options)
          )
        )
      )
      _filterMoreOptionsView?.setData(it)
    })
  }

  private fun setupJobList() {
    viewBinding.includeJobList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(this@SearchJobResultActivityV2)
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4_8))
          .drawFoot(true)
          .build()
      )
    }

    viewModel.jobListViewModel.setAdapter(
      object : SimpleDBListAdapter<JobData>(
        this@SearchJobResultActivityV2,
        R.layout.c_recycler_job_item
        // R.layout.personal_recycler_search_result_job_item
      ) {
        override fun convert(
          holder: SuperViewHolder,
          viewType: Int,
          item: JobData,
          position: Int
        ) {
          super.convert(holder, viewType, item, position)
          holder.findViewById<TextView>(R.id.tv_real_job_tag).visibility =
            if (item.isRealJob) View.VISIBLE else View.GONE
        }
      }.apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data[position]?.let {
              if (v.id == R.id.tv_application) {
                viewModel.sendResumePreCheck(it)
              } else {
                startActivity(
                  JobDetailsActivityV2.newIntent(
                    this@SearchJobResultActivityV2,
                    it.id
                  )
                )
              }
            }
          }
        }, R.id.tv_application)
      }
    )
  }

  override fun finish() {
    if (_filterDropDownPopup?.hasOpenMenu() == true) {
      _filterDropDownPopup?.close()
    } else {
      super.finish()
    }
  }
}