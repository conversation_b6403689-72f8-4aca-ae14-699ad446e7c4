package com.bxkj.personal.ui.activity.resumetop

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.ResumeTopPageBean
import com.bxkj.personal.data.ResumeTopServiceItemBean
import com.bxkj.personal.data.source.AccountRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/25
 **/
class ResumeTopViewModelV2 @Inject constructor(
  private val _accountRepo: AccountRepo
) : BaseViewModel() {

  val pageData = MutableLiveData<ResumeTopPageBean>()

  val resumeTopDaysRemaining = MutableLiveData<Int>()

  val serviceList = MutableLiveData<List<ResumeTopServiceItemBean>>()

  val selectedService = MutableLiveData<ResumeTopServiceItemBean>()

  val showNoResumeTipsCommand = MutableLiveData<VMEvent<Unit>>()

  val createOrderSuccessEvent = MutableLiveData<VMEvent<String>>()

  fun start() {
    viewModelScope.launch {
      showLoading()
      _accountRepo.getResumeTopPageData()
        .handleResult({
          it?.let {
            pageData.value = it
            resumeTopDaysRemaining.value = it.shengyutianshu
            serviceList.value = it.dataList
            selectedService.value = serviceList.value?.get(0)
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun selectService(service: ResumeTopServiceItemBean) {
    selectedService.value = service
  }

  fun isSelected(service: ResumeTopServiceItemBean): Boolean {
    return selectedService.value == service
  }

  fun createOrder() {
    selectedService.value?.let {
      viewModelScope.launch {
        showLoading()
        _accountRepo.createTopResumeOrderV2(it.id)
          .handleResult({orderId->
            orderId?.let {
              createOrderSuccessEvent.value = VMEvent(orderId)
            }
          }, {
            if (it.isNoDataError) {
              showNoResumeTipsCommand.value = VMEvent(Unit)
            } else {
              showToast(it.errMsg)
            }
          }, {
            hideLoading()
          })
      }
    }
  }
}