package com.bxkj.personal.ui.fragment.bbs

import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.business.ui.goodcourselist.GoodCourseListNavigation
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.DiscussData
import com.bxkj.personal.databinding.PersonalFragmentBbsBinding
import com.bxkj.personal.ui.activity.addquestion.AddQuestionActivity
import com.bxkj.personal.ui.activity.finearticle.FineArticleActivity
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussActivity
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussRankActivity
import com.bxkj.personal.ui.activity.postnews.PostNewsActivity
import com.bxkj.personal.ui.activity.study.StudyActivity
import com.bxkj.personal.ui.fragment.qa.QAActivity

/**
 *
 * @author: sanjin
 * @date: 2022/8/18
 */
class BBSFragment : BaseDBFragment<PersonalFragmentBbsBinding, BBSViewModel>() {

    private val _detailsLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            viewModel.handleDetailsResult(it)
        }

    override fun getViewModelClass(): Class<BBSViewModel> = BBSViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_fragment_bbs

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        viewBinding.ivPost.setOnClickListener {
            PostBBSNewsDialog({
                afterLogin {
                    startActivity(AddQuestionActivity.newIntent(requireContext()))
                }
            }, {
                afterLogin {
                    startActivity(PostNewsActivity.newIntent(requireContext()))
                }
            }).show(childFragmentManager)
        }

        setupContentList()

        viewModel.start()
    }

    private fun setupContentList() {
        val contentAdapter = MultiTypeAdapter(requireContext()).apply {
            register(BBSHeaderDataWrapper::class.java,
                object : DefaultViewBinder<BBSHeaderDataWrapper>(
                    R.layout.personal_recycler_bbs_header,
                    BR.data
                ) {
                    override fun onBindViewHolder(
                        holder: SuperViewHolder,
                        item: BBSHeaderDataWrapper,
                        position: Int
                    ) {
                        super.onBindViewHolder(holder, item, position)
                        val recyclerHotDiscuss =
                            holder.findViewById<RecyclerView>(R.id.recycler_hot_news)
                        if (recyclerHotDiscuss.adapter == null) {
                            recyclerHotDiscuss.apply {
                                isNestedScrollingEnabled = false
                                layoutManager = LinearLayoutManager(requireContext())
                                adapter = HotQARankListAdapter().apply {
                                    setOnItemClickListener(object :
                                        SuperItemClickListener {
                                        override fun onClick(v: View, position: Int) {
                                            getData()?.get(position)?.let {
                                                startActivity(
                                                    HotDiscussActivity.newIntent(
                                                        requireContext(),
                                                        it.wdid
                                                    )
                                                )
                                            }
                                        }
                                    })
                                }
                            }
                        }
                        recyclerHotDiscuss.adapter?.let {
                            (it as HotQARankListAdapter).submitList(item.hotQAList)
                        }
                    }
                }.apply {
                    setOnItemClickListener(
                        object :
                            DefaultViewBinder.OnItemClickListener<BBSHeaderDataWrapper> {
                            override fun onItemClicked(
                                v: View,
                                position: Int,
                                item: BBSHeaderDataWrapper
                            ) {
                                when (v.id) {
                                    R.id.tv_qa -> {
                                        startActivity(QAActivity.newIntent(requireContext()))
                                    }

                                    R.id.tv_study -> {
                                        startActivity(StudyActivity.newIntent(requireContext()))
                                    }

                                    R.id.tv_fine_article -> {
                                        startActivity(FineArticleActivity.newIntent(requireContext()))
                                    }

                                    R.id.tv_skill -> {
                                        GoodCourseListNavigation.create().start()
                                    }

                                    R.id.ll_hot_discuss -> {
                                        startActivity(
                                            HotDiscussRankActivity.newIntent(
                                                requireContext()
                                            )
                                        )
                                    }
                                }
                            }
                        },
                        R.id.tv_qa,
                        R.id.tv_study,
                        R.id.tv_fine_article,
                        R.id.tv_skill,
                        R.id.ll_hot_discuss
                    )
                })
            register(
                DiscussData::class.java,
                DefaultViewBinder<DiscussData>(
                    R.layout.personal_recycler_bbs_news_item,
                    BR.data
                ).apply {
                    setOnItemClickListener(object :
                        DefaultViewBinder.OnItemClickListener<DiscussData> {
                        override fun onItemClicked(v: View, position: Int, item: DiscussData) {
                            if (v.id == R.id.iv_avatar || v.id == R.id.tv_name || v.id == R.id.tv_city) {
                                UserHomeNavigation.navigate(item.userID).start()
                            } else {
                                _detailsLauncher.launch(
                                    HotDiscussActivity.newIntent(
                                        requireContext(),
                                        item.wdid
                                    )
                                )
                                item.addViewCount()
                            }
                        }
                    }, R.id.iv_avatar, R.id.tv_name, R.id.tv_city)
                }
            )
        }
        viewBinding.includeBbsList.recyclerContent.apply {
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f8f8f8_8))
                    .drawFoot(true)
                    .build()
            )
        }

        viewModel.contentListViewModel.setAdapter(contentAdapter)
    }
}