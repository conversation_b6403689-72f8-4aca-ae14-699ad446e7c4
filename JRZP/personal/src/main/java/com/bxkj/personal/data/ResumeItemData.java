package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 投递简历列表信息
 * @TODO: TODO
 * @date 2018/4/20
 */

public class ResumeItemData {

    /**
     * id : 18425
     * name : 软件工程师
     * detailsName : null
     * sortId : 0
     * jobType1 : 0
     * jobType2 : 0
     * isApply : 0
     */

    private int id;
    private String name;
    private String detailsName;
    private int sortId;
    private int jobType1;
    private int jobType2;
    private int isApply;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDetailsName() {
        return detailsName;
    }

    public void setDetailsName(String detailsName) {
        this.detailsName = detailsName;
    }

    public int getSortId() {
        return sortId;
    }

    public void setSortId(int sortId) {
        this.sortId = sortId;
    }

    public int getJobType1() {
        return jobType1;
    }

    public void setJobType1(int jobType1) {
        this.jobType1 = jobType1;
    }

    public int getJobType2() {
        return jobType2;
    }

    public void setJobType2(int jobType2) {
        this.jobType2 = jobType2;
    }

    public int getIsApply() {
        return isApply;
    }

    public void setIsApply(int isApply) {
        this.isApply = isApply;
    }
}
