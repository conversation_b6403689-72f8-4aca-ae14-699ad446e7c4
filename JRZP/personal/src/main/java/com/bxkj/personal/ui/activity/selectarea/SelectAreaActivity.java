package com.bxkj.personal.ui.activity.selectarea;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.contract.GetAreaListContract;
import com.bxkj.common.mvp.persenter.GetAreaListPresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity;
import com.bxkj.personal.R;
import com.hjq.toast.Toaster;
import com.zaaach.citypicker.CityPicker;
import com.zaaach.citypicker.adapter.OnPickListener;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.LocatedCity;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.selectarea
 * @Description: 选择区域
 * @TODO: TODO
 * @date 2018/4/15
 */

public class SelectAreaActivity extends BaseDaggerActivity implements GetAreaListContract.View {

  public static final String RESULT_DATA = "resultData";
  private static final String PARENT_CITY_ID = "parentCityId";
  private static final String NEED_CHANGE_GLOBAL_CITY = "needChangeGlobalCity";
  private static final String MAX_SELECTED_COUNT = "max_selected_count";
  private static final String NEED_SHOW_PARENT_CITY = "needShowParentCity";

  private static final String START_TYPE = "startType";
  private static final int CHANGE_CITY_AND_SELECT_AREA = 1;

  @Inject
  GetAreaListPresenter mGetAreaListPresenter;

  private RecyclerView recyclerArea;
  private TextView tvSelectAreaTips;
  private LinearLayout llOptionsBarOne;
  private LinearLayout llOptionsBarTwo;
  private TextView tvCurrentCity;

  private AreaListAdapter mAreaListAdapter;
  private List<AreaOptionsData> mSelectedAreaList;
  private int mPageType;
  private CityPicker mCityPicker;
  private int mCityId;
  private String mCityName;
  private int mMaxSelectCount;
  private boolean mNeedShowParentCity;

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_select_area;
  }

  public static Intent newIntent(Activity activity, boolean needChangeGlobalCity) {
    Intent intent = new Intent(activity, SelectAreaActivity.class);
    intent.putExtra(START_TYPE, CHANGE_CITY_AND_SELECT_AREA);
    intent.putExtra(NEED_CHANGE_GLOBAL_CITY, needChangeGlobalCity);
    return intent;
  }

  public static Intent newIntent(Activity activity, int parentCityId) {
    Intent intent = new Intent(activity, SelectAreaActivity.class);
    intent.putExtra(PARENT_CITY_ID, parentCityId);
    return intent;
  }

  public static Intent newIntent(Activity activity, int parentCityId, boolean needShowParentCity,
    int maxSelectedCount) {
    Intent intent = new Intent(activity, SelectAreaActivity.class);
    intent.putExtra(PARENT_CITY_ID, parentCityId);
    intent.putExtra(NEED_SHOW_PARENT_CITY, needShowParentCity);
    intent.putExtra(MAX_SELECTED_COUNT, maxSelectedCount);
    return intent;
  }

  @Override
  protected void initPresenter() {
    mGetAreaListPresenter.attachView(this);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_select_area));
  }

  @Override
  protected void initIntent(Intent intent) {
    mPageType = intent.getIntExtra(START_TYPE, CommonApiConstants.NO_DATA);
    mMaxSelectCount = intent.getIntExtra(MAX_SELECTED_COUNT, 3);
    mNeedShowParentCity = intent.getBooleanExtra(NEED_SHOW_PARENT_CITY, false);
  }

  private ActionDialog mNoCityTipsDialog;

  private final ActivityResultLauncher<Intent> launcher = registerForActivityResult(
    new StartActivityForResult(), result -> {
      if (result.getResultCode() == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
        mNoCityTipsDialog.dismiss();
        initPage();
      }
    });

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    //未选择城市
    if (UserUtils.getUserSelectedCityId() == 0) {
      mNoCityTipsDialog = new ActionDialog.Builder()
        .setTitle("提示")
        .setContent("选择城市后可切换区域")
        .setOnConfirmClickListener((dialog) -> {
          launcher.launch(CityPickerActivity.newIntent(this));
        })
        .setOnCancelClickListener(this::finish)
        .setCancelable(false).build();
      mNoCityTipsDialog.show(getSupportFragmentManager());
    } else {
      if (mPageType == CHANGE_CITY_AND_SELECT_AREA) {
        llOptionsBarOne.setVisibility(View.GONE);
        llOptionsBarTwo.setVisibility(View.VISIBLE);
        tvCurrentCity.setText(UserUtils.getUserSelectedCityName());
        initCityPicker();
        mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
          UserUtils.getUserSelectedCityId());
      } else {
        final int mParentCityId = getIntent().getIntExtra(PARENT_CITY_ID, CommonApiConstants.NO_ID);
        mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE, mParentCityId);
      }
      initView();
    }
  }

  private void initCityPicker() {
    mCityPicker = new CityPicker()
      .setLocatedCity(
        new LocatedCity(UserUtils.getUserLocateCityName(), CommonApiConstants.NO_TEXT,
          String.valueOf(UserUtils.getUserLocateCityId())))
      .setFragmentManager(getSupportFragmentManager())
      .enableAnimation(true)
      .setAnimationStyle(R.style.RightPopupAnim)
      .setOnPickListener(new OnPickListener() {
        @Override
        public void onPick(int position, City data) {
          if (data != null) {
            tvCurrentCity.setText(data.getName());
            if (getIntent().getBooleanExtra(NEED_CHANGE_GLOBAL_CITY, false)) {
              UserUtils.saveUserSelectedCityInfo(Integer.parseInt(data.getCode()),
                data.getName());
            } else {
              mCityId = Integer.parseInt(data.getCode());
              mCityName = data.getName();
            }
            mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
              UserUtils.getUserSelectedCityId());
          }
        }

        @Override
        public void onLocate() {

        }
      });
  }

  private void initView() {
    tvSelectAreaTips.setText(getString(R.string.personal_select_area_tips, mMaxSelectCount, 0));
    mSelectedAreaList = new ArrayList<>();
    mAreaListAdapter = new AreaListAdapter(this, null, R.layout.personal_recycler_select_area_item,
      mMaxSelectCount);
    mAreaListAdapter.setOnItemClickListener((view, position) -> {
      AreaOptionsData areaItemData = mAreaListAdapter.getData().get(position);
      if (mPageType == CHANGE_CITY_AND_SELECT_AREA) {
        backAndResultData(areaItemData);
        return;
      }
      if (mSelectedAreaList.contains(areaItemData)) {
        mSelectedAreaList.remove(areaItemData);
      } else {
        mSelectedAreaList.add(areaItemData);
      }
      tvSelectAreaTips.setText(
        getString(R.string.personal_select_area_tips, mMaxSelectCount, mSelectedAreaList.size()));
    });
    recyclerArea.setLayoutManager(new GridLayoutManager(this, 3));
    recyclerArea.setAdapter(mAreaListAdapter);
  }

  private void backAndResultData(AreaOptionsData areaItemData) {
    areaItemData.setPid(mCityId == 0 ? UserUtils.getUserSelectedCityId() : mCityId);
    areaItemData.setPName(
      CheckUtils.isNullOrEmpty(mCityName) ? UserUtils.getUserSelectedCityName() : mCityName);
    Intent intent = new Intent();
    Bundle bundle = new Bundle();
    bundle.putParcelable(RESULT_DATA, areaItemData);
    intent.putExtras(bundle);
    setResult(RESULT_OK, intent);
    finish();
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_select_area_confirm) {
      if (CheckUtils.isNullOrEmpty(mSelectedAreaList)) {
        Toaster.show(getString(R.string.common_select_at_least_one));
        return;
      }
      Bundle bundle = new Bundle();
      bundle.putParcelableArrayList(RESULT_DATA,
        (ArrayList<? extends Parcelable>) mSelectedAreaList);
      setResult(RESULT_OK, new Intent().putExtras(bundle));
      finish();
    } else {
      if (mCityPicker != null) {
        mCityPicker.show();
      }
    }
  }

  @Override
  public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {
    if (mPageType == CHANGE_CITY_AND_SELECT_AREA || mNeedShowParentCity) {
      //在列表的最前面插入全**，id为0
      optionsDataList.add(0,
        new AreaOptionsData(CommonApiConstants.NO_ID, "全" + UserUtils.getUserSelectedCityName()));
    }
    mAreaListAdapter.reset(optionsDataList);
  }

  @Override
  public void onError(String errMsg) {
    Toaster.show(errMsg);
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mGetAreaListPresenter.detachView();
  }

  private void bindView(View bindSource) {
    recyclerArea = bindSource.findViewById(R.id.recycler_area);
    tvSelectAreaTips = bindSource.findViewById(R.id.tv_select_area_tips);
    llOptionsBarOne = bindSource.findViewById(R.id.ll_options_bar_one);
    llOptionsBarTwo = bindSource.findViewById(R.id.ll_options_bar_two);
    tvCurrentCity = bindSource.findViewById(R.id.tv_current_city);
    bindSource.findViewById(R.id.tv_select_area_confirm).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_chang_city).setOnClickListener(v -> onViewClicked(v));
  }
}
