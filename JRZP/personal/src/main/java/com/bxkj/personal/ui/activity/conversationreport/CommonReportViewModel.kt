package com.bxkj.personal.ui.activity.conversationreport

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.personal.R
import com.bxkj.personal.data.source.MomentRepo
import com.bxkj.personal.ui.activity.postnews.MomentFileItem
import com.bxkj.personal.ui.activity.postnews.PhotoNewListAdapter
import com.luck.picture.lib.entity.LocalMedia
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/8
 * @version: V1.0
 */
class CommonReportViewModel @Inject constructor(
  private val momentRepo: MomentRepo,
  private val commonRepo: CommonRepository
) : BaseViewModel() {

  companion object {
    private const val MAX_SELECT_COUNT = 6
  }

  private var _photoList = ArrayList<MomentFileItem>()

  private var _photoAdapter: PhotoNewListAdapter? = null

  private var mReportType: Int = CommonApiConstants.NO_ID
  private var mReportInfoID: Int = CommonApiConstants.NO_ID

  val reportReasonText = MutableLiveData<String>().apply { value = "请选择具体原因" }

  val reportContent = MutableLiveData<String>()

  val reportSuccessEvent = LiveEvent<Void>()

  fun start(reportType: Int, reportInfoID: Int) {
    mReportType = reportType
    mReportInfoID = reportInfoID
  }

  fun getPhotoAdapter(): PhotoNewListAdapter? {
    return _photoAdapter
  }

  fun setPhotoAdapter(adapter: PhotoNewListAdapter) {
    adapter.add(MomentFileItem.getAddItem())
    _photoAdapter = adapter
  }

  fun deletePhoto(index: Int) {
    _photoList.removeAt(index)
    _photoAdapter?.removeAt(index)
    if (_photoList.size == (MAX_SELECT_COUNT - 1)) {
      _photoAdapter?.add(MomentFileItem.getAddItem())
    }
  }

  fun uploadFileList(list: List<LocalMedia>) {
    showLoading()
    if (!CheckUtils.isNullOrEmpty(list)) {
      val tempList = ArrayList<MomentFileItem>()
      val currentUploadIndex = 0
      uploadFile(list, tempList, currentUploadIndex)
    }
  }

  /**
   * 上传文件
   */
  fun uploadFile(
    list: List<LocalMedia>,
    tempList: ArrayList<MomentFileItem>,
    currentUploadIndex: Int
  ) {
    //当前文件
    val file = list[currentUploadIndex]
    //是否是视频
    val fileIsVideo = ZPFileUtils.isVideoFile(file.mimeType)
    //最终使用的路径
    val finalPath = ZPFileUtils.getPictureSelectorPath(file)
    momentRepo.uploadMomentFile(finalPath, UploadFileRequestParams.fromFileType(
      getSelfUserID(),
      if (fileIsVideo) UploadFileRequestParams.TYPE_VIDEO else UploadFileRequestParams.TYPE_IMG
    ), object : ResultDataCallBack<String> {
      override fun onSuccess(data: String?) {
        tempList.add(MomentFileItem.fromPathAndUrl(finalPath, data, fileIsVideo))
        if (currentUploadIndex < list.size - 1) {   //未上传完，继续上传下一个
          val nextIndex = currentUploadIndex + 1
          uploadFile(list, tempList, nextIndex)
        } else {
          uploadSuccess(tempList)
        }
      }

      override fun onError(respondThrowable: RespondThrowable) {
        showToast(R.string.post_news_upload_file_error_tips)
        hideLoading()
      }
    })
  }

  private fun uploadSuccess(tempList: List<MomentFileItem>) {
    hideLoading()
    _photoList.addAll(tempList)
    _photoAdapter?.let {
      it.addAll(it.data.size - 1, tempList)
      if (getAvailableImgNum() <= 0) {
        it.removeAt(it.data.lastIndex)
      }
    }
  }

  fun getAvailableImgNum(): Int {
    return MAX_SELECT_COUNT - _photoList.size
  }

  fun setupReportReason(resultReason: String) {
    reportReasonText.value = resultReason
  }

  fun submit() {
    reportContent.value?.let { content ->
      if (content.length < 10) {
        showToast("请输入举报的详细说明，且不少于10个字")
        return
      }
      val picStringBuilder = StringBuilder()
      _photoList.forEach {
        picStringBuilder.append(it.fileUrl).append("|")
      }
      val finalPicString =
        if (CheckUtils.isNullOrEmpty(picStringBuilder)) "" else picStringBuilder.substring(
          0,
          picStringBuilder.length - 1
        )
      viewModelScope.launch {
        showLoading()
        commonRepo.report(
          getSelfUserID(),
          mReportInfoID,
          reportReasonText.value!!,
          content,
          finalPicString,
          mReportType
        ).handleResult({
          reportSuccessEvent.call()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }
}