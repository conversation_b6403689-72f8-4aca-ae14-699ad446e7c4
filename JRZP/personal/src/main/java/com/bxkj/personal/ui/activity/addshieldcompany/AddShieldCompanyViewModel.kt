package com.bxkj.personal.ui.activity.addshieldcompany

import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.data.CompanyDetailsData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.SearchRepo
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.lang.StringBuilder
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/6/10
 * @version: V1.0
 */
class AddShieldCompanyViewModel @Inject constructor(
  application: Application,
  private val mSearchRepo: SearchRepo,
  private val mAccountRepo: AccountRepo
) :
  BaseViewModel() {
  val searchKeyword = MutableLiveData<String>().apply { value = "" }

  val searchResultListViewModel = RefreshListViewModel()

  val allSelect = MutableLiveData<Boolean>().apply { value = false }

  val selectedItems = MutableLiveData<List<CompanyDetailsData>>()

  val searchResultCount = LiveEvent<Int>()
    .apply { value = 0 }

  val addShieldCompanySuccess = LiveEvent<Void>()

  val switchAllSelectedCommand = LiveEvent<Void>()

  val searchLoading = MutableLiveData<Boolean>().apply { value = false }

  private val _selectedItems = ArrayList<CompanyDetailsData>()
  private var mCurrentSearchJob: Job? = null

  init {
    setupSearchResultListViewModel()
  }

  private fun setupSearchResultListViewModel() {
    searchResultListViewModel.refreshLayoutViewModel.enableRefresh(false)
    searchResultListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    searchResultListViewModel.setOnLoadDataListener { currentPage ->
      mCurrentSearchJob = viewModelScope.launch {
        searchKeyword.value?.let {
          searchLoading.value = true
          mSearchRepo.searchCompany(getSelfUserID(), it, currentPage, 20)
            .handleResult({ result ->
              if (result != null) {
                searchResultCount.value = result.size
              } else {
                searchResultCount.value = 0
              }
              searchResultListViewModel.reset(result)
            }, { err ->
              if (err.isNoDataError) {
                searchResultCount.value = 0
                searchResultListViewModel.noMoreData()
              } else {
                searchResultListViewModel.loadError()
              }
            }, {
              hideSearchLoading()
            })
        }
      }
    }
  }

  private fun hideSearchLoading() {
    searchLoading.value = false
  }

  /**
   * 开始搜索
   */
  fun startSearch() {
    stopSearch()
    searchResultListViewModel.refresh(true)
  }

  /**
   * 停止搜索
   */
  fun stopSearch() {
    if (allSelect.value == true) {
      switchSelectAllState()
    }
    searchResultListViewModel.clear()
    _selectedItems.clear()
    notifySelectedItemsChange()
    mCurrentSearchJob?.cancel()
  }

  /**
   * 切换全选状态
   */
  fun switchSelectAllState() {
    if (searchResultListViewModel.childCount > 0) {
      switchAllSelectedCommand.call()
    }
  }

  /**
   * 设置全选状态
   */
  fun setSelectAll(selectAll: Boolean) {
    allSelect.value = selectAll
  }

  /**
   * 获取选中公司
   */
  fun getSelectedCompany(): ArrayList<CompanyDetailsData> {
    return _selectedItems;
  }

  /**
   * 刷新选中公司状态
   */
  fun notifySelectedItemsChange() {
    selectedItems.value = ArrayList(_selectedItems)
  }

  fun addShieldCompany() {
    if (CheckUtils.isNullOrEmpty(_selectedItems)) {
      showToast("最少选择一家公司")
    } else {
      val idsBuilder = StringBuilder()
      val namesBuilder = StringBuilder()
      _selectedItems.forEach {
        idsBuilder.append(it.companyID).append(",")
        namesBuilder.append(it.companyName.replace(",", "")).append(",")
      }
      viewModelScope.launch {
        showLoading()
        mAccountRepo.addShieldCompany(
          getSelfUserID(),
          idsBuilder.substring(0, idsBuilder.lastIndexOf(",")),
          namesBuilder.substring(0, namesBuilder.lastIndexOf(","))
        ).handleResult({
          addShieldCompanySuccess.call()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }
}