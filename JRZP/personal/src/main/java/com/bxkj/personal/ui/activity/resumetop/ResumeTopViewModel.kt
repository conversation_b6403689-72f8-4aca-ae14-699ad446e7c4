package com.bxkj.personal.ui.activity.resumetop

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.alipay.sdk.app.PayTask
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.TextUtils
import com.bxkj.personal.R
import com.bxkj.personal.data.AliPayResultData
import com.bxkj.personal.data.CreateOrderResultData
import com.bxkj.personal.data.OrderItemData
import com.bxkj.personal.data.ResumeTopDiscountItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: jrzp
 * @Package com.bxkj.personal.ui.activity.resumetop
 * @Description: 简历置顶
 * <AUTHOR>
 * @date 2020/2/10
 * @version V1.0
 */
class ResumeTopViewModel @Inject constructor(private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val discountList = MutableLiveData<List<ResumeTopDiscountItemData>>()
    val selectedDiscountItem = MutableLiveData<ResumeTopDiscountItemData>()
    val createOrderSuccessEvent =
      LiveEvent<CreateOrderResultData>()
    val paymentSuccessEvent = LiveEvent<Void>()
    private var mTopResumeId: Int = CommonApiConstants.NO_ID

    fun start(intent: Intent) {
        mTopResumeId = intent.getIntExtra(ResumeTopActivity.EXTRA_RESUME_ID, CommonApiConstants.NO_ID)
        getResumeTopDiscount()
    }

    private fun getResumeTopDiscount() {
        mAccountRepo.getResumeTopDiscount(getSelfUserID(), 1
                , object : ResultDataCallBack<List<ResumeTopDiscountItemData>> {
            override fun onSuccess(data: List<ResumeTopDiscountItemData>) {
                selectedDiscountItem.value = data[0]
                discountList.value = data
            }

            override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun setSelectedDiscount(discount: ResumeTopDiscountItemData) {
        selectedDiscountItem.value = discount
    }

    /**
     * 创建订单
     */
    fun createOrder() {
        showLoading()
        selectedDiscountItem.value?.let {
            mAccountRepo.createResumeTopOrder(getSelfUserID(), mTopResumeId, it.count
                    , object : ResultDataCallBack<String> {
                override fun onSuccess(data: String?) {
                    getAlipayOrderInfo(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    showToast(respondThrowable.errMsg)
                }
            })
        }
    }

    /**
     * 获取支付宝订单信息
     */
    private fun getAlipayOrderInfo(data: String?) {
        data?.let {
            mAccountRepo.getAlipayOrderInfo(getSelfUserID(), TextUtils.hexToString(data)
                    , object : ResultDataCallBack<CreateOrderResultData> {
                override fun onSuccess(data: CreateOrderResultData?) {
                    createOrderSuccessEvent.value = data
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    showToast(respondThrowable.errMsg)
                }
            })
        }
    }

    /**
     * 支付宝支付
     */
    fun payOfAlipay(payTask: PayTask, it: CreateOrderResultData) {
        mAccountRepo.payOfAlipay(payTask, it
                , object : ResultDataCallBack<AliPayResultData> {
            override fun onSuccess(data: AliPayResultData?) {
                data?.let {
                    val resultStatus = it.resultStatus
                    if (resultStatus == "9000") {
                        resumeTopSuccess()
                    } else if (resultStatus == "6001") {
                        hideLoading()
                        showToast(R.string.resume_top_payment_cancel)
                    } else if (resultStatus == "4000") {
                        hideLoading()
                        showToast(R.string.resume_top_payment_failed)
                    } else if (resultStatus == "8000" || resultStatus == "6004") {
                        checkPaymentStatus(data.orderId)
                    } else {
                        checkPaymentStatus(data.orderId)
                    }
                }
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    /**
     * 支付结果未知，验证支付结果
     */
    private fun checkPaymentStatus(orderId: String) {
        mAccountRepo.getPaymentResult(getSelfUserID(), orderId
                , object : ResultDataCallBack<OrderItemData> {
            override fun onSuccess(data: OrderItemData) {
                if (data.ispay == OrderItemData.PAID) {
                    resumeTopSuccess()
                } else {
                    hideLoading()
                    showToast(R.string.resume_top_payment_failed)
                }
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    /**
     * 简历置顶成功
     */
    private fun resumeTopSuccess() {
        hideLoading()
        showToast(R.string.resume_top_success)
        paymentSuccessEvent.call()
    }

}