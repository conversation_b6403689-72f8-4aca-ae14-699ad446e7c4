package com.bxkj.personal.ui.fragment.maintab

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.enterprise.ui.fragment.talentpool.TalentPoolFragmentNavigation
import com.bxkj.personal.R
import com.bxkj.personal.databinding.FragmentMainTabContainerBinding
import com.bxkj.personal.ui.fragment.jobnewscontainer.GeekMainTabTwoFragment

/**
 *
 * @author: sanjin
 * @date: 2022/7/5
 */
class MainTabTwoContainerFragment :
    BaseDBFragment<FragmentMainTabContainerBinding, BaseViewModel>() {

    companion object {

        fun newInstance(): Fragment {
            return MainTabTwoContainerFragment()
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.fragment_main_tab_container

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        setupContentFragment()

        subscribeIdentityChange()
    }

    private fun subscribeIdentityChange() {
        addDisposable(RxBus.get().toObservable(RxBus.Message::class.java)
            .subscribe {
                if (it.code == RxMsgCode.ACTION_USER_ROLE_CHANGE) {
                    setupContentFragment()
                }
            })
    }

    private fun setupContentFragment() {
        if (UserUtils.isPersonalRole()) {
            switchContentFragment(GeekMainTabTwoFragment())
        } else {
            switchContentFragment(TalentPoolFragmentNavigation.create())
        }
    }

    private fun switchContentFragment(fragment: Fragment) {
        childFragmentManager.beginTransaction().replace(R.id.fl_container, fragment)
            .commitAllowingStateLoss()
    }
}