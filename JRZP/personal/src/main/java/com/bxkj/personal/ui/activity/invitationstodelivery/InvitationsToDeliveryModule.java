package com.bxkj.personal.ui.activity.invitationstodelivery;

import com.bxkj.common.di.scope.PerFragment;
import com.bxkj.personal.ui.fragment.invitationstodelivery.InvitationsToDeliveryFragment;

import dagger.Module;
import dagger.android.ContributesAndroidInjector;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.invitationstodelivery
 * @Description:
 * @TODO: TODO
 * @date 2018/9/26
 */
@Module
public abstract class InvitationsToDeliveryModule {
    @PerFragment
    @ContributesAndroidInjector
    abstract InvitationsToDeliveryFragment invitationsToDeliveryFragment();
}
