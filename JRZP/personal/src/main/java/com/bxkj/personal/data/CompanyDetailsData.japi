package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.api.R;
import com.bxkj.personal.api.BR;

/**
 * @version V1.0
 * @date 2018/4/26
 */

public class CompanyDetailsData extends BaseObservable implements Parcelable {

  private int id;
  private int companyID;
  private String name;
  private String companyName;
  private String companyName2;
  private String address;
  private String phone;
  private String lxr;
  private String qq;
  private String info;
  private String proName;
  private String sizeName;
  private String tradeName;
  private String coordinate;
  private String traffic;
  private String companyLogo;
  private int status;
  private int count;
  private String jobName;
  private boolean isFollowCompany;
  private int userID;
  private boolean inBlackList;
  private String logo;
  private int uid;
  private String rzPic;
  private int state;
  private int level;

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public String getRzPic() {
    return rzPic;
  }

  public void setRzPic(String rzPic) {
    this.rzPic = rzPic;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public String getLogo() {
    return logo;
  }

  public void setLogo(String logo) {
    this.logo = logo;
  }

  public String getDesc() {
    final StringBuilder descStr = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(tradeName)) {
      descStr.append(tradeName).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(proName)) {
      descStr.append(proName).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(sizeName)) {
      descStr.append(sizeName).append(" | ");
    }
    return CheckUtils.isNullOrEmpty(descStr) ? "" : descStr.substring(0, descStr.lastIndexOf("|"));
  }

  public int getUserID() {
    return userID;
  }

  public void setUserID(int userID) {
    this.userID = userID;
  }

  @Bindable
  public boolean isFollowCompany() {
    return isFollowCompany;
  }

  public void setFollowCompany(boolean followCompany) {
    isFollowCompany = followCompany;
    notifyPropertyChanged(BR.followCompany);
  }

  public int getCompanyID() {
    return companyID;
  }

  public void setCompanyID(int companyID) {
    this.companyID = companyID;
  }

  public CompanyDetailsData() {
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getCompanyName() {
    return companyName;
  }

  public void setCompanyName(String companyName) {
    this.companyName = companyName;
  }

  public String getCompanyName2() {
    return companyName2;
  }

  public String getHandleCompanyName2() {
    return CheckUtils.isNullOrEmpty(companyName2) ? "无" : companyName2;
  }

  public void setCompanyName2(String companyName2) {
    this.companyName2 = companyName2;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getLxr() {
    return lxr;
  }

  public void setLxr(String lxr) {
    this.lxr = lxr;
  }

  public String getQq() {
    return qq;
  }

  public void setQq(String qq) {
    this.qq = qq;
  }

  public String getInfo() {
    return info;
  }

  public void setInfo(String info) {
    this.info = info;
  }

  public String getProName() {
    return proName;
  }

  public void setProName(String proName) {
    this.proName = proName;
  }

  public String getSizeName() {
    return sizeName;
  }

  public void setSizeName(String sizeName) {
    this.sizeName = sizeName;
  }

  public String getTradeName() {
    return tradeName;
  }

  public void setTradeName(String tradeName) {
    this.tradeName = tradeName;
  }

  public String getCoordinate() {
    return coordinate;
  }

  public void setCoordinate(String coordinate) {
    this.coordinate = coordinate;
  }

  public String getTraffic() {
    return traffic;
  }

  public void setTraffic(String traffic) {
    this.traffic = traffic;
  }

  public int getStatus() {
    return status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public String getCompanyLogo() {
    return companyLogo;
  }

  public void setCompanyLogo(String companyLogo) {
    this.companyLogo = companyLogo;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public boolean isInBlackList() {
    return inBlackList;
  }

  public void setInBlackList(boolean inBlackList) {
    this.inBlackList = inBlackList;
  }

  public String getAboutInfo() {
    final StringBuilder aboutInfoBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(tradeName)) {
      aboutInfoBuilder.append(tradeName);
    }
    if (!CheckUtils.isNullOrEmpty(sizeName)) {
      aboutInfoBuilder.append(" | ").append(sizeName);
    }
    if (!CheckUtils.isNullOrEmpty(proName)) {
      aboutInfoBuilder.append(" | ").append(proName);
    }
    return aboutInfoBuilder.toString();
  }

  public int getMemberLevelIcon() {
    switch (level) {
      case 1101: {
        return R.drawable.ic_member_bule;
      }
      case 2101: {
        return R.drawable.ic_member_gold;
      }
      case 3101: {
        return R.drawable.ic_member_diamond;
      }
      default: {
        return -1;
      }
    }
  }

  public boolean isVip() {
    return level > 101;
  }

  public void addFollow() {
    setFollowCompany(true);
  }

  public void removeFollow() {
    setFollowCompany(false);
  }

  @Override public int describeContents() {
    return 0;
  }

  @Override public void writeToParcel(Parcel dest, int flags) {

  }
}