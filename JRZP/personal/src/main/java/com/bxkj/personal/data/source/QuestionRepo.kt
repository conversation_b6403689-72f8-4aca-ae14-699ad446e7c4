package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.network.*
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.api.PersonalApi
import com.bxkj.personal.data.*
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * @date 2020/2/25
 */
class QuestionRepo @Inject constructor(
    private val mPersonalApi: PersonalApi,
    private val mCoroutinesApi: CoroutinesApi,
) : BaseRepo() {

    /**
     * 获取热议列表
     */
    suspend fun getDiscussList(pageIndex: Int, pageSize: Int, searchKeyword: String? = ""):ReqResponse<List<DiscussData>>{
        return httpRequest {
            mCoroutinesApi.getBBSQAList(
                ZPRequestBody().apply {
                    put("title",searchKeyword)
                    put("pageIndex",pageIndex)
                    put("pageSize",pageSize)
                }
            )
        }
    }

    /**
     * 获取热门问答
     */
    suspend fun getHotDiscuss(top: Int): ReqResponse<List<DiscussData>> {
        return httpRequest {
            mCoroutinesApi.getHotQAList(
                ZPRequestBody().apply {
                    put("top", top)
                }
            )
        }
    }

    fun getAnswerDetails(
        userId: Int,
        answerId: Int,
        callback: ResultDataCallBack<AnswerDetailsData>,
    ) {
        mPersonalApi.getAnswerDetails(userId, answerId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(baseResponse.data as AnswerDetailsData?)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getQuestionDetails(
        userId: Int,
        questionId: Int,
        callback: ResultDataCallBack<QuestionData>
    ) {
        mPersonalApi.getQuestionDetails(userId, questionId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(baseResponse.data as QuestionData?)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getAnswerList(
        userId: Int,
        questionId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<AnswerItemData>>,
    ) {
        mPersonalApi.getAnswerList(userId, questionId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getInviteUserList(
        questionId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<InviteUserItemData>>,
    ) {
        mPersonalApi.getInviteUserList(questionId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun inviteUserToAnswer(userId: Int, questionId: Int, inviteId: Int, callback: ResultCallBack) {
        mPersonalApi.inviteUserToAnswer(userId, questionId, inviteId.toString())
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getInviteQuestionList(
        userId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<QuestionItemData>>,
    ) {
        mPersonalApi.getInviteQuestionList(userId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getRecommendQuestionList(
        linkId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<QuestionItemData>>,
        title: String? = "",
    ) {
        mPersonalApi.getRecommendQuestionList(linkId.toString(), title, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getQuestionList(
        linkId: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<QuestionItemData>>,
    ) {
        mPersonalApi.getQuestionList(linkId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun searchQuestionByKeyword(
        keyword: String,
        callback: ResultDataCallBack<List<QuestionItemData>>,
    ) {
        mPersonalApi.searchQuestionByKeyword(keyword)
            .debounce(1, TimeUnit.SECONDS)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getQARankList(
        userId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<QARankItemData>>,
    ) {
        mPersonalApi.getQARankList(userId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getUnansweredQuestionList(callback: ResultDataCallBack<List<QuestionItemData>>) {
        mPersonalApi.unansweredQuestionList
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun searchQANews(
        keyword: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<QuestionItemData>>,
    ) {
        mPersonalApi.searchQaNews(keyword, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    /**
     * 添加快捷回答
     */
    suspend fun addQuickAnswer(userId: Int, content: String): ReqResponse<Nothing> {
        return httpRequest {
            mCoroutinesApi.addQuickAnswer(
                ZPRequestBody()
                    .apply {
                        put("userID", userId)
                        put("content", content)
                    }
            )
        }
    }
}