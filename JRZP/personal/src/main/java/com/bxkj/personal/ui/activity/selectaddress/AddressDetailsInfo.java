package com.bxkj.personal.ui.activity.selectaddress;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.selectaddress
 * @Description: 详细地址信息
 * @TODO: TODO
 * @date 2018/5/7
 */

public class AddressDetailsInfo implements Parcelable {
    private int provinceId;
    private String province;
    private int cityId;
    private String city;
    private int areaId;
    private String area;
    private int streetId;
    private String street;
    private String detailsAddress;

    public AddressDetailsInfo() {
    }

    protected AddressDetailsInfo(Parcel in) {
        provinceId = in.readInt();
        province = in.readString();
        cityId = in.readInt();
        city = in.readString();
        areaId = in.readInt();
        area = in.readString();
        streetId = in.readInt();
        street = in.readString();
        detailsAddress = in.readString();
    }

    public static final Creator<AddressDetailsInfo> CREATOR = new Creator<AddressDetailsInfo>() {
        @Override
        public AddressDetailsInfo createFromParcel(Parcel in) {
            return new AddressDetailsInfo(in);
        }

        @Override
        public AddressDetailsInfo[] newArray(int size) {
            return new AddressDetailsInfo[size];
        }
    };

    public String getDetailsAddress() {
        return detailsAddress;
    }

    public void setDetailsAddress(String detailsAddress) {
        this.detailsAddress = detailsAddress;
    }

    public int getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public int getAreaId() {
        return areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public int getStreetId() {
        return streetId;
    }

    public void setStreetId(int streetId) {
        this.streetId = streetId;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(provinceId);
        parcel.writeString(province);
        parcel.writeInt(cityId);
        parcel.writeString(city);
        parcel.writeInt(areaId);
        parcel.writeString(area);
        parcel.writeInt(streetId);
        parcel.writeString(street);
        parcel.writeString(detailsAddress);
    }
}
