package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.UserUtils;
import com.bxkj.personal.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/12/5
 */
public class SchoolMateItemData extends BaseObservable {


    /**
     * glid : 394156
     * juli : 0.36
     * name2 : 刘先生
     * photo : http://img.jrzp.com/images_server/comm/nan.png
     * graduationTime : 师兄
     * sjNewsCount : 3
     * isHasGuanzhu : 0
     */

    private int uid;
    private int glid;
    private double juli;
    private String name2;
    private String photo;
    private String graduationTime;
    private int sjNewsCount;
    private int isHasGuanzhu;

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getGlid() {
        return glid;
    }

    public void setGlid(int glid) {
        this.glid = glid;
    }

    public double getJuli() {
        return juli;
    }

    public String getJuliText() {
        if (juli < 1) {
            return juli * 1000 + "m";
        } else {
            return juli + "km";
        }
    }

    public void setJuli(double juli) {
        this.juli = juli;
    }

    public String getName2() {
        return name2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getGraduationTime() {
        return graduationTime;
    }

    public void setGraduationTime(String graduationTime) {
        this.graduationTime = graduationTime;
    }

    public int getSjNewsCount() {
        return sjNewsCount;
    }

    public void setSjNewsCount(int sjNewsCount) {
        this.sjNewsCount = sjNewsCount;
    }

    @Bindable
    public int getIsHasGuanzhu() {
        return isHasGuanzhu;
    }

    public void setIsHasGuanzhu(int isHasGuanzhu) {
        this.isHasGuanzhu = isHasGuanzhu;
        notifyPropertyChanged(BR.isHasGuanzhu);
    }

    /**
     * 添加关注
     */
    public void addFollow() {
        setIsHasGuanzhu(1);
    }

    /**
     * 取消关注
     */
    public void removeFollow() {
        setIsHasGuanzhu(0);
    }

    public boolean isSelf(){
        return UserUtils.logged()&&UserUtils.getUserId()==uid;
    }
}
