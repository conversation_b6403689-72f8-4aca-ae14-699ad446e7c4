package com.bxkj.personal.ui.activity.createresumesteptwo;

import androidx.annotation.NonNull;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.data.CreateResumeData;
import com.bxkj.personal.data.ResumeDefaultData;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumesteptwo
 * @Description: CreateResumeStepTwo
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CreateResumeStepTwoPresenter extends CreateResumeStepTwoContract.Presenter {

    private static final String TAG = CreateResumeStepTwoPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public CreateResumeStepTwoPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    private boolean checkCareerObjective(CreateResumeData createResumeData) {
//        if (CheckUtils.isNullOrEmpty(createResumeData.getName())) {
//            mView.onError("未填写简历名称");
//            return false;
//        } else
        if (CheckUtils.isNullOrEmpty(createResumeData.getDetailsName())) {
            mView.onError("未填写原从事职业");
            return false;
        } else if (createResumeData.getJobType2() == CommonApiConstants.NO_ID) {
            mView.onError("未选择期望类别");
            return false;
        } else if (CheckUtils.isNullOrEmpty(createResumeData.getDetailsName2())) {
            mView.onError("未填写具体职位");
            return false;
        } else if (createResumeData.getJnid() == 0) {
            mView.onError("未选择工作性质");
            return false;
        } else if (createResumeData.getXiang() == CommonApiConstants.NO_DATA) {
            mView.onError("请选择具体工作地点");
            return false;
        } else if (createResumeData.getWtid() == 0) {
            mView.onError("未选择工作经验");
            return false;
        }
        return true;
    }

    @Override
    void getCreateResumeDefaultInfo(int userId) {
        mPersonalApi.getCreateResumeDefaultData(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.getDefaultInfoSuccess((ResumeDefaultData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void createResume(CreateResumeData createResumeData) {
        if (!checkCareerObjective(createResumeData)) return;
        mView.showLoading();
        mPersonalApi.createResume(createResumeData)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.createResumeSuccess(Integer.parseInt(baseResponse.getMsg()));
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }
}
