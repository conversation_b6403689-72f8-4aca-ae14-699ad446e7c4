package com.bxkj.personal.ui.activity.microresumeinfo

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResult
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.MicroResumeData
import com.bxkj.common.data.RandomAvatarData
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.jrzp.support.feature.data.AddressItemData
import com.bxkj.jrzp.support.feature.ui.selectaddress.AddressMultiSelectActivity
import com.bxkj.jrzp.support.feature.ui.seletjobtype.SelectJobTypeActivity
import com.bxkj.personal.data.source.MyResumeRepo
import com.bxkj.personal.ui.activity.jobintention.JobIntentionActivity
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/5
 */
class MicroResumeInfoViewModel @Inject constructor(
  private val _My_resumeRepo: MyResumeRepo,
  private val _commonRepo: CommonRepository
) : BaseViewModel() {

  val microResumeInfo = MutableLiveData<MicroResumeData>().apply { value = MicroResumeData() }
  private val showAvatarPath = MutableLiveData<String>()
  val fixAvatarPath = MediatorLiveData<String>().apply {
    addSource(showAvatarPath) {
      value = it.replace("https", "http")
    }
  }

  val showBirthdayPickerCommand = MutableLiveData<VMEvent<String?>>()
  val showResumeOpenStatePickerCommand = MutableLiveData<VMEvent<String>>()
  val toSelectJobTypeCommand = MutableLiveData<VMEvent<List<JobTypeData>?>>()
  val showAddressPickerCommand = MutableLiveData<VMEvent<MicroResumeData>>()

  val editMicroInfoSuccessEvent = MutableLiveData<VMEvent<Unit>>()

  private var _randomAvatar: RandomAvatarData? = null

  fun start() {
    viewModelScope.launch {
      showLoading()
      _My_resumeRepo.getMicroResumeInfo()
        .handleResult({
          it?.let {
            showAvatarPath.value = it.userPhoto.getOrDefault()
            microResumeInfo.value = it
          } ?: let { microResumeInfo.value = MicroResumeData() }
        }, {
          microResumeInfo.value = MicroResumeData()
        }, {
          hideLoading()
          _commonRepo.getUserRandomAvatar()
            .handleResult({
              _randomAvatar = it
            }, final = {
              setUserGender(microResumeInfo.value?.userSex.getOrDefault())
            })
        })
    }
  }

  fun showBirthdayPicker() {
    showBirthdayPickerCommand.value = VMEvent(microResumeInfo.value?.birthDate ?: "")
  }

  fun toSelectExpectJobType() {
    microResumeInfo.value?.let {
      toSelectJobTypeCommand.value = VMEvent(it.intention ?: ArrayList())
    }
  }

  fun toSelectExpectAddress() {
    microResumeInfo.value?.let {
      showAddressPickerCommand.value = VMEvent(it)
    }
  }

  fun showResumeOpenStatePicker() {
    microResumeInfo.value?.let {
      showResumeOpenStatePickerCommand.value = VMEvent(it.getOpenStateText().getOrDefault())
    }
  }

  fun setUserGender(gender: Int) {
    microResumeInfo.value?.apply {
      userSex = gender
      if (userPhoto.isNullOrBlank()) {
        _randomAvatar?.let { avatars ->
          if (userPhoto.isNullOrBlank() || avatars.contain(showAvatarPath.value.getOrDefault())) {
            val randomAvatar =
              if (gender == 0) avatars.getRandomMaleAvatar() else avatars.getRandomFemaleAvatar()
            userPhoto = randomAvatar
            showAvatarPath.value = randomAvatar
          }
        }
      }
    }
  }

  fun setUserBirthday(birthday: String) {
    microResumeInfo.value?.updateBirthday(birthday)
  }

  fun setUserEducation(name: String, id: Int) {
    microResumeInfo.value?.updateEducation(id, name)
  }

  fun setUserWorkExp(pickerOptionsData: PickerOptionsData) {
    microResumeInfo.value?.updateWorkExp(pickerOptionsData.id, pickerOptionsData.name)
  }

  fun setOpenState(openState: Int) {
    microResumeInfo.value?.updateOpenState(openState)
  }

  fun setSalaryRange(minSalary: Int, maxSalary: Int) {
    microResumeInfo.value?.setSalaryRange(minSalary, maxSalary)
  }

  fun handleSelectJobTypeResult(result: ActivityResult) {
    if (result.resultCode == Activity.RESULT_OK) {
      result.data?.let {
        microResumeInfo.value?.updateIntention(
          it.getParcelableArrayListExtra<JobTypeData>(
            SelectJobTypeActivity.EXTRA_SELECTED_JOB_TYPE
          )?.toList()
        )
      }
    }
  }

  fun handleSelectAddressResult(it: ActivityResult?) {
    it?.let {
      if (it.resultCode == Activity.RESULT_OK && it.data != null) {
        val selectedAddress =
          it.data!!.getParcelableArrayListExtra<AddressItemData>(
            AddressMultiSelectActivity.EXTRA_SELECTED_ADDRESS
          )
        microResumeInfo.value?.updateAddress(selectedAddress)
      }
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val imgPath = data.getSelectedFirstMediaPath()
        showAvatarPath.value = imgPath
        microResumeInfo.value?.userPhoto =
          CommonApiConstants.IMG_UPLOAD_PREFIX + ZPFileUtils.bitmapToString(
            imgPath,
            0.1f
          )
      }
    }
  }

  fun handleJobIntentionResult(result: ActivityResult) {
    if (result.resultCode == Activity.RESULT_OK) {
      result.data?.let {
        microResumeInfo.value =
          it.getParcelableExtra(JobIntentionActivity.EXTRA_MICRO_RESUME)
      }
    }
  }

  fun save() {
    microResumeInfo.value?.let {
      if (it.intention.isNullOrEmpty()) {
        showToast("请完善求职意向")
        return
      }
      showLoading()
      viewModelScope.launch {
        _My_resumeRepo.updateMicroResumeInfo(it)
          .handleResult({
            showToast("保存成功")
            editMicroInfoSuccessEvent.value = VMEvent(Unit)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }
}