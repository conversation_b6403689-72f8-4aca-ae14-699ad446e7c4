package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.personal.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 未读消息数据
 * @TODO: TODO
 * @date 2018/12/12
 */
public class UnreadMsgGroupData extends BaseObservable {

  private int unreadMsgNoticeCount;
  private int unreadSubMsgCount;
  private int unreadSewMeCount;
  private int unreadSystemMsgCount;

  private boolean hasNoticePermission;

  @Bindable
  public int getUnreadMsgNoticeCount() {
    return unreadMsgNoticeCount;
  }

  public void setUnreadMsgNoticeCount(int unreadMsgNoticeCount) {
    this.unreadMsgNoticeCount = unreadMsgNoticeCount;
    notifyPropertyChanged(BR.unreadMsgNoticeCount);
  }

  @Bindable
  public int getUnreadSubMsgCount() {
    return unreadSubMsgCount;
  }

  public void setUnreadSubMsgCount(int unreadSubMsgCount) {
    this.unreadSubMsgCount = unreadSubMsgCount;
    notifyPropertyChanged(BR.unreadSubMsgCount);
  }

  @Bindable
  public int getUnreadViewMeCount() {
    return unreadSewMeCount;
  }

  public void setUnreadViewMeCount(int unreadSewMeCount) {
    this.unreadSewMeCount = unreadSewMeCount;
    notifyPropertyChanged(BR.unreadViewMeCount);
  }

  @Bindable
  public int getUnreadSystemMsgCount() {
    return unreadSystemMsgCount;
  }

  public void setUnreadSystemMsgCount(int unreadSystemMsgCount) {
    this.unreadSystemMsgCount = unreadSystemMsgCount;
    notifyPropertyChanged(BR.unreadSystemMsgCount);
  }

  @Bindable
  public boolean isHasNoticePermission() {
    return hasNoticePermission;
  }

  public void setHasNoticePermission(boolean hasNoticePermission) {
    this.hasNoticePermission = hasNoticePermission;
    notifyPropertyChanged(BR.hasNoticePermission);
  }
}
