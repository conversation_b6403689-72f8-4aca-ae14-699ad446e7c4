package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: UpdateMessageStatus
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface UpdateMessageStatusContract {
    interface View extends BaseView {
        void updateMsgStatusSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void updateMessageStatus(int userId, int msgId);
    }
}
