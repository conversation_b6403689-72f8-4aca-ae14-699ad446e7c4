package com.bxkj.personal.ui.activity.jobdetails

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class JobDetailsNavigation {
  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/jobdetails"

    const val EXTRA_JOB_ID = "JOB_ID"

    const val EXTRA_SHOW_LIVE = "SHOW_LIVE"

    fun navigate(jobId: Int, showLive: Boolean = false): RouterNavigator {
      return Router.getInstance().to(PATH).withInt(EXTRA_JOB_ID, jobId)
        .withBoolean(EXTRA_SHOW_LIVE, showLive)
    }
  }
}