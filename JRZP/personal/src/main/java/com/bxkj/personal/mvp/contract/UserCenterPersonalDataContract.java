package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.UserCenterPersonalData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: UserCenterPersonalData
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface UserCenterPersonalDataContract {
    interface View extends BaseView {
        void getPersonalDataSuccess(UserCenterPersonalData userCenterPersonalData);
        void noPersonalData();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getUserCenterPersonalData(int userId);

    }
}
