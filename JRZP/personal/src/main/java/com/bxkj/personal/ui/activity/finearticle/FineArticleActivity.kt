package com.bxkj.personal.ui.activity.finearticle

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.data.FineArticleData
import com.bxkj.personal.databinding.PersonalActivityFineArticleBinding

/**
 * 好文
 * @author: sanjin
 * @date: 2022/8/31
 */
class FineArticleActivity :
    BaseDBActivity<PersonalActivityFineArticleBinding, FineArticleViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, FineArticleActivity::class.java)
        }
    }

    private val _detailsLauncher=registerForActivityResult(ActivityResultContracts.StartActivityForResult()){
        viewModel.handleDetailsPageResult(it)
    }

    override fun getViewModelClass(): Class<FineArticleViewModel> = FineArticleViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_fine_article

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupContentListAdapter()

        viewModel.start()
    }

    private fun setupContentListAdapter() {
        val articleListAdapter = SimpleDBListAdapter<FineArticleData>(
            this,
            R.layout.personal_recycler_fine_article_item
        ).apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    data[position]?.let {
                        startActivity(
                            FineArticleDetailsActivity.newIntent(
                                this@FineArticleActivity,
                                it.id
                            )
                        )
                    }
                }
            })
        }

        viewBinding.includeArticleList.recyclerContent.apply {
            layoutManager = LinearLayoutManager(this@FineArticleActivity)
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f4f4f4_8))
                    .build()
            )
        }

        viewModel.contentListViewModel.setAdapter(articleListAdapter)
    }
}