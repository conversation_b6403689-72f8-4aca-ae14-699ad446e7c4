package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.data.UnreadVideoMsgData
import javax.inject.Inject

/**
 * @date: 2020/7/4
 */
class MessageRepo @Inject constructor(
  private val mCoroutinesApi: CoroutinesApi
) : BaseRepo() {

  /**
   * 获取未读订阅消息数量
   */
  suspend fun getUnreadSubMsgCount(userID: Int): ReqResponse<Int> {
    return httpRequest {
      mCoroutinesApi.getUnreadSubMsgCount(ZPRequestBody().apply {
        put("uid", userID)
      }).apply {
        if (status == 10001) {
          data = msg.toInt()
        }
      }
    }
  }

  /**
   * 获取未读谁看过我数量
   */
  suspend fun getUnreadViewMeCount(userID: Int): ReqResponse<Int> {
    return httpRequest {
      mCoroutinesApi.getUnreadViewMeCount(ZPRequestBody().apply {
        put("uid", userID)
      }).apply {
        if (status == 10001) {
          data = msg.toInt()
        }
      }
    }
  }

  /**
   * 获取未读系统消息数量
   */
  suspend fun getUnreadSystemMsgCount(userID: Int): ReqResponse<Int> {
    return httpRequest {
      mCoroutinesApi.getUnreadSystemMsgCount(ZPRequestBody().apply {
        put("uid", userID)
      }).apply {
        if (status == 10001) {
          data = msg.toInt()
        }
      }
    }
  }

  /**
   * 检查是否存在未读视频消息
   */
  suspend fun checkHasUnreadVideoMsg(userID: Int): ReqResponse<UnreadVideoMsgData> {
    return httpRequest {
      mCoroutinesApi.checkHasUnreadVideoMsg(ZPRequestBody().apply {
        put("userID", userID)
      }).apply {
        if (status == 10001) {
          data = UnreadVideoMsgData(msg == "true")
        }
      }
    }
  }
}