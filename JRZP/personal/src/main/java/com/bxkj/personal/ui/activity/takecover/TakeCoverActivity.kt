package com.bxkj.personal.ui.activity.takecover

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.media.MediaMetadataRetriever
import android.media.MediaPlayer
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.VideoCoverView
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityTakeCoverBinding
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.takecover
 * @Description: 截取视频封面
 * <AUTHOR>
 * @date 2020/1/2
 * @version V1.0
 */
class TakeCoverActivity : BaseDBActivity<PersonalActivityTakeCoverBinding, TakeCoverViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_VIDEO_PATH = "VIDEO_PATH"
    const val EXTRA_COVER_PATH = "COVER_PATH"
    const val RESULT_CAPTURE_SUCCESS = Activity.RESULT_FIRST_USER + 1
    fun newIntent(context: Context, videoPath: String): Intent {
      return Intent(context, TakeCoverActivity::class.java)
        .apply {
          putExtra(EXTRA_VIDEO_PATH, videoPath)
        }
    }
  }

  private val mRetriever = MediaMetadataRetriever()

  private var mCoverPosition = 0f
  override fun getViewModelClass(): Class<TakeCoverViewModel> = TakeCoverViewModel::class.java


  override fun getLayoutId(): Int = R.layout.personal_activity_take_cover

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this
    setupVideoPlayer(intent.getStringExtra(EXTRA_VIDEO_PATH).getOrDefault())

    viewBinding.titleBar.setRightOptionClickListener {
      saveVideoCoverAndBack()
    }
  }

  private val mHandler = object : Handler(Looper.getMainLooper()) {
    override fun handleMessage(msg: Message) {
      msg.let {
        if (msg.obj !== null && msg.obj is Bitmap) {
          val keyFrameBitmap: Bitmap = msg.obj as Bitmap
          val imgPath = ZPFileUtils.saveBitmap(keyFrameBitmap)
          if (imgPath.isNullOrBlank()) {
            hiddenLoading()
            showToast(getString(R.string.edit_cover_failed))
          } else {
            hiddenLoading()
            backAndResult(imgPath)
          }
        }
      }
    }
  }

  private fun backAndResult(path: String) {
    val resultIntent = Intent()
      .apply {
        putExtra(EXTRA_COVER_PATH, path)
      }
    setResult(RESULT_CAPTURE_SUCCESS, resultIntent)
    finish()
  }

  private fun saveVideoCoverAndBack() {
    showLoading()
    Thread(Runnable {
      val coverBitmap = mRetriever.getFrameAtTime((mCoverPosition * 1000.toLong()).toLong())
      mHandler.sendMessage(Message()
        .apply {
          obj = coverBitmap
        })
    }).start()
  }

  private fun setupVideoPlayer(videoPath: String) {
    viewBinding.videoPlayer.setOnPreparedListener { mp ->
      viewBinding.videoPlayer.seekTo(0)
      mp.setOnInfoListener { _, what, _ ->
        if (what == MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START) {
          // video started
          viewBinding.videoPlayer.setBackgroundColor(Color.TRANSPARENT)
          return@setOnInfoListener true
        }
        return@setOnInfoListener false
      }
    }

    viewBinding.videoPlayer.setOnErrorListener { mp, what, extra ->
      return@setOnErrorListener false
    }

    viewBinding.videoPlayer.setVideoPath(videoPath)

    setupVideoCoverView(videoPath)
  }

  private fun setupVideoCoverView(videoPath: String) {
    mRetriever.setDataSource(videoPath)
    val time =
      mRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION).getOrDefault("0")

    viewBinding.vcView.setupVideoPath(videoPath)
    viewBinding.vcView.setOnProgressChangeListener(object :
      VideoCoverView.OnProgressChangeListener {
      override fun onProgress(progress: Int) {
        val msec = ((progress.toFloat() / 100) * time.toInt())
        mCoverPosition = msec
        viewBinding.videoPlayer.seekTo(msec.toInt())
      }
    })
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.tv_album -> {
          PermissionUtils.requestPermission(
            this@TakeCoverActivity,
            getString(R.string.permission_tips_title),
            getString(R.string.permission_select_img_tips),
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                PictureSelector.create(this@TakeCoverActivity)
                  .openGallery(SelectMimeType.ofImage())
                  .setSelectionMode(SelectModeConfig.SINGLE)
                  .setSandboxFileEngine(SandboxFileEngine.getInstance())
                  .setCompressEngine(ImageCompressEngine.getInstance())
                  .setImageEngine(GlideEngine.getInstance())
                  .setImageSpanCount(4)
                  .forResult(PictureConfig.CHOOSE_REQUEST)
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel_operation))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE
          )
        }
      }
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    mRetriever.release()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST && resultCode == Activity.RESULT_OK && data != null) {
      backAndResult(data.getSelectedFirstMediaPath())
    }
  }

}