package com.bxkj.personal.ui.fragment.homev3

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.bxkj.jrzp.support.scan.ui.qrcode.ScanQrCodeActivity
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.data.FamousCompanyData
import com.bxkj.personal.data.source.JobRepo.GetPartTimeJobParams
import com.bxkj.personal.databinding.PersonalFragmentHomeV3Binding
import com.bxkj.personal.ui.activity.govnews.GovRecruitmentActivity
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.activity.minesearchjobs.MineSearchJobsActivity
import com.bxkj.personal.ui.activity.recommendcompany.RecommendCompanyActivity
import com.bxkj.personal.ui.activity.recommendjob.RecommendJobActivity
import com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultActivityV2
import com.bxkj.personal.ui.activity.videolist.VideoListActivity
import com.bxkj.personal.ui.fragment.home.HomeADBannerAdapter
import com.bxkj.personal.ui.fragment.joblist.LatestJobListFragment
import com.bxkj.personal.ui.fragment.joblist.JobListPage
import com.bxkj.personal.ui.fragment.joblist.NearbyJobListFragment
import com.bxkj.personal.ui.fragment.parttimejoblist.PartTimeJobListFragment
import com.donkingliang.consecutivescroller.ConsecutiveScrollerLayout
import com.scwang.smartrefresh.layout.api.RefreshFooter
import com.scwang.smartrefresh.layout.listener.SimpleMultiPurposeListener
import com.skydoves.balloon.Balloon
import com.youth.banner.indicator.RectangleIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 *
 * @author: sanjin
 * @date: 2022/8/16
 */
class GeekHomeFragmentV3 : BaseDBFragment<PersonalFragmentHomeV3Binding, GeekHomeViewModelV3>(),
  View.OnClickListener {

  companion object {

    fun newInstance(): Fragment {
      return GeekHomeFragmentV3()
    }
  }

  private var _balloonNoLocationPermission: Balloon? = null

  override fun getViewModelClass(): Class<GeekHomeViewModelV3> = GeekHomeViewModelV3::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_home_v3

  override fun initImmersionBar() {
    statusBarManager.titleBar(viewBinding.titleBar).statusBarDarkFont(true, 0.4f).init()
  }

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    viewBinding.tvCity.text = if (UserUtils.getUserSelectedCityName()
        .isNullOrBlank()
    ) "全国" else UserUtils.getUserSelectedCityName()

    setupRefreshLayout()
    setupHotCompanyList()
    setupContent()
    setupContentScrollListener()

    subscribeCityChangeEvent()
    subscribeToTopEvent()
    subscribeViewModelEvent()
  }

  override fun onResume() {
    super.onResume()
    viewModel.recheckVisitorModeState()
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.tv_city -> {
          startActivity(
            CityPickerActivity.newIntent(requireActivity())
          )
        }

        R.id.tv_search -> {
          startActivity(MineSearchJobsActivity.newIntent(requireActivity()))
        }

        R.id.iv_scan_qr_code -> {
          startActivity(ScanQrCodeActivity.newIntent(requireContext()))
        }

        R.id.tv_post -> {
          RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.COMMAND_POST_NEWS))
        }

        R.id.tv_exit_visitor_mode -> {
          relaunchApp()
        }

        R.id.ll_header_right_top -> {
          startActivity(GovRecruitmentActivity.newIntent(requireContext()))
        }

        R.id.ll_header_right_center -> {
          startActivity(RecommendCompanyActivity.newIntent(requireContext()))
        }

        R.id.ll_header_right_bottom -> {
          startActivity(VideoListActivity.newIntent(requireContext()))
        }

        R.id.ll_header_right_school -> {
          RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_SWITCH_TO_SCHOOL_RECRUIT))
        }

        R.id.iv_filter -> {
          startActivity(
            SearchJobResultActivityV2.newIntent(requireActivity())
          )
        }
      }
    }
  }

  fun finishRefresh() {
    viewBinding.vpContent.viewPager2.isUserInputEnabled = true
    viewBinding.srlContent.finishRefresh()
    viewBinding.srlContent.finishLoadMore()
  }

  private fun subscribeCityChangeEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
        if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
          _balloonNoLocationPermission?.dismiss()
          viewBinding.tvCity.text = UserUtils.getUserSelectedCityName()
          viewModel.refreshFamousCompany()
        } else if (it.code == RxMsgCode.ACTION_HIDE_NO_PERMISSION_TIPS) {
          _balloonNoLocationPermission?.dismiss()
        }
      }
    )
  }

  private fun setupContentScrollListener() {
    changeTitleBarBgAlpha(0)
    viewBinding.scrollContent.setOnVerticalScrollChangeListener { _, scrollY, _, scrollState ->
      val percent = (scrollY.toFloat() / 100)
      changeTitleBarBgAlpha((255 * percent).toInt())
      if (scrollState == ConsecutiveScrollerLayout.SCROLL_STATE_IDLE) {
        viewBinding.ivBatchSendResume.visibility = View.VISIBLE
      } else {
        viewBinding.ivBatchSendResume.visibility = View.GONE
      }
    }
  }

  private fun changeTitleBarBgAlpha(alpha: Int) {
    viewBinding.vTitleBarBgFull.background.mutate().alpha = minOf(255, alpha)
  }

  private fun subscribeViewModelEvent() {
    viewModel.toBatchSendResumeCommand.observe(viewLifecycleOwner, EventObserver {
      startActivity(
        RecommendJobActivity.newIntent(requireContext())
      )
    })

    viewModel.toCreateResumeCommand.observe(viewLifecycleOwner, EventObserver {
      showToast(getString(R.string.personal_home_must_create_resume_first))
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })

    viewModel.adDataList.observe(viewLifecycleOwner) {
      viewBinding.bannerAd.apply {
        indicator = RectangleIndicator(requireContext())
        setAdapter(HomeADBannerAdapter(it).apply {
          setOnBannerListener { data, position ->
            data?.let {
              if (it.type == 1) {
                startActivity(RecommendJobActivity.newIntent(requireContext()))
              } else {
                UserHomeNavigation.navigate(it.id).start()
              }
            }
          }
        })
      }
    }
  }

  private fun setupRefreshLayout() {
    viewBinding.srlContent.setEnableLoadMore(false)
    viewBinding.srlContent.setOnRefreshListener {
      viewModel.refreshFamousCompany()
      //将下拉刷新分发到子Fragment
      viewBinding.vpContent.viewPager2.isUserInputEnabled = false
      val currentChildFragment =
        childFragmentManager.findFragmentByTag("f${viewBinding.vpContent.currentItem}")
      if (currentChildFragment is JobListPage) {
        currentChildFragment.refreshPage()
      }
    }

    viewBinding.srlContent.setOnMultiPurposeListener(object : SimpleMultiPurposeListener() {
      override fun onFooterMoving(
        footer: RefreshFooter?,
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        footerHeight: Int,
        maxDragHeight: Int
      ) {
        super.onFooterMoving(
          footer,
          isDragging,
          percent,
          offset,
          footerHeight,
          maxDragHeight
        )
        viewBinding.scrollContent.stickyOffset = offset
      }
    })
  }

  private fun subscribeToTopEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_MAIN_TAB_ONE_TO_TOP) {
            viewBinding.scrollContent.scrollTo(0, 0)
          }
        }
    )
  }

  private fun setupHotCompanyList() {
    viewBinding.recyclerFamousCompany.post {
      viewBinding.recyclerFamousCompany.apply {
        this.context?.let {
          layoutManager = GridLayoutManager(it, 4)
          addItemDecoration(GridItemDecoration(getResDrawable(R.drawable.divider_6)))
          adapter =
            object : SimpleDiffListAdapter<FamousCompanyData>(
              R.layout.personal_recycler_home_hot_company_item,
              FamousCompanyData.DiffCallback()
            ) {
              override fun bind(
                holder: SuperViewHolder,
                item: FamousCompanyData,
                position: Int
              ) {
                with(viewBinding.recyclerFamousCompany) {
                  holder.itemView.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    this.height / 2 - dip(2)    //高度为RecyclerView的一半，减去分割线高度
                  )
                }
                super.bind(holder, item, position)
              }
            }.apply {
              setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                  UserHomeNavigation.navigate(getData()?.get(position)?.uid.getOrDefault())
                    .start()
                }
              })
            }
        }
      }

      viewModel.start()

    }
  }

  private fun setupContent() {
    viewBinding.indicatorType.navigator = CommonNavigator(requireContext()).apply {
      adapter = HomeJobTypeIndicatorAdapter(arrayOf("最新职位", "附近直招")).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    }

    viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 2

      override fun createFragment(position: Int): Fragment {
        return when (position) {
          0 -> {
            LatestJobListFragment()
          }

          1 -> {
            NearbyJobListFragment()
          }

          else -> {
            PartTimeJobListFragment.newInstance(
              GetPartTimeJobParams(
                shi = UserUtils.getUserSelectedCityId(),
                px = 1
              )
            )
          }
        }
      }
    }

    viewBinding.vpContent.viewPager2.attachIndicator(viewBinding.indicatorType)
  }
}