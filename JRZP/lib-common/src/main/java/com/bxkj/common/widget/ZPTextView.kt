package com.bxkj.common.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView

/**
 * Description:
 * Author:45457
 **/
class ZPTextView @JvmOverloads constructor(context: Context, attributeSet: AttributeSet? = null, defStyles: Int = 0) :
    AppCompatTextView(context, attributeSet, defStyles) {

    init {
        paint.isFakeBoldText = true
        
    }
}