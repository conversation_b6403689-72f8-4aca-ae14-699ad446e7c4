package com.bxkj.common.widget.indicator

import android.content.Context
import android.graphics.Typeface

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/25
 * @version: V1.0
 */
class BoldPagerTitleView constructor(context: Context) : ScalePagerTitleView(context) {

  override fun onSelected(index: Int, totalCount: Int) {
    super.onSelected(index, totalCount)
    typeface = Typeface.DEFAULT_BOLD
  }

  override fun onDeselected(index: Int, totalCount: Int) {
    super.onDeselected(index, totalCount)
    typeface = Typeface.DEFAULT
  }
}