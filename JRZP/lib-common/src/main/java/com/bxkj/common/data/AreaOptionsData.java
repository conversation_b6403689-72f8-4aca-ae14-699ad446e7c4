package com.bxkj.common.data;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import com.bxkj.common.widget.filterpopup.FilterOption;
import com.bxkj.common.widget.popup.IWheelOptions;
import java.util.List;

/**
 * @date 2018/4/3
 */
@Entity(tableName = "table_user_select_city_history")
public class AreaOptionsData implements Parcelable, IWheelOptions,
  FilterOption {

  /**
   * id : 1 pid : 0 type : 0 name : 上海
   */

  @PrimaryKey(autoGenerate = true)
  private int tableId;
  private int id;
  private int pid;
  private String pName;
  private int type;
  private String name;
  @Ignore
  private List<AreaOptionsData> cityList;

  public AreaOptionsData() {
  }

  public AreaOptionsData(int id, String name) {
    this.id = id;
    this.name = name;
    this.pName = "";
  }

  public AreaOptionsData(int id, int type, String name) {
    this.id = id;
    this.type = type;
    this.name = name;
  }

  public AreaOptionsData(int id, int pid, String pName, int type, String name) {
    this.id = id;
    this.pid = pid;
    this.pName = pName;
    this.type = type;
    this.name = name;
  }

  protected AreaOptionsData(Parcel in) {
    id = in.readInt();
    pid = in.readInt();
    pName = in.readString();
    type = in.readInt();
    name = in.readString();
    cityList = in.createTypedArrayList(AreaOptionsData.CREATOR);
  }

  public static final Creator<AreaOptionsData> CREATOR = new Creator<AreaOptionsData>() {
    @Override
    public AreaOptionsData createFromParcel(Parcel in) {
      return new AreaOptionsData(in);
    }

    @Override
    public AreaOptionsData[] newArray(int size) {
      return new AreaOptionsData[size];
    }
  };

  public int getTableId() {
    return tableId;
  }

  public void setTableId(int tableId) {
    this.tableId = tableId;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getPid() {
    return pid;
  }

  public void setPid(int pid) {
    this.pid = pid;
  }

  public String getPName() {
    return pName;
  }

  public void setPName(String pName) {
    this.pName = pName;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<AreaOptionsData> getCityList() {
    return cityList;
  }

  public void setCityList(List<AreaOptionsData> cityList) {
    this.cityList = cityList;
  }

  @Override
  public String getItemOption() {
    return name;
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel parcel, int i) {
    parcel.writeInt(id);
    parcel.writeInt(pid);
    parcel.writeString(pName);
    parcel.writeInt(type);
    parcel.writeString(name);
    parcel.writeTypedList(cityList);
  }

  public static class DiffCallBack extends DiffUtil.ItemCallback<AreaOptionsData> {

    @Override
    public boolean areItemsTheSame(@NonNull AreaOptionsData oldItem, @NonNull
    AreaOptionsData newItem) {
      return oldItem.id == newItem.id;
    }

    @Override
    public boolean areContentsTheSame(@NonNull AreaOptionsData oldItem, @NonNull
    AreaOptionsData newItem) {
      return oldItem.name.equals(newItem.name);
    }
  }

  public static AreaOptionsData getDefaultLocation() {
    return new AreaOptionsData(28590, 28009, "浙江", 0, "杭州");
  }
}
