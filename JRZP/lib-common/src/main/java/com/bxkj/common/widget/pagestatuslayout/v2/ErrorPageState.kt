package com.bxkj.common.widget.pagestatuslayout.v2

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.Guideline
import com.bxkj.common.R
import com.bxkj.common.util.kotlin.setHeight
import com.sanjindev.pagestatelayout.VisibilityPageState

/**
 *
 * @author: sanjin
 * @date: 2022/4/19
 */
class ErrorPageState : VisibilityPageState(R.layout.view_error_layout) {

    private var ivImage: ImageView? = null
    private var tvContent: TextView? = null
    private var tvNext: TextView? = null
    private var glCenterLine: Guideline? = null

    override fun onViewShow(parent: ViewGroup, view: View) {
        super.onViewShow(parent, view)

        view.findViewById<TextView>(R.id.tv_title).visibility = View.GONE

        if (ivImage == null) {
            ivImage = view.findViewById<ImageView>(R.id.iv_error_img).apply {
                setImageResource(R.drawable.ic_error_no_network)
            }
        }
        if (tvContent == null) {
            tvContent = view.findViewById<TextView>(R.id.tv_error_content).apply {
                text = "请求出现错误"
            }
        }
        if (tvNext == null) {
            tvNext = view.findViewById<TextView>(R.id.tv_error_next).apply {
                text = "重新加载"
            }
        }

        glCenterLine = view.findViewById<Guideline>(R.id.gl_center_line)
    }

    fun setContent(content: String) {
        tvContent?.text = content
    }

    fun setImage(@DrawableRes image: Int) {
        ivImage?.setImageResource(image)
    }

    fun setCenterLinePercent(percent: Float) {
        glCenterLine?.setGuidelinePercent(percent)
    }

    fun setNextOptionText(text: String) {
        tvNext?.text = text
    }

    fun setLayoutHeight(height: Int) {
        view?.setHeight(height)
    }

    fun setNextOptionClickListener(onClickListener: View.OnClickListener) {
        tvNext?.setOnClickListener(onClickListener)
    }
}