package com.bxkj.common.adapter.viewpager

import android.annotation.SuppressLint
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.PagerAdapter

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.adapter
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/7/18
 * @version V1.0
 */
class CommonPagerAdapter : FragmentStatePagerAdapter {

  private var mFragments: List<Fragment>? = null
  private var mTitles: Array<String>? = null

  constructor(fm: FragmentManager, fragments: List<Fragment>? = null) : this(
    fm,
    fragments,
    null
  )

  @SuppressLint("WrongConstant")
  constructor(fm: FragmentManager, fragments: List<Fragment>?, titles: Array<String>?) : super(
    fm,
    BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
  ) {
    this.mFragments = fragments
    this.mTitles = titles
  }

  override fun getItem(position: Int): Fragment = mFragments?.get(position)!!

  override fun getCount(): Int = mFragments?.size ?: 0

  override fun getPageTitle(position: Int): CharSequence? = mTitles?.get(position)

  override fun getItemPosition(item: Any): Int {
    return PagerAdapter.POSITION_NONE
  }

  fun reset(newsPageList: List<Fragment>?) {
    mFragments = newsPageList
    notifyDataSetChanged()
  }
}