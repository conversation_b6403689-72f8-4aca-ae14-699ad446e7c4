package com.bxkj.common.widget.filterpopup;

import android.app.Activity;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.annotation.StyleRes;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.bxkj.common.R;
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.FilterPopup
 * @Description: 筛选PopupWindow
 * @TODO: TODO
 * @date 2018/4/3
 */

public class FilterPopup extends PopupWindow {

    public static class Builder {
        private Activity mActivity;
        private List filterOptionsDataList;
        private int width = ViewGroup.LayoutParams.MATCH_PARENT;
        private int height = ViewGroup.LayoutParams.WRAP_CONTENT;
        private int bottomBarVisible = View.VISIBLE;
        private boolean isItemClickedDismiss = false;
        private int animStyle;
        private boolean hasShadow;

        public Builder(Activity activity) {
            mActivity = activity;
        }

        public Builder setHeight(int height) {
            this.height = height;
            return this;
        }

        public Builder setWidth(int width) {
            this.width = width;
            return this;
        }

        public Builder setAnimStyle(@StyleRes int animStyle) {
            this.animStyle = animStyle;
            return this;
        }

        public Builder hasShadow(boolean hasShadow) {
            this.hasShadow = hasShadow;
            return this;
        }

        public Builder setData(List data) {
            this.filterOptionsDataList = data;
            return this;
        }

        public Builder setBottomBarVisible(int visible) {
            this.bottomBarVisible = visible;
            return this;
        }

        public Builder setItemClickedDismiss(boolean isItemClickedDismiss) {
            this.isItemClickedDismiss = isItemClickedDismiss;
            return this;
        }

        public FilterPopup build() {
            return new FilterPopup(this);
        }
    }

    private Activity mActivity;
    private int mWidth;
    private int mHeight;
    private int mBottomBarVisible;
    private int mAnimStyle;
    private boolean mHasShadow;
    private View rootView;
    private List mListData;
    private RecyclerView recyclerFilterOptions;
    private MultiTypeAdapter mMultiTypeAdapter;
    private Map<Integer, Integer> positionHolderMap;
    private OnFilterConfirmListener OnFilterConfirmListener;
    private boolean isItemClickedDismiss;
    private List mOptionsGroupList;

    public FilterPopup(Builder builder) {
        super(builder.mActivity);
        mActivity = builder.mActivity;
        mWidth = builder.width;
        mHeight = builder.height;
        mAnimStyle = builder.animStyle;
        mHasShadow = builder.hasShadow;
        mBottomBarVisible = builder.bottomBarVisible;
        mListData = builder.filterOptionsDataList;
        isItemClickedDismiss = builder.isItemClickedDismiss;
        initView();
    }

    private void initView() {
        rootView = LayoutInflater.from(mActivity).inflate(R.layout.common_popup_filter_by_more, null);
        setContentView(rootView);

        setWidth(mWidth);
        setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundDrawable(new ColorDrawable());
        setOutsideTouchable(true);
        setFocusable(true);
        setAnimationStyle(mAnimStyle);

        rootView.findViewById(R.id.v_line).setVisibility(mBottomBarVisible);
        rootView.findViewById(R.id.ll_filter_bottom_bar).setVisibility(mBottomBarVisible);
        rootView.findViewById(R.id.ll_filter_content).getLayoutParams().height = mHeight;
        rootView.findViewById(R.id.v_other).setOnClickListener(view -> dismiss());

        mOptionsGroupList = new ArrayList();
        recyclerFilterOptions = rootView.findViewById(R.id.recycler_filter_options);
        recyclerFilterOptions.setLayoutManager(new LinearLayoutManager(mActivity));
        mMultiTypeAdapter = new MultiTypeAdapter(mActivity, mListData);
        mMultiTypeAdapter.register(FilterGroupTitle.class, new FilterGroupTitleViewBinder());
        FilterItemRecyclerViewBinder filterItemRecyclerViewBinder = new FilterItemRecyclerViewBinder(mActivity);
        mMultiTypeAdapter.register(FilterOptionsGroup.class, filterItemRecyclerViewBinder);
        recyclerFilterOptions.setAdapter(mMultiTypeAdapter);

        positionHolderMap = new HashMap<>();

        filterItemRecyclerViewBinder.setOnFilterOptionClickListener((type, position) -> {
            positionHolderMap.put(type, position);
            if (isItemClickedDismiss) {
                OnFilterConfirmListener.onFilterConfirm(positionHolderMap);
                dismiss();
            }
        });

        //点击重置
        rootView.findViewById(R.id.tv_filter_reset).setOnClickListener(view -> {
            filterItemRecyclerViewBinder.resetPosition();
            mMultiTypeAdapter.notifyDataSetChanged();
            positionHolderMap.clear();
        });

        //点击确定
        rootView.findViewById(R.id.tv_filter_confirm).setOnClickListener(view -> {
            if (OnFilterConfirmListener != null && !mOptionsGroupList.isEmpty()) {
                OnFilterConfirmListener.onFilterConfirm(positionHolderMap);
            }
            dismiss();
        });
    }

    /**
     * 添加分组标题
     *
     * @param filterGroupTitle
     */
    public void addGroupTitle(FilterGroupTitle filterGroupTitle) {
        mMultiTypeAdapter.add(filterGroupTitle);
    }

    /**
     * 添加分组items
     *
     * @param filterOptionsGroup
     */
    public void addGroupItems(FilterOptionsGroup filterOptionsGroup) {
        mOptionsGroupList.add(filterOptionsGroup.filterOptionList);
        mMultiTypeAdapter.add(filterOptionsGroup);
    }

    public void setData(List data) {
        mOptionsGroupList.clear();
        for (Object datum : data) {
            if (datum instanceof FilterOptionsGroup) {
                mOptionsGroupList.add(((FilterOptionsGroup) datum).filterOptionList);
            }
        }
        mMultiTypeAdapter.setData(data);
        mMultiTypeAdapter.notifyDataSetChanged();
    }

    public List getOptionsGroupData(int position) {
        return (List) mOptionsGroupList.get(position);
    }

    public void setOnFilterConfirmListener(OnFilterConfirmListener onFilterConfirmListener) {
        OnFilterConfirmListener = onFilterConfirmListener;
    }

    public interface OnFilterConfirmListener {
        void onFilterConfirm(Map<Integer, Integer> positionHolderMap);
    }

    @Override
    public void showAsDropDown(View anchor) {
        if (Build.VERSION.SDK_INT >= 24) {
            Rect visibleFrame = new Rect();
            anchor.getGlobalVisibleRect(visibleFrame);
            int height = anchor.getResources().getDisplayMetrics().heightPixels - visibleFrame.bottom;
            setHeight(height);
        }
        super.showAsDropDown(anchor);
    }

    @Override
    public void showAsDropDown(View anchor, int xoff, int yoff) {
        if (Build.VERSION.SDK_INT >= 24) {
            Rect visibleFrame = new Rect();
            anchor.getGlobalVisibleRect(visibleFrame);
            int height = anchor.getResources().getDisplayMetrics().heightPixels - visibleFrame.bottom;
            setHeight(height);
        }
        super.showAsDropDown(anchor, xoff, yoff);
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        if (mHasShadow) {
            setBackgroundAlpha(0.8f);
        }
    }

    private void setBackgroundAlpha(float alpha) {
        WindowManager.LayoutParams layoutParams = mActivity.getWindow().getAttributes();
        layoutParams.alpha = alpha;
        mActivity.getWindow().setAttributes(layoutParams);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (mHasShadow) {
            setBackgroundAlpha(1.0f);
        }
    }
}
