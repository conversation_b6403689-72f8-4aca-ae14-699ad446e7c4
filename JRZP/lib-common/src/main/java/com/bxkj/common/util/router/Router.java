package com.bxkj.common.util.router;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description: 路由框架包装类
 * @TODO: TODO
 * @date 2018/4/12
 */

public class Router {

    private static Router instance;

    private Router() {
    }

    public static Router getInstance() {
        if (instance == null) {
            instance = new Router();
        }
        return instance;
    }

    public RouterNavigator to(String url) {
        return new RouterNavigator(url);
    }

}
