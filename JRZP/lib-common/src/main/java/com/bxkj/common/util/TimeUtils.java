package com.bxkj.common.util;

import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description: 时间工具类
 * @TODO: TODO
 * @date 2018/4/16
 */

public class TimeUtils {

  /**
   * 秒转时分秒
   */
  public static String second2MinutesAndSeconds(long preSecond) {
    String hour = String.valueOf(preSecond / 3600);
    String minute = String.valueOf(preSecond % 3600 / 60);
    String second = String.valueOf(preSecond % 3600 % 60);
    return handleLessThan10(hour) + ":" + handleLessThan10(minute) + ":" + handleLessThan10(second);
  }

  public static String second2MinutesAndSeconds(long preSecond, String... splitters) {
    String hour = String.valueOf(preSecond / 3600);
    String minute = String.valueOf(preSecond % 3600 / 60);
    String second = String.valueOf(preSecond % 3600 % 60);
    return handleLessThan10(hour)
        + splitters[0]
        + handleLessThan10(minute)
        + splitters[1]
        + handleLessThan10(second)
        + (splitters.length > 2 ? splitters[2] : "");
  }

  /**
   * 毫秒转时分秒
   */
  public static String millisecondFormat(long millisecond) {
    String hour = String.valueOf(millisecond / (3600 * 1000));
    String minute = String.valueOf(millisecond % (3600 * 1000) / (60 * 1000));
    String second = String.valueOf(millisecond % (3600 * 1000) % (60 * 1000) / 1000);
    String millisSecond = String.valueOf(millisecond % (3600 * 1000) % (60 * 1000) % 1000 / 100);
    return handleLessThan10(hour)
        + ":"
        + handleLessThan10(minute)
        + ":"
        + handleLessThan10(second)
        + "."
        + millisSecond;
  }

  public static String timestamp2Date(long timestamp, String pattern) {
    return new SimpleDateFormat(pattern, Locale.getDefault()).format(
        new Date(timestamp));
  }

  /**
   * 判断是否是今天
   */
  public static boolean isToday(Calendar calendar) {
    Calendar toDayCalendar = Calendar.getInstance();
    return toDayCalendar.get(Calendar.YEAR) == calendar.get(Calendar.YEAR)
        && (toDayCalendar.get(Calendar.MONTH) + 1) == calendar.get(Calendar.MONTH)
        && toDayCalendar.get(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH);
  }

  /**
   * 秒转天时分秒
   */
  public static String second2DayMinutesAndSeconds(long preSecond, String... splitters) {
    String day = String.valueOf(preSecond / (3600 * 24));
    String hour = String.valueOf(preSecond % (3600 * 24) / 3600);
    String minute = String.valueOf(preSecond % 3600 / 60);
    String second = String.valueOf(preSecond % 3600 % 60);
    return handleLessThan10(day)
        + splitters[0]
        + handleLessThan10(hour)
        + splitters[1]
        + handleLessThan10(minute)
        + splitters[2]
        + handleLessThan10(second)
        + (splitters.length > 3 ? splitters[3] : "");
  }

  /**
   * 小于10前面补0
   */
  public static String handleLessThan10(String obj) {
    if (obj.length() == 1) {
      return "0" + obj;
    }
    return obj;
  }

  /**
   * String 类型的日期转换成 Calendar
   */
  public static Calendar string2Calendar(String date) {
    return string2Calendar(date, "yyyy/MM/dd");
  }

  public static Calendar string2Calendar(String date, String pattern) {
    final Calendar calendar = Calendar.getInstance();
    calendar.setTime(string2Date(date, pattern));
    return calendar;
  }

  /**
   * String 转 Data
   */
  public static Date string2Date(String date, String pattern) {
    try {
      return new SimpleDateFormat(pattern, Locale.CHINA).parse(date);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return null;
  }

  public static String formatTimeDiff(String startDate) {
    Calendar todayCalendar = Calendar.getInstance();
    Calendar startCalendar = string2Calendar(startDate);
    long startTime = startCalendar.getTimeInMillis();
    long todayTime = todayCalendar.getTimeInMillis();
    if (startTime - todayTime < 0) {
      return "进行中";
    } else {
      int diffDays = (int) ((startTime - todayTime) / (1000 * 60 * 60 * 24));
      if (diffDays < 30) {
        return diffDays + "天";
      } else {
        return diffDays / 30 + "月";
      }
    }
  }

  /**
   * 获取今天的日期
   */
  public static String getToDayDate() {
    return getToDayDate("yyyy-MM-dd");
  }

  public static String getToDayDate(String pattern) {
    Date todayData = new Date(System.currentTimeMillis());
    return new SimpleDateFormat(pattern, Locale.getDefault()).format(todayData);
  }

  public static final int HOUR = 0x01;
  public static final int MINUTE = 0x02;
  public static final int SECOND = 0x03;

  /**
   * 获取当前时间
   *
   * @param type 精确单位
   */
  public static String getCurrentTime(int type) {
    return getCurrentTime(type, "/");
  }

  /**
   * 获取当前时间
   *
   * @param type 精确单位
   */
  public static String getCurrentTime(int type, String split) {
    Date currentTime = new Date(System.currentTimeMillis());
    String pattern = "";
    switch (type) {
      case HOUR:
        pattern = "yyyy" + split + "MM" + split + "dd HH";
        break;
      case MINUTE:
        pattern = "yyyy" + split + "MM" + split + "dd HH:mm";
        break;
      case SECOND:
        pattern = "yyyy" + split + "MM" + split + "dd HH:mm:ss";
        break;
      default:
        break;
    }
    return new SimpleDateFormat(pattern, Locale.getDefault()).format(currentTime);
  }

  public static String getCurrentTime(String pattern) {
    Date currentTime = new Date(System.currentTimeMillis());
    return new SimpleDateFormat(pattern, Locale.getDefault()).format(currentTime);
  }

  /**
   * 获取n天或n天后的日期
   */
  public static String getDateOfBeforeOrAfterNDay(int day) {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.DATE, day);
    return new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.getTime());
  }

  /**
   * 获取n月前或n月后
   */
  public static Calendar getDateOfBeforeOrAfterNMonth(int month) {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MONTH, month);
    return calendar;
  }

  public static int getAge(String birthday, String pattern) {
    if (CheckUtils.isNullOrEmpty(birthday)) {
      return 0;
    }
    int age = 0;
    try {
      Date birthDate = new SimpleDateFormat(pattern, Locale.getDefault()).parse(birthday);
      Calendar now = Calendar.getInstance();
      Calendar birth = Calendar.getInstance();
      birth.setTime(birthDate);
      if (now.before(birth)) {
        return age;
      }
      age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
      if (now.get(Calendar.DAY_OF_MONTH) < birth.get(Calendar.DAY_OF_MONTH)) {
        age -= 1;
      }
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return Math.max(age, 0);
  }

  /**
   * 根据出生日期计算年龄
   */
  public static int getAge(String birthday) {
    if (birthday == null) {
      return 0;
    }
    return getAge(birthday, "yyyy/MM/dd");
  }

  /**
   * 根据时间字符串获取毫秒数
   */
  public static long getTimeMillis(String strTime) {
    long returnMillis = 0;
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm", Locale.getDefault());
    try {
      Date date = dateFormat.parse(strTime);
      returnMillis = date.getTime();
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return returnMillis;
  }

  public static long getTimeMillis(String strTime, String pattern) {
    long returnMillis = 0;
    SimpleDateFormat dateFormat = new SimpleDateFormat(pattern, Locale.getDefault());
    try {
      Date date = dateFormat.parse(strTime);
      returnMillis = date.getTime();
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return returnMillis;
  }

  /**
   * 毫秒转小时
   */
  public static int millis2Hour(long millis) {
    return (int) (millis / (60 * 60 * 1000));
  }

  /**
   * 毫秒转天
   */
  public static int millis2Day(long diffMillis) {
    return (int) (diffMillis / (24 * 60 * 60 * 1000));
  }

  /**
   * 获取时间差
   */
  public static String getTwoTimeDiff(String startTime, String endTime) {
    String timeDiff;
    long diffMillis = getTimeMillis(endTime) - getTimeMillis(startTime);
    int diffHour = millis2Hour(diffMillis);
    if (diffHour < 1) {
      int diffMinute = (int) (diffMillis % (3600 * 1000) / (60 * 1000));
      if (diffMillis < 1) {
        timeDiff = "刚刚";
      } else {
        timeDiff = diffMinute + "分钟前";
      }
    } else if (diffHour <= 24) {
      timeDiff = diffHour + "小时前";
    } else {
      return startTime;
    }
    return timeDiff;
  }

  public static String getNewTimeDiff(String startTime) {
    return formatTimeDiff(startTime, "yyyy.MM.dd HH:mm");
  }

  public static String formatTimeDiff(String time, String pattern) {
    if (CheckUtils.isNullOrEmpty(time)) {
      return "";
    }
    String timeDiff;
    long diffMillis =
        getTimeMillis(getCurrentTime(TimeUtils.MINUTE)) - getTimeMillis(time, pattern);
    int diffHour = millis2Hour(diffMillis);
    if (diffHour < 1) {
      int diffMinute = (int) (diffMillis % (3600 * 1000) / (60 * 1000));
      if (diffMillis < 1) {
        timeDiff = "刚刚";
      } else {
        timeDiff = diffMinute + "分钟前";
      }
    } else if (diffHour <= 24) {
      timeDiff = diffHour + "小时前";
    } else {
      int diffDays = millis2Day(diffMillis);
      if (diffDays >= 30) {
        int diffMonth = diffDays / 30;
        if (diffMonth >= 12) {
          //timeDiff = diffMonth / 12 + "年前";
          timeDiff = "1年前";
        } else {
          timeDiff = diffMonth + "个月前";
        }
      } else {
        timeDiff = diffDays + "天前";
      }
    }
    return timeDiff;
  }

  public static void saveLastRequestTime(@NotNull String tag) {
    SPUtils.getInstance().put(tag, System.currentTimeMillis());
  }

  public static long getRequestMinuteDiff(String tag) {
    Date lastTime = new Date(SPUtils.getInstance().getLong(tag));
    Date curTime = new Date(System.currentTimeMillis());
    long msDiff = curTime.getTime() - lastTime.getTime();
    return msDiff / (10 * 60 * 1000);
  }

  public static long getSecondDifference(Date startDate, Date endDate) {
    return (endDate.getTime() - startDate.getTime()) / 1000;
  }

  public static String getTimeDiff2(String startTime, String endTime) {
    String timeDiff = "";
    Calendar startCalendar = stringToCalendar("yyyy/MM/dd HH:mm:ss", startTime);
    Calendar endCalendar = stringToCalendar("yyyy/MM/dd HH:mm:ss", endTime);
    int diffMonth = getMonthDifference(startCalendar, endCalendar);
    if (diffMonth == 0) {
      int diffDay = getDayDifference(startCalendar, endCalendar);
      if (diffDay == 0) {
        timeDiff = getTwoTimeDiff(startTime, endTime);
      } else {
        timeDiff = diffDay + "天前";
      }
    } else {
      //if (diffMonth == 1) {
      //  timeDiff = "上个月";
      //} else {
      timeDiff = diffMonth + "个月前";
      //}
    }
    return timeDiff;
  }

  /**
   * 获取月份差
   */
  public static int getMonthDifference(Calendar startCalendar, Calendar endCalendar) {
    int diffMonth = 0;
    if (startCalendar.after(endCalendar)) {
      return diffMonth;
    } else {
      diffMonth = endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
      if (startCalendar.get(Calendar.YEAR) == endCalendar.get(Calendar.YEAR)) {
        return diffMonth;
      } else {
        int diffYear = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
        diffMonth = diffYear * 12 + diffMonth;
      }
    }
    return diffMonth;
  }

  /**
   * 获取天数差
   */
  public static int getDayDifference(Calendar startCalendar, Calendar endCalendar) {
    return endCalendar.get(Calendar.DAY_OF_MONTH) - startCalendar.get(Calendar.DAY_OF_MONTH);
  }

  /**
   * 判断是否同一天
   */
  public static boolean isToday(String rightDate) {
    Calendar todayCalendar = Calendar.getInstance();
    Calendar rightCalendar = Calendar.getInstance();
    try {
      Date date = stringToDate("yyyy/MM/dd", rightDate);
      rightCalendar.setTime(date);
      return todayCalendar.get(Calendar.YEAR) == rightCalendar.get(Calendar.YEAR)
          && todayCalendar.get(Calendar.MONTH) == rightCalendar.get(Calendar.MONTH)
          && todayCalendar.get(Calendar.DAY_OF_MONTH) == rightCalendar.get(Calendar.DAY_OF_MONTH);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return false;
  }

  /**
   * 将Calendar转换成指定格式String
   */
  public static String calendarToString(String pattern, Calendar calendar) {
    return new SimpleDateFormat(pattern, Locale.getDefault()).format(calendar.getTime());
  }

  /**
   * 根据给定格式返回date
   */
  public static String formatDate(Date date, String pattern) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern, Locale.CHINESE);
    simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
    return simpleDateFormat.format(date);
  }

  /**
   * String 转Date
   */
  public static Date stringToDate(String pattern, String date) throws ParseException {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern, Locale.CHINESE);
    simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
    return simpleDateFormat.parse(date);
  }

  public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";

  /**
   * string转Calendar
   */
  public static Calendar stringToCalendar(String pattern, String date) {
    Calendar calendar = Calendar.getInstance();
    try {
      calendar.setTime(stringToDate(pattern, date));
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return calendar;
  }

  /**
   * 根据年月日获取一个日历对象
   */
  public static Calendar getCalendar(int year, int month, int day) {
    Calendar calendar = Calendar.getInstance();
    calendar.set(year, month - 1, day);
    return calendar;
  }
}