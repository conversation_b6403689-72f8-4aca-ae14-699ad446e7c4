package com.bxkj.common.widget.filterpopup;

import android.view.View;
import android.widget.TextView;

import com.bxkj.common.R;
import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;

/**
 * @date 2018/4/3
 */

public class FilterGroupTitleViewBinder implements ItemViewBinder<FilterGroupTitle> {
    @Override
    public void onBindViewHolder(SuperViewHolder holder, FilterGroupTitle item, int position) {
        holder.setText(R.id.tv_more_requirements_title, item.getGroupTitle());
        TextView tvMoreTitle = holder.findViewById(R.id.tv_more);
        if (!CheckUtils.isNullOrEmpty(item.getMoreTitle())) {
            tvMoreTitle.setVisibility(View.VISIBLE);
            tvMoreTitle.setText(item.getMoreTitle());
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.common_recycler_filter_group_title;
    }
}
