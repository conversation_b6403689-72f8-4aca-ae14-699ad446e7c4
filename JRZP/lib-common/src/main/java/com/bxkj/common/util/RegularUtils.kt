package com.bxkj.common.util

import java.util.regex.Pattern

/**
 * @Description:
 * @date 2018/4/19
 */
object RegularUtils {

  //数字正则
  private val PATTERN_IS_NUMERIC: Pattern = Pattern.compile("^[-\\+]?[\\d]*$")

  //邮箱正则
  private val PATTERN_EMAIL: Pattern = Pattern.compile(
    "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?"
  )

  //手机号正则
  private const val PATTERN_MOBILE = "1[3-9]\\d{9}"

  //QQ号正则
  private const val PATTERN_QQ = "[1-9][0-9]{4,}"

  private const val URL = """(https?|ftp|file)://[-\w+&@#/%?=~|!:,.;]+[\w+&@#/%=~|]"""

  private val PATTERN_CONTRACT = Regex("($PATTERN_MOBILE)|($PATTERN_QQ)|($URL)")

  /**
   * 匹配手机号、QQ号、微信号
   */
  fun containsContact(text: String): Boolean {
    return PATTERN_CONTRACT.containsMatchIn(text)
  }

  /**
   * 匹配密码
   */
  @JvmStatic fun isPassword(password: String?): Boolean {
    val regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[\\w\\W]{8,16}\$"
    return Pattern.matches(regex, password)
  }

  /**
   * 匹配邮箱
   */
  @JvmStatic fun isEmail(email: String): Boolean {
    return PATTERN_EMAIL.matcher(email.trim { it <= ' ' }).matches()
  }

  /**
   * 检查手机号
   */
  @JvmStatic fun isMobile(mobile: String?): Boolean {
    return mobile?.let { PATTERN_MOBILE.toRegex().containsMatchIn(mobile) } == true
  }

  /**
   * 检查电话号
   */
  fun isPhone(phone: String?): Boolean {
    val regex = "^((0\\d{2,3}(-|_|—))|(0\\d{2,3}))(\\d{7,8})((-|_|—)\\d{3,})?$"
    return Pattern.matches(regex, phone)
  }

  fun isNumeric(str: String?): Boolean {
    return PATTERN_IS_NUMERIC.matcher(str).matches()
  }
}
