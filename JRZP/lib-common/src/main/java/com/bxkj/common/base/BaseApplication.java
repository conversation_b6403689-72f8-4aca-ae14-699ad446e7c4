package com.bxkj.common.base;

import android.app.Activity;
import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;
import com.bxkj.common.R;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.refreshlayoutmanger.JRZPRefreshHeader;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import dagger.android.AndroidInjector;
import dagger.android.support.DaggerApplication;
import java.lang.ref.WeakReference;

/**
 * @date 2018/3/20
 */

public abstract class BaseApplication extends DaggerApplication {

  private WeakReference<FragmentActivity> mCurrentActivity;

  @Override
  public void onCreate() {
    super.onCreate();
    initBackgroundExceptionHandle();
  }

  /**
   * 处理后台异常
   */
  private void initBackgroundExceptionHandle() {
    registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
      @Override
      public void onActivityCreated(Activity activity, Bundle bundle) {

      }

      @Override
      public void onActivityStarted(Activity activity) {
      }

      @Override
      public void onActivityResumed(Activity activity) {
        if (activity instanceof FragmentActivity) {
          mCurrentActivity = new WeakReference<>((FragmentActivity) activity);
        }
      }

      @Override
      public void onActivityPaused(Activity activity) {

      }

      @Override
      public void onActivityStopped(Activity activity) {
      }

      @Override
      public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {

      }

      @Override
      public void onActivityDestroyed(Activity activity) {

      }
    });
  }

  public WeakReference<FragmentActivity> getCurrentActivity() {
    return mCurrentActivity;
  }

  static {
    //设置全局的Header构建器
    SmartRefreshLayout.setDefaultRefreshHeaderCreator((context, layout) -> {
      layout.setPrimaryColorsId(R.color.common_colorPrimary, android.R.color.white);//全局设置主题颜色
      return new JRZPRefreshHeader(context);
    });
    //设置全局的Footer构建器
    SmartRefreshLayout.setDefaultRefreshFooterCreator((context, layout) -> {
      //指定为经典Footer，默认是 BallPulseFooter
      return new ClassicsFooter(context).setDrawableSize(30);
    });
  }

  public static int getZPUserId() {
    return UserUtils.getUserId();
  }

  @Override
  protected abstract AndroidInjector<? extends DaggerApplication> applicationInjector();

}
