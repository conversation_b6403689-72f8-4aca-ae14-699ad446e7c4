package com.bxkj.common.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.BR;

import com.bxkj.common.widget.filterpopup.FilterOption;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.data
 * @Description: 职位分类信息
 * @TODO: TODO
 * @date 2018/3/29
 */

public class JobTypeData extends BaseObservable implements Parcelable , FilterOption {

  /**
   * id : 19 name : 计算机、互联网类
   */

  @SerializedName(value = "jobtype", alternate = {"pid"})
  private int pid;
  @SerializedName(value = "typeName", alternate = {"pname"})
  private String pname;
  @SerializedName(value = "jobid", alternate = {"id"})
  private int id;
  @SerializedName(value = "jobName", alternate = {"name"})
  private String name;
  private boolean selected;
  private List<JobTypeData> jobTypeDataList;

  public JobTypeData(List<JobTypeData> jobTypeDataList) {
    this.jobTypeDataList = jobTypeDataList;
  }

  public JobTypeData(int id, String name) {
    this.id = id;
    this.name = name;
  }

  @Bindable
  public boolean isSelected() {
    return selected;
  }

  public void updateSelectedState(boolean selected) {
    if (this.selected == selected) {
      return;
    }
    this.selected = selected;
    notifyPropertyChanged(BR.selected);
  }

  protected JobTypeData(Parcel in) {
    pid = in.readInt();
    pname = in.readString();
    id = in.readInt();
    name = in.readString();
    jobTypeDataList = in.createTypedArrayList(JobTypeData.CREATOR);
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(pid);
    dest.writeString(pname);
    dest.writeInt(id);
    dest.writeString(name);
    dest.writeTypedList(jobTypeDataList);
  }

  @Override
  public int describeContents() {
    return 0;
  }

  public static final Creator<JobTypeData> CREATOR = new Creator<JobTypeData>() {
    @Override
    public JobTypeData createFromParcel(Parcel in) {
      return new JobTypeData(in);
    }

    @Override
    public JobTypeData[] newArray(int size) {
      return new JobTypeData[size];
    }
  };

  public int getPid() {
    return pid;
  }

  public void setPid(int pid) {
    this.pid = pid;
  }

  public String getPname() {
    return pname;
  }

  public void setPname(String pname) {
    this.pname = pname;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<JobTypeData> getJobClassDataList() {
    return jobTypeDataList;
  }

  public void setJobClassDataList(List<JobTypeData> jobTypeDataList) {
    this.jobTypeDataList = jobTypeDataList;
  }

  public String getFullTypeText() {
    return pname + "-" + name;
  }

  public static class DiffCallbak extends DiffUtil.ItemCallback<JobTypeData> {

    @Override
    public boolean areItemsTheSame(@NonNull JobTypeData oldItem, @NonNull JobTypeData newItem) {
      return oldItem.equals(newItem);
    }

    @Override
    public boolean areContentsTheSame(@NonNull JobTypeData oldItem, @NonNull JobTypeData newItem) {
      return oldItem.id == newItem.id && oldItem.pid == newItem.pid;
    }
  }
}
