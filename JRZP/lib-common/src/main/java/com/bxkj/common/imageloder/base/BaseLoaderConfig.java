package com.bxkj.common.imageloder.base;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib.imageloder
 * @Description: 图片加载参数类
 * @TODO: TODO
 * @date 2018/3/20
 */

public class BaseLoaderConfig {

  protected Object url;    //要加载的路径
  protected int placeHolder;    //加载时的占位图
  protected Drawable placeholderDrawable;
  protected int errorImg;   //加载失败显示的图片
  protected Drawable errorImgDrawable;
  protected ImageView imageView;    //需要加载的目标imageview
  protected boolean skipCache;
  protected ImageView.ScaleType scaleType;

  public Object getUrl() {
    return url;
  }

  public void setUrl(Object url) {
    this.url = url;
  }

  public int getPlaceHolder() {
    return placeHolder;
  }

  public void setPlaceHolder(int placeHolder) {
    this.placeHolder = placeHolder;
  }

  public int getErrorImg() {
    return errorImg;
  }

  public void setErrorImg(int errorImg) {
    this.errorImg = errorImg;
  }

  public ImageView getImageView() {
    return imageView;
  }

  public void setImageView(ImageView imageView) {
    this.imageView = imageView;
  }

  public boolean isSkipCache() {
    return skipCache;
  }

  public void setSkipCache(boolean skipCache) {
    this.skipCache = skipCache;
  }

  public ImageView.ScaleType getScaleType() {
    return scaleType;
  }

  public void setScaleType(ImageView.ScaleType scaleType) {
    this.scaleType = scaleType;
  }

  public Drawable getPlaceholderDrawable() {
    return placeholderDrawable;
  }

  public void setPlaceholderDrawable(Drawable placeholderDrawable) {
    this.placeholderDrawable = placeholderDrawable;
  }

  public Drawable getErrorImgDrawable() {
    return errorImgDrawable;
  }

  public void setErrorImgDrawable(Drawable errorImgDrawable) {
    this.errorImgDrawable = errorImgDrawable;
  }
}
