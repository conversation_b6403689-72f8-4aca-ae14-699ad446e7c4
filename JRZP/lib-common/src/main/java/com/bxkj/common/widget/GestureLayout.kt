package com.bxkj.common.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.TimeInterpolator
import android.content.Context
import android.os.SystemClock
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import com.bxkj.common.R
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.DensityUtils
import kotlin.math.abs
import kotlin.random.Random

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/11
 * @version: V1.0
 */
class GestureLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

  private var mOnGestureListener: OnGestureListener? = null
  private var mGestureDetector: GestureDetector? = null
  private var mFlipDistance = AppConstants.SCREEN_WIDTH / 5

  companion object {
    const val OP_LEFT_SCROLL = 1
    const val DEFAULT_FLIP_DISTANCE = 80

    val rotates = floatArrayOf(-25f, -15f, 0f, 15f, 25f)
  }

  init {
    isFocusable = true
    isClickable = true
    val flipDistance = AppConstants.SCREEN_WIDTH / 5
    mFlipDistance = if (flipDistance == 0) DEFAULT_FLIP_DISTANCE else flipDistance
    setupGestureDetector()
  }

  private var lastClickTime = 0L

  private fun setupGestureDetector() {

    mGestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {

      override fun onDown(e: MotionEvent): Boolean {
        val systemTime = SystemClock.uptimeMillis()
        if (systemTime - lastClickTime < 400) {
          setupAnimView(e)
        }
        lastClickTime = SystemClock.uptimeMillis()
        return true
      }

      override fun onScroll(
        e1: MotionEvent?,
        e2: MotionEvent,
        distanceX: Float,
        distanceY: Float
      ): Boolean {
        e1?.let {
          Log.d("GestureLayout:onScroll", "按下点：${e1.x}*${e1.y}---抬起点：${e2.x}*${e2.y}")
          if (e1.x - e2.x > 40 && abs(e1.x - e2.x) / abs(e1.y - e2.y) > 2) {
            parent.requestDisallowInterceptTouchEvent(true)
          }
        }
        return false;
      }

      override fun onFling(
        e1: MotionEvent?,
        e2: MotionEvent,
        velocityX: Float,
        velocityY: Float
      ): Boolean {
        e1?.let {
          Log.d("GestureLayout:onScroll", "按下点：${e1.x}*${e1.y}---抬起点：${e2.x}*${e2.y}")
          if (e1.x - e2.x > mFlipDistance && abs(e1.x - e2.x) / abs(e1.y - e2.y) > 2) {
            parent.requestDisallowInterceptTouchEvent(true)
            return mOnGestureListener?.onGesture(OP_LEFT_SCROLL) ?: super.onScroll(
              e1,
              e2,
              velocityX,
              velocityY
            )
          }
        }
        return false;
      }

      override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        return mOnGestureListener?.onSingleTapConfirm() ?: super.onSingleTapConfirmed(e)
      }

      override fun onDoubleTap(e: MotionEvent): Boolean {
        return mOnGestureListener?.onDoubleTap() ?: super.onDoubleTap(e)
      }
    })
  }

  private fun setupAnimView(event: MotionEvent) {

    val animViewSize = DensityUtils.dp2px(context, 80f)

    //创建动画view
    val animImageView = ImageView(context)
    val animLayoutParams =
      LayoutParams(animViewSize, animViewSize)

    animLayoutParams.leftMargin = (event.x - animViewSize / 2).toInt()
    animLayoutParams.topMargin = (event.y - animViewSize * 1.5).toInt()

    animImageView.setImageResource(R.drawable.ic_video_liked)
    animImageView.layoutParams = animLayoutParams

    addView(animImageView)

    AnimatorSet().apply {
      play(scaleAni(animImageView, "scaleX", 2f, 0.9f, 100, 0))
        .with(scaleAni(animImageView, "scaleY", 2f, 0.9f, 100, 0))
        .with(rotation(animImageView, 0, 0, rotates[Random.nextInt(4)]))
        .with(alphaAni(animImageView, 0F, 1F, 100, 0))
        .with(scaleAni(animImageView, "scaleX", 0.9f, 1F, 50, 150))
        .with(scaleAni(animImageView, "scaleY", 0.9f, 1F, 50, 150))
        .with(translationY(animImageView, 0f, -600F, 800, 400))
        .with(alphaAni(animImageView, 1F, 0F, 300, 400))
        .with(scaleAni(animImageView, "scaleX", 1F, 3f, 700, 400))
        .with(scaleAni(animImageView, "scaleY", 1F, 3f, 700, 400))
      addListener(object : AnimatorListenerAdapter() {
        override fun onAnimationEnd(animation: Animator) {
          super.onAnimationEnd(animation)
          removeViewInLayout(animImageView)
        }
      })
    }.start()
  }

  //vararg可变参数修饰符，此处可以传入多个Float类型值
  fun rotation(view: View, time: Long, delayTime: Long, vararg values: Float): ObjectAnimator {
    val ani = ObjectAnimator.ofFloat(view, "rotation", *values)
    ani.duration = time
    ani.startDelay = delayTime
    ani.interpolator = TimeInterpolator { input -> input }
    return ani
  }

  private fun alphaAni(view: View, from: Float, to: Float, time: Long, delayTime: Long): ObjectAnimator {
    val ani = ObjectAnimator.ofFloat(view, "alpha", from, to)
    ani.interpolator = LinearInterpolator()
    ani.duration = time
    ani.startDelay = delayTime
    return ani
  }

  fun translationY(
    view: View,
    from: Float,
    to: Float,
    time: Long,
    delayTime: Long
  ): ObjectAnimator {
    val ani = ObjectAnimator.ofFloat(view, "translationY", from, to)
    ani.interpolator = LinearInterpolator()
    ani.startDelay = delayTime
    ani.duration = time
    return ani
  }

  fun translationX(
    view: View,
    from: Float,
    time: Long,
    to: Float,
    delayTime: Long
  ): ObjectAnimator {
    val ani = ObjectAnimator.ofFloat(view, "translationX", from, to)
    ani.startDelay = delayTime
    ani.duration = time
    ani.interpolator = LinearInterpolator()
    return ani
  }

  fun scaleAni(
    view: View,
    propertyName: String,
    from: Float,
    to: Float,
    time: Long,
    delayTime: Long
  ): ObjectAnimator {
    val ani = ObjectAnimator.ofFloat(view, propertyName, from, to)
    ani.interpolator = LinearInterpolator()
    ani.startDelay = delayTime
    ani.duration = time
    return ani
  }

  override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
    return true
  }

  override fun onTouchEvent(event: MotionEvent): Boolean {
    return mGestureDetector?.onTouchEvent(event) ?: super.onTouchEvent(event)
  }

  abstract class SimpleOnGestureListener : OnGestureListener {
    override fun onGesture(gesture: Int): Boolean {
      return false
    }

    override fun onDoubleTap(): Boolean {
      return false
    }

    override fun onSingleTapConfirm(): Boolean {
      return false
    }
  }

  interface OnGestureListener {

    fun onGesture(gesture: Int): Boolean

    fun onDoubleTap(): Boolean

    fun onSingleTapConfirm(): Boolean
  }

  public fun setOnGestureListener(onGestureListener: OnGestureListener) {
    mOnGestureListener = onGestureListener
  }
}