package com.bxkj.common.util.location

import android.content.Context
import android.os.Looper
import androidx.fragment.app.FragmentActivity
import com.bxkj.common.util.PermissionUtils
import com.elvishew.xlog.XLog
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.Toaster
import com.kwad.sdk.collector.CollectResponse
import org.gavaghan.geodesy.Ellipsoid
import org.gavaghan.geodesy.GeodeticCalculator
import org.gavaghan.geodesy.GlobalCoordinates

/**
 * @Description: 定位管理类
 * @date 2019/11/29
 * @version V1.0
 */
object LocationManager {

  fun getDistance(lng1: Double, lat1: Double, lng2: Double, lat2: Double): Double {
    val convertLatLng1 = CoordinateTransformUtil.wgs84tobd09(lng1, lat1)
    val convertLatLng2 = CoordinateTransformUtil.wgs84tobd09(lng2, lat2)
    return GeodeticCalculator().calculateGeodeticCurve(
      Ellipsoid.WGS84,
      GlobalCoordinates(convertLatLng1[1], convertLatLng1[0]),
      GlobalCoordinates(convertLatLng2[1], convertLatLng2[0])
    ).ellipsoidalDistance
  }

  fun getLocationInfo(
    context: Context,
    onLocationListener: OnLocationListener
  ) {
    XXPermissions.with(context)
      .permission(Permission.ACCESS_FINE_LOCATION)
      .request(object : OnPermissionCallback {
        override fun onGranted(permissions: MutableList<String>, all: Boolean) {
          requestLocation(context, onLocationListener)
        }

        override fun onDenied(permissions: MutableList<String>, never: Boolean) {
          onLocationListener.onFailed()
        }
      })
  }

  @JvmOverloads
  fun requestLocation(
    context: Context,
    onLocationListener: OnLocationListener,
    geocoding: Boolean = false
  ) {
      GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(context).let {
        if (it == ConnectionResult.SUCCESS) {
          XLog.d("google play services available")
          val locationRequest = LocationRequest().apply {
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            numUpdates = 1
          }

          LocationServices.getSettingsClient(context)
            .checkLocationSettings(
              LocationSettingsRequest.Builder().addLocationRequest(locationRequest).build()
            )
            .addOnSuccessListener { locationSettingsResponse ->
              if (locationSettingsResponse.locationSettingsStates?.isLocationUsable == true) {
                LocationServices.getFusedLocationProviderClient(context)
                  .requestLocationUpdates(locationRequest, object : LocationCallback() {
                    override fun onLocationResult(p0: LocationResult) {
                      p0.lastLocation?.let { location ->
                        val convertLocationArray =
                          CoordinateTransformUtil.wgs84tobd09(location.longitude, location.latitude)
                        XLog.d("convertLocationArray:${convertLocationArray.contentToString()}")
                        val zpLocation = ZPLocation(convertLocationArray[1], convertLocationArray[0]);
                        onLocationListener.onSuccess(zpLocation)
                      } ?: let { onLocationListener.onFailed() }
                    }
                  }, Looper.getMainLooper())
              } else {
                onLocationListener.onFailed()
              }
            }.addOnFailureListener {exp->
              XLog.d("error: ${exp.message}")
              Toaster.show("未开启定位服务，请开启定位服务后重试")
              onLocationListener.onFailed()
            }
        }else{
          XLog.d("google play services not available")
        }
      }
  }

  @JvmOverloads
  fun getLocationInfo(
    activity: FragmentActivity,
    tipsTitle: String,
    tipsContent: String,
    onLocationListener: OnLocationListener,
    vararg permission: String = arrayOf(
      Permission.ACCESS_FINE_LOCATION,
      Permission.ACCESS_COARSE_LOCATION
    ),
  ) {
    PermissionUtils.requestPermission(
      activity,
      tipsTitle,
      tipsContent,
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          requestLocation(activity, onLocationListener)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          onLocationListener.onFailed()
        }
      },
      *permission
    )
  }

  interface OnLocationListener {
    fun onSuccess(location: ZPLocation)
    fun onFailed()
  }
}