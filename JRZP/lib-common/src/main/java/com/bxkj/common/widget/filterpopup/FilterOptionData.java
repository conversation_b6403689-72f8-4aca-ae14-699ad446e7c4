package com.bxkj.common.widget.filterpopup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import com.bxkj.common.widget.popup.IWheelOptions;
import com.contrarywind.interfaces.IPickerViewData;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 公司性质
 * @TODO: TODO
 * @date 2018/4/2
 */

public class FilterOptionData implements IPickerViewData, FilterOption, IWheelOptions {

  public static final int NATURE_OF_COMPANY = 0x01;

  public static final int WORKING_EXP = 0x02;

  public static final int EDU = 0x03;

  public static final int PUBLISH_DATE = 0x04;

  public static final int WORK_NATURE = 0x05;

  public static final int POSITION_TAG = 0x06;

  public static final int SALARY = 0x07;

  public static final int NEWS_HOT_CITY = 0x05;

  public static final int NEWS_TYPE = 0x06;

  public static final int GENDER = 0x07;

  public static final int SPECIAL_REQ = 0x08;

  private int id;

  @SerializedName(alternate = { "EditTime" }, value = "name")
  private String name;

  private int type;

  private List<FilterOptionData> mFilterOptionDataList;

  private int areaType;

  //记录是否是更多
  private boolean isMore = false;

  public FilterOptionData(List<FilterOptionData> filterOptionDataList, int type) {
    this.mFilterOptionDataList = filterOptionDataList;
    this.type = type;
  }

  public FilterOptionData(int id, String name) {
    this.id = id;
    this.name = name;
  }

  public FilterOptionData(int id, String name, boolean isMore) {
    this.id = id;
    this.name = name;
    this.isMore = isMore;
  }

  public int getAreaType() {
    return areaType;
  }

  public FilterOptionData setAreaType(int areaType) {
    this.areaType = areaType;
    return this;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<FilterOptionData> getFilterOptionsDataList() {
    return mFilterOptionDataList;
  }

  public int getType() {
    return type;
  }

  public boolean isMore() {
    return isMore;
  }

  public void setMore(boolean more) {
    isMore = more;
  }

  @Override
  public String getPickerViewText() {
    return name;
  }

  @Override
  public String getItemOption() {
    return name;
  }

  public static class DiffCallback extends DiffUtil.ItemCallback<FilterOptionData> {

    @Override
    public boolean areItemsTheSame(@NonNull final FilterOptionData oldItem,
      @NonNull final FilterOptionData newItem) {
      return oldItem.id == newItem.id;
    }

    @Override
    public boolean areContentsTheSame(@NonNull final FilterOptionData oldItem,
      @NonNull final FilterOptionData newItem) {
      return oldItem.id == newItem.id && oldItem.name.equals(newItem.name);
    }
  }
}
