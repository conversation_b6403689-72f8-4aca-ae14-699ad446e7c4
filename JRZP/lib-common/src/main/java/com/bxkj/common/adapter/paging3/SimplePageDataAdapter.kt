package com.bxkj.common.adapter.paging3

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.BR
import com.bxkj.common.adapter.superadapter.SuperItemClickListener

/**
 *
 * @author: sanjin
 * @date: 2022/8/25
 */
open class SimplePageDataAdapter<T : Any, VB : ViewDataBinding> constructor(
    private val layoutId: Int,
    diffCallback: DiffUtil.ItemCallback<T>,
    private val dataId: Int? = BR.data
) : PagingDataAdapter<T, PagingViewHolder<VB>>(diffCallback) {

    private var _superItemClickListener: SuperItemClickListener? = null
    private var _clickViewIds: IntArray? = null

    override fun onBindViewHolder(holder: PagingViewHolder<VB>, position: Int) {
        dataId?.let {
            holder.viewBinding.setVariable(it, getItem(position))
            holder.viewBinding.executePendingBindings()
        }
        holder.itemView.setOnClickListener {
            _superItemClickListener?.onClick(it, position)
        }
        _clickViewIds?.let {
            it.forEach { itemId ->
                holder.itemView.findViewById<View>(itemId).setOnClickListener { view ->
                    _superItemClickListener?.onClick(view, position)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PagingViewHolder<VB> {
        return PagingViewHolder(
            DataBindingUtil.inflate(
                LayoutInflater.from(parent.context),
                layoutId,
                parent,
                false
            )
        )
    }

    fun setOnItemClickListener(superItemClickListener: SuperItemClickListener, vararg clickViewIds: Int) {
        _superItemClickListener = superItemClickListener
        _clickViewIds = clickViewIds
    }

    fun getItemBean(position: Int): T? {
        return getItem(position)
    }

}