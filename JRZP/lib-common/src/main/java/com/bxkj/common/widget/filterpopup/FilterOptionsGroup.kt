package com.bxkj.common.widget.filterpopup

/**
 * @Description: 筛选选项容器
 * @date 2018/5/26
 */
class FilterOptionsGroup<T : FilterOption> @JvmOverloads constructor(
    @JvmField var tag: Int,   //分组标记
    @JvmField var filterOptionList: List<T?>,  //分组数据
    @JvmField var isFlow: Boolean = false, //是否流式布局
    @JvmField var spanCount: Int = 4, //列数
    @JvmField var openMultiSelect: Boolean = false, //是否开启多选
    @JvmField var onMultiSelectListener: OnMultiSelectListener<T>? = null
) {

    interface OnMultiSelectListener<T:FilterOption> {

        fun onMultiSelect(filterOption: List<T>)
    }
}