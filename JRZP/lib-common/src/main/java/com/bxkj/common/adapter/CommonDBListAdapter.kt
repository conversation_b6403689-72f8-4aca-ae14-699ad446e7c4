package com.bxkj.common.adapter

import android.content.Context
import com.bxkj.common.BR
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder

/**
 * @date: 2020/7/19
 * @version: V1.0
 */
open class CommonDBListAdapter<T>(context: Context, layoutID: Int, private var dataID: Int = BR.data) :
  SuperAdapter<T>(context, layoutID) {

  override fun convert(holder: SuperViewHolder, viewType: Int, item: T, position: Int) {
    holder.bind(dataID, item)
  }
}