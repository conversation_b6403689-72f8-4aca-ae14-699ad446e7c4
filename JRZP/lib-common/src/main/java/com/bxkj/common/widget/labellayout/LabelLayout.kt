package com.bxkj.common.widget.labellayout

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.use
import com.bxkj.common.R

/**
 * @description: 标签布局
 * @author: sanjin
 * @date: 2022/7/30
 */
class LabelLayout @JvmOverloads constructor(
  context: Context,
  attributeSet: AttributeSet? = null,
  defStyle: Int = 0
) : LinearLayout(context, attributeSet, defStyle) {

  private var itemTextSize: Int = 0
  private var itemTextColor: Int = 0
  private var itemBackground: Drawable? = null
  private var itemMargin: Int = 0
  private var itemPaddingTB: Int = 0
  private var itemPaddingLR: Int = 0
  private var maxShowTab: Int = Int.MAX_VALUE

  private val cacheItemViews = mutableListOf<TextView>()

  init {
    orientation = HORIZONTAL
    val array =
      context.obtainStyledAttributes(
        attributeSet,
        R.styleable.LabelLayout,
        defStyle,
        0
      )
    itemTextSize =
      array.getDimensionPixelOffset(R.styleable.LabelLayout_itemTextSize, 0)
    itemTextColor =
      array.getColor(
        R.styleable.LabelLayout_itemTextColor,
        ContextCompat.getColor(context, R.color.cl_333333)
      )
    itemMargin = array.getDimensionPixelOffset(R.styleable.LabelLayout_itemMargin, 0)
    itemPaddingTB =
      array.getDimensionPixelOffset(R.styleable.LabelLayout_itemPaddingTB, 0)
    itemPaddingLR =
      array.getDimensionPixelOffset(R.styleable.LabelLayout_itemPaddingLR, 0)
    itemBackground = array.getDrawable(R.styleable.LabelLayout_itemBackground)
    maxShowTab = array.getInt(R.styleable.LabelLayout_maxShowTab, Int.MAX_VALUE)
    array.recycle()
  }

  fun setItems(items: Array<String>?) {
    if (items.isNullOrEmpty()) {
      removeAllViews()
      cacheItemViews.clear()
      return
    }

    val itemCount = minOf(items.size, maxShowTab)

    while (cacheItemViews.size > itemCount) {
      removeView(cacheItemViews.removeAt(cacheItemViews.size - 1))
    }

    for (i in 0 until itemCount) {
      if (i < cacheItemViews.size) {
        cacheItemViews[i].text = items[i]
      } else {
        val newView = createTabView(items[i], i)
        addView(newView)
        cacheItemViews.add(newView)
      }
    }
  }

  private fun createTabView(tab: String, index: Int): TextView {
    val item = TextView(context).apply {
      id = index
      val itemLayoutParams = MarginLayoutParams(
        MarginLayoutParams.WRAP_CONTENT,
        MarginLayoutParams.WRAP_CONTENT
      )
      if (index > 0) {
        itemLayoutParams.leftMargin = itemMargin
      }
      layoutParams = itemLayoutParams
      maxLines = 1
      ellipsize = TextUtils.TruncateAt.END
      setTextColor(itemTextColor)
      setTextSize(TypedValue.COMPLEX_UNIT_PX, itemTextSize.toFloat())
      setPadding(itemPaddingLR, itemPaddingTB, itemPaddingLR, itemPaddingTB)
      text = tab
      background = itemBackground?.constantState?.newDrawable()
    }
    return item
  }
}