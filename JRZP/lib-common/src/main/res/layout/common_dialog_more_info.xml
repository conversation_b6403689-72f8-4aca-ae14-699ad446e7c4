<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_match"
  android:background="@drawable/common_bg_bottom_sheet"
  android:orientation="vertical">

  <FrameLayout
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.17sp.000000.Bold"
      android:layout_gravity="center" />

    <ImageView
      android:id="@+id/iv_close"
      style="@style/wrap_wrap"
      android:layout_gravity="end|center_vertical"
      android:layout_marginEnd="4dp"
      android:src="@drawable/common_ic_close" />
  </FrameLayout>

  <androidx.core.widget.NestedScrollView style="@style/match_match">

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.16sp.333333"
      android:layout_width="match_parent"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:layout_marginBottom="@dimen/dp_8"
      android:lineSpacingMultiplier="1.2" />
  </androidx.core.widget.NestedScrollView>
</LinearLayout>