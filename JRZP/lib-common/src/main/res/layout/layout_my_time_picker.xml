<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="240dp"
    android:background="@drawable/bg_white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_options_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_20"
            android:text="@string/common_cancel"
            android:textColor="@color/common_888888"
            android:textSize="@dimen/dp_16" />

        <Space
            android:layout_width="@dimen/dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_now"
            style="@style/wrap_wrap"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/up_to_now"
            android:textColor="@color/cl_ff7405"
            android:textSize="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_finish"
            style="@style/wrap_wrap"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/common_complete"
            android:textColor="@color/cl_ff7405"
            android:textSize="@dimen/dp_16" />
    </LinearLayout>

    <!--此部分需要完整复制过去，删减或者更改ID会导致初始化找不到内容而报空-->
    <LinearLayout
        android:id="@+id/timepicker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <com.contrarywind.view.WheelView
            android:id="@+id/year"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.contrarywind.view.WheelView

            android:id="@+id/month"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/day"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/hour"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/min"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/second"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />
    </LinearLayout>

</LinearLayout>