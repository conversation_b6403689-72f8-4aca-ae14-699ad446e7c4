<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/cl_parent"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:background="@drawable/bg_ffffff"
  android:orientation="horizontal"
  android:paddingStart="@dimen/dp_12"
  android:paddingTop="@dimen/dp_8"
  android:paddingBottom="@dimen/dp_8">

  <EditText
    android:id="@+id/et_content"
    android:layout_width="0dp"
    android:layout_height="@dimen/dp_40"
    android:layout_marginEnd="@dimen/dp_12"
    android:background="@drawable/bg_f4f4f4_radius_2"
    android:paddingStart="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_10"
    android:textColor="@color/cl_333333"
    android:textColorHint="@color/common_b5b5b5"
    android:textSize="@dimen/sp_14"
    app:layout_constraintEnd_toStartOf="@id/tv_send"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_send"
    style="@style/Text.14sp.FFFFFF"
    android:layout_height="match_parent"
    android:layout_marginEnd="@dimen/dp_12"
    android:background="@drawable/common_bg_basic_btn_selector"
    android:gravity="center"
    android:paddingStart="@dimen/dp_12"
    android:paddingTop="@dimen/dp_6"
    android:paddingEnd="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_6"
    android:text="@string/send"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>