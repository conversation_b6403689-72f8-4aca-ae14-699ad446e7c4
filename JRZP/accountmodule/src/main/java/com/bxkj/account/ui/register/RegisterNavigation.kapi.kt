package com.bxkj.account.ui.register

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

/**
 * Description:
 * Author:Sanjin
 **/
class RegisterNavigation {

    companion object {

        const val PATH = "${RouterNavigation.ACCOUNT_URL }/register"

        const val EXTRA_REGISTER_IDENTITY = "REGISTER_IDENTITY"

        fun create(authType: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_REGISTER_IDENTITY, authType)
        }
    }
}