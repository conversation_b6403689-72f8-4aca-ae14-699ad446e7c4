package com.bxkj.account;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.testlib
 * @Description: 账户包基类
 * @date 2018/3/21
 */

public class AccountApplication{

//    @Override
//    protected AndroidInjector<? extends DaggerApplication> applicationInjector() {
//        return DaggerAccountAppComponent.builder().application(this).applicationModule(new ApplicationModule(this)).build();
//    }
}
