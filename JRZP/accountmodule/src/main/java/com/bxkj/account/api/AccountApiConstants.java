package com.bxkj.account.api;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.contants
 * @Description: 接口常量
 * @date 2018/3/27
 */

public class AccountApiConstants {

  //用户登录
  public static final String I_LOGIN = "/User/UserLogin/";

  //手机号登录
  public static final String I_LOGIN_FOR_MOBILE = "/User/UserLoginByPhone/";

  //邮箱注册
  public static final String I_REGISTER_OF_EMAIL = "/User/UserReg/";

  //手机注册
  public static final String I_REGISTER_OF_MOBILE = "/User/UserMobileReg/";

  //检查手机号是否存在
  public static final String I_CHECK_THE_MOBILE_NUMBER_EXISTS = "/User/ExistUserMobile/";

  //发送短信验证码
  public static final String I_SEND_SMS_VERIFICATION_CODE = "/User/SendPhoneCodeWithKeyIv/";

  //获取职位分类list
  public static final String I_GET_JOB_CLASS_LIST = "/Job/GetJobList/";

  //添加用户期望的职位
  public static final String I_ADD_EXPECTED_JOBS = "/UserJob/UserJobAdd/";

  //根据职位名称查询职位
  public static final String I_SEARCH_JOB_CLASS_BY_NAME = "/Job/GetJobListByName/";

  //批量删除期望职位
  public static final String I_DELETE_USER_CHECKED_JOB = "/UserJob/UserJobDelete/";

  //发送邮件重置密码
  public static final String I_SEND_UPDATE_PASSWORD_EMAIL = "/User/SendResetPwdEmail/";

  //根据手机号修改密码
  public static final String I_UPDATE_PASSWORD_WITH_MOBILE = "/User/UpdatePwdByMobile/";

  //修改密碼
  public static final String I_UPDATE_PWD_WITH_OLD_PWD = "/User/UpdatePwd/";

  //检查简历总数
  public static final String I_GET_RESUME_COUNT = "/Resume/GetResumeList/";

  //绑定用户推送token
  public static final String I_BIND_USER_PUSH_TOKEN = "/PushToken/AddPushToken/";
}
