package com.bxkj.account.ui.register

import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import com.bxkj.accountmodule.R
import com.bxkj.accountmodule.databinding.AccountActivityRegisterV2Binding
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.ActivityRouterUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.LoginActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxBus.Message
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.therouter.router.Route

/**
 * Description: 注册
 * Author:Sanjin
 * Date:2024/7/17
 **/
@Route(path = RegisterNavigation.PATH)
class RegisterActivityV2 : BaseDBActivity<AccountActivityRegisterV2Binding, RegisterViewModel>() {

  private val intentAuthType by lazy {
    intent.getIntExtra(
      RegisterNavigation.EXTRA_REGISTER_IDENTITY,
      0
    )
  }

  override fun getViewModelClass(): Class<RegisterViewModel> = RegisterViewModel::class.java

  override fun getLayoutId(): Int = R.layout.account_activity_register_v2

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    viewModel.start(intentAuthType)

    setupPrivacyNoteClickListener()

    subscribeViewModelEvent()
  }

  private fun setupPrivacyNoteClickListener() {
    val spannableStringBuilder =
      SpannableStringBuilder(getString(R.string.account_login_privacy_note))
    val agreementClickSpan: ClickableSpan = object : ClickableSpan() {
      override fun onClick(widget: View) {
        ActivityRouterUtils.toAgreementActivity()
      }

      override fun updateDrawState(ds: TextPaint) {
        ds.color = getResColor(R.color.cl_ff7405)
        ds.isUnderlineText = false
      }
    }
    val privacyAgreementClickSpan: ClickableSpan = object : ClickableSpan() {
      override fun onClick(widget: View) {
        ActivityRouterUtils.toPrivacyActivity()
      }

      override fun updateDrawState(ds: TextPaint) {
        ds.color = getResColor(R.color.cl_ff7405)
        ds.isUnderlineText = false
      }
    }
    spannableStringBuilder.setSpan(
      agreementClickSpan,
      8,
      14,
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    spannableStringBuilder.setSpan(
      privacyAgreementClickSpan, 15, 21,
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    viewBinding.tvPrivacy.movementMethod = LinkMovementMethod.getInstance()
    viewBinding.tvPrivacy.text = spannableStringBuilder
  }

  private fun subscribeViewModelEvent() {
    viewModel.mobileNumberHasRegisteredEvent.observe(this, EventObserver {
      Router.getInstance()
        .to(LoginActivity.URL)
        .withString(LoginActivity.LOGIN_PHONE, it)
        .start()
    })

    viewModel.registerSuccessEvent.observe(this, EventObserver { userId ->
      showToast(getString(R.string.account_register_success))
      RxBus.get().post(Message(RxMsgCode.ACTION_FROM_REGISTER_USER, true))
      when (intentAuthType) {
        AuthenticationType.ENTERPRISE, AuthenticationType.INSTITUTIONS -> {
          UserUtils.saveUserData(userId, AuthenticationType.ENTERPRISE)
          BusinessBasicInfoNavigation.navigate(true, AuthenticationType.ENTERPRISE, false).start()
        }

        AuthenticationType.SCHOOL -> {
          UserUtils.saveUserData(userId, AuthenticationType.SCHOOL)
          SchoolInfoNavigation.navigate(SchoolInfoNavigation.NEXT_STEP_AUTH, false).start()
        }

        AuthenticationType.PERSONAL_RECRUITER -> {
          UserUtils.saveUserData(userId, AuthenticationType.PERSONAL_RECRUITER)
          IDCardValidationNavigation.create(IDCardValidationNavigation.NEXT_STEP_TO_MAIN).start()
        }

        else -> {
          UserUtils.saveUserData(userId, AuthenticationType.PERSONAL)
          MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME_FOR_REGISTER)
            .start()
        }
      }
      finish()
    })
  }
}