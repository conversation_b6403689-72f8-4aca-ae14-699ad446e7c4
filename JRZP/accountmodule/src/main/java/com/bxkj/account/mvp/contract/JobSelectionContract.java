package com.bxkj.account.mvp.contract;

import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.mvp.contract
 * @Description: GetJobClass
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface JobSelectionContract {
    interface View extends BaseView {
        void getJobFirstClassListSuccess(List<JobTypeData> firstClassList);

        void getJobSecondClassListSuccess(List<JobTypeData> secondClassList);

        void addUserExpectedJobsSuccess();

        void deleteUserCheckedJobSuccess();

        void searchJobClassByNameSuccess(List<JobTypeData> searchResultList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        protected abstract void getJobClassList(int type, int firstClassId);

        protected abstract void addUserExpectedJobs(int userId, List<JobTypeData> checkedJobList);

        public abstract void deleteUserCheckedJobById(int userId, List<JobTypeData> checkedJobList);

        public abstract void searchJobClassByName(int type, String name, int top);
    }
}
