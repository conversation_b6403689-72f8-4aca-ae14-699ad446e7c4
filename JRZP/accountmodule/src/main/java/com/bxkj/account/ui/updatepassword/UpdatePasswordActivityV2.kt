package com.bxkj.account.ui.updatepassword

import android.os.Bundle
import android.view.View
import com.bxkj.accountmodule.R
import com.bxkj.accountmodule.databinding.AccountActivityUpdatePasswordV2Binding
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.PersonalMainActivity
import com.therouter.router.Route

/**
 * Description:
 * Author:Sanjin
 * Date:2024/7/18
 **/
@Route(path = UpdatePasswordNavigation.PATH)
class UpdatePasswordActivityV2 :
  BaseDBActivity<AccountActivityUpdatePasswordV2Binding, UpdatePasswordViewModel>() {

  private val extraUpdateType by lazy {
    intent.getIntExtra(
      UpdatePasswordNavigation.EXTRA_UPDATE_TYPE,
      UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_OLD_PWD
    )
  }

  private val extraMobile by lazy {
    intent.getStringExtra(UpdatePasswordNavigation.EXTRA_MOBILE).getOrDefault()
  }

  override fun getViewModelClass(): Class<UpdatePasswordViewModel> =
    UpdatePasswordViewModel::class.java

  override fun getLayoutId(): Int = R.layout.account_activity_update_password_v2

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    viewBinding.etOldPassword.visibility =
      if (extraUpdateType == UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_NUMBER) View.GONE else View.VISIBLE

    subscribeViewModelEvent()

    viewModel.start(extraUpdateType, extraMobile)
  }

  private fun subscribeViewModelEvent() {
    viewModel.updateWithOldPwdSuccessEvent.observe(this, EventObserver {
      showToast("修改成功，请使用新密码重新登录")
      UserUtils.removeUserData()
      Router.getInstance()
        .to(PersonalMainActivity.URL)
        .start()
    })

    viewModel.updateWithNumberSuccessEvent.observe(this, EventObserver {
      showToast("修改成功，请使用新密码重新登录")
      setResult(RESULT_OK)
      finish()
    })
  }
}