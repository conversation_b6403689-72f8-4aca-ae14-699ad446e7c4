package com.bxkj.account.api

import com.bxkj.account.data.LoginResultData
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.user.data.UserAuthStatusData
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.api
 * @Description: 账户相关请求
 * @date 2018/3/29
 */
interface AccountServices {

  @POST(AccountApiConstants.I_UPDATE_PWD_WITH_OLD_PWD)
  suspend fun updatePwdWithOldPwdV2(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(AccountApiConstants.I_UPDATE_PASSWORD_WITH_MOBILE)
  suspend fun updatePwdWithMobileNumberV2(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(AccountApiConstants.I_REGISTER_OF_MOBILE)
  suspend fun registerWithMobile(@Body mRequestBody: ZPRequestBody): BaseResponse<Int>

  @POST("/User/ExistUserMobile/")
  suspend fun checkMobileNumberIsRegistered(@Body mRequestBody: ZPRequestBody?): BaseResponse<Nothing>

  @POST(AccountApiConstants.I_LOGIN)
  fun login(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<LoginResultData>>

  @POST(AccountApiConstants.I_LOGIN)
  suspend fun loginV2(@Body mRequestBody: ZPRequestBody): BaseResponse<LoginResultData>

  @POST(AccountApiConstants.I_SEND_SMS_VERIFICATION_CODE)
  suspend fun requestSmsCode(@Body mRequestBody: ZPRequestBody): BaseResponse<String>

  @POST(AccountApiConstants.I_LOGIN_FOR_MOBILE)
  suspend fun loginWithSmsCode(@Body mRequestBody: ZPRequestBody): BaseResponse<LoginResultData>

  @POST(AccountApiConstants.I_GET_JOB_CLASS_LIST)
  fun getJobClassList(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<List<JobTypeData>>>

  @POST(AccountApiConstants.I_SEARCH_JOB_CLASS_BY_NAME)
  fun searchJobClassByName(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<List<JobTypeData>>>

  @POST(AccountApiConstants.I_SEND_UPDATE_PASSWORD_EMAIL)
  fun sendUpdatePasswordEmail(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<Any?>>

  @POST(AccountApiConstants.I_UPDATE_PASSWORD_WITH_MOBILE)
  fun updatePasswordByMobile(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<Any?>>

  @POST(AccountApiConstants.I_UPDATE_PWD_WITH_OLD_PWD)
  fun updatePasswordByOldPassword(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<Any?>>

  @POST(AccountApiConstants.I_GET_RESUME_COUNT)
  fun getResumeCount(@Body mRequestBody: ZPRequestBody?): Observable<BaseResponse<List<Any>>>

  @POST(AccountApiConstants.I_GET_RESUME_COUNT)
  suspend fun getResumeCountV2(@Body mRequestBody: ZPRequestBody): BaseResponse<List<Any>>

  @POST(AccountApiConstants.I_BIND_USER_PUSH_TOKEN)
  fun bindUserPushToken(@Body mRequestBody: EncryptReqParams?): Observable<BaseResponse<*>>

  @POST("/BusinessLicense/GetBusinessLicenseStatusV2/")
  fun getUserCurrentAuthStatus(
    @Body mRequestBody: EncryptReqParams?
  ): Observable<BaseResponse<List<UserAuthStatusData?>?>?>?

  @POST(CommonApiConstants.I_CHECK_IS_BIND_MOBILE)
  suspend fun checkUserBindPhone(@Body mRequestBody: ZPRequestBody?): BaseResponse<Nothing>
}