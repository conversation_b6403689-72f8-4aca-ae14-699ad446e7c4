package com.bxkj.jrzp.support.chat.ui.allcontact

import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.annotation.IntDef
import androidx.core.app.NotificationManagerCompat
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder.OnItemClickListener
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.constants.PushConstants
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.data.ContactBodyBean.DiffCallback
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentNavigation
import com.bxkj.jrzp.support.chat.BR
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.R.layout
import com.bxkj.jrzp.support.chat.data.ContactFeatureBean
import com.bxkj.jrzp.support.chat.data.ContactTopChatBean
import com.bxkj.jrzp.support.chat.data.NoReplyInfoBean
import com.bxkj.jrzp.support.chat.databinding.ChatFragmentContactListBinding
import com.bxkj.jrzp.support.chat.ui.markunsuitable.MarkUnsuitableActivity
import com.bxkj.jrzp.support.chat.ui.noreplyfriend.NoReplyFriendActivity
import com.bxkj.personal.ui.activity.conversation.GeekChatContentPageNavigation
import com.bxkj.personal.ui.activity.lastjob.LatestJobNavigation
import com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuLayout
import com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuRecyclerView

/**
 * Description:
 * Author:45457
 **/
class AllContactFragment : BaseDBFragment<ChatFragmentContactListBinding, AllContactViewModel>() {
  companion object {
    const val NORMAL = 0

    const val UNSUITABLE = 1

    @IntDef(NORMAL, UNSUITABLE)
    annotation class ContactType

    private const val EXTRA_CONTACT_TYPE = "CONTACT_TYPE"

    fun newInstance(
      @ContactType contactType: Int,
    ): AllContactFragment =
      AllContactFragment().apply {
        arguments = bundleOf(EXTRA_CONTACT_TYPE to contactType)
      }
  }

  private val _contactType by lazy { arguments?.getInt(EXTRA_CONTACT_TYPE).getOrDefault(NORMAL) }

  private var _contactListAdapter: MultiTypeAdapter? = null

  override fun getViewModelClass(): Class<AllContactViewModel> = AllContactViewModel::class.java

  override fun getLayoutId(): Int = R.layout.chat_fragment_contact_list

  override fun initPage(
    view: View,
    saveInstanceState: Bundle?,
  ) {
    viewBinding.viewModel = viewModel

    subscribeReceiveNewMsg()

    setupContactListAdapter()

    viewModel.setPageParams(_contactType)
  }

  override fun onResume() {
    super.onResume()
    viewModel.contactListViewModel.refresh(false)
  }

  private fun subscribeReceiveNewMsg() {
    addDisposable(
      RxBus
        .get()
        .toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_RECEIVE_NEW_MSG) {
            NotificationManagerCompat
              .from(parentActivity)
              .cancel(PushConstants.MSG_TYPE_CONVERSATION_MSG.toInt())
            viewModel.contactListViewModel.refresh(false)
          } else if (it.code == RxMsgCode.REFRESH_CONTACT_LIST) {
            viewModel.contactListViewModel.refresh(false)
          }
        },
    )
  }

  private fun setupContactListAdapter() {
    _contactListAdapter =
      MultiTypeAdapter(context).apply {
        register(
          ContactFeatureBean::class.java,
          DefaultViewBinder<ContactFeatureBean>(
            layout.chat_recycler_feature_item,
            BR.data,
          ).apply {
            setOnItemClickListener(
              object : OnItemClickListener<ContactFeatureBean> {
                override fun onItemClicked(
                  v: View,
                  position: Int,
                  item: ContactFeatureBean,
                ) {
                  when (item.jumpTag) {
                    ContactFeatureBean.JUMP_TAG_0 -> {
                      LatestJobNavigation.create().start()
                    }

                    else -> {
                      startActivity(MarkUnsuitableActivity.newIntent(requireContext()))
                    }
                  }
                }
              },
            )
          },
        )
        register(
          NoReplyInfoBean::class.java,
          DefaultViewBinder<NoReplyInfoBean>(R.layout.chat_recycler_no_reply_feature_item, BR.data).apply {
            setOnItemClickListener(
              object : OnItemClickListener<NoReplyInfoBean> {
                override fun onItemClicked(
                  v: View,
                  position: Int,
                  item: NoReplyInfoBean,
                ) {
                  startActivity(NoReplyFriendActivity.newIntent(requireContext()))
                }
              },
            )
          },
        )
        register(
          ContactTopChatBean::class.java,
          object : DefaultViewBinder<ContactTopChatBean>(
            R.layout.chat_recycler_top_contact_item,
            BR.data,
          ) {
            override fun onBindViewHolder(
              holder: SuperViewHolder,
              item: ContactTopChatBean,
              position: Int,
            ) {
              val ivExpand = holder.findViewById<ImageView>(R.id.iv_expand)
              val expandLayout = holder.findViewById<FrameLayout>(R.id.fl_expand_more)
              expandLayout.visibility =
                if (item.chatList?.size.getOrDefault() > 1) View.VISIBLE else View.GONE
              expandLayout.setOnClickListener {
                item.switchExpandState()
                if (ivExpand.rotation > 0) {
                  ivExpand.animate().rotation(0f).start()
                } else {
                  ivExpand.animate().rotation(180f).start()
                }
              }
              val topContactList =
                holder.findViewById<SwipeMenuRecyclerView>(R.id.recycler_chat_list)
              if (topContactList.adapter == null) {
                topContactList.isNestedScrollingEnabled = false
                topContactList.closeDefaultAnim()
                topContactList.layoutManager = LinearLayoutManager(context)
                topContactList.adapter =
                  SimpleDiffListAdapter(
                    layout.chat_recycler_contact_item,
                    DiffCallback(),
                  ).apply {
                    setOnItemClickListener(
                      object : SuperItemClickListener {
                        override fun onClick(
                          v: View,
                          childListPosition: Int,
                        ) {
                          item.chatList?.get(childListPosition)?.let { clickItem ->
                            when (v.id) {
                              R.id.tv_up -> {
                                topContactList.smoothCloseMenu()
                                viewModel.upOrCancelUpMsg(clickItem)
                              }

                              R.id.tv_delete -> {
                                topContactList.smoothCloseMenu()
                                viewModel.deleteMsg(clickItem, true)
                              }

                              else -> {
                                handleContactClickJump(clickItem)
                              }
                            }
                          }
                        }
                      },
                      R.id.tv_up,
                      R.id.tv_delete,
                    )
                  }
              }
              super.onBindViewHolder(holder, item, position)
            }
          },
        )
        register(
          ContactBodyBean::class.java,
          object :
            DefaultViewBinder<ContactBodyBean>(layout.chat_recycler_contact_item, BR.data) {
            override fun onBindViewHolder(
              holder: SuperViewHolder,
              item: ContactBodyBean,
              position: Int,
            ) {
              super.onBindViewHolder(holder, item, position)
              holder.findViewById<SwipeMenuLayout>(R.id.swipe_layout).isSwipeEnable =
                _contactType == NORMAL
            }
          }.apply {
            setOnItemClickListener(
              object :
                DefaultViewBinder.OnItemClickListener<ContactBodyBean> {
                override fun onItemClicked(
                  v: View,
                  position: Int,
                  item: ContactBodyBean,
                ) {
                  when (v.id) {
                    R.id.tv_up -> {
                      viewBinding.includeContactList.recyclerContent.smoothCloseMenu()
                      viewModel.upOrCancelUpMsg(item)
                    }

                    R.id.tv_delete -> {
                      viewBinding.includeContactList.recyclerContent.smoothCloseMenu()
                      viewModel.deleteMsg(item)
                    }

                    else -> {
                      handleContactClickJump(item)
                    }
                  }
                }
              },
              R.id.tv_up,
              R.id.tv_delete,
            )
          },
        )
      }

    viewBinding.includeContactList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(requireContext())
    }

    viewModel.contactListViewModel.setAdapter(_contactListAdapter)
  }

  /**
   * 处理点击联系人跳转
   */
  private fun handleContactClickJump(clickItem: ContactBodyBean) {
    clickItem.allMsgRead()
    if (UserUtils.isPersonalRole()) {
      GeekChatContentPageNavigation
        .create(
          ChatRole.PERSONAL,
          clickItem.cUserID,
        ).start()
    } else {
      BusinessChatContentNavigation
        .create(
          ChatRole.BUSINESS,
          clickItem.userID,
        ).start()
    }
  }

  fun switchInitiator(initiator: Int) {
    viewModel.switchRole(initiator)
  }

  fun filterByJobId(jobId: Int) {
    viewModel.filterChatByJobId(jobId)
  }

  fun setAllMsgHasRead() {
    viewModel.setAllMsgHasRead()
  }
}
