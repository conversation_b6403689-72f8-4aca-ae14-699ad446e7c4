<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            app:title="@string/chat_setting_title" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/civ_hello_setting"
            style="@style/JrzpCommonListItemView"
            android:onClick="@{onClickListener}"
            app:yui_accessory_type="chevron"
            app:yui_content_type="none"
            app:yui_title="@string/chat_setting_hello_text" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/civ_reply_setting"
            style="@style/JrzpCommonListItemView"
            android:onClick="@{onClickListener}"
            app:yui_accessory_type="chevron"
            app:yui_content_type="none"
            app:yui_title="@string/chat_setting_reply_text" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/civ_phone_setting"
            style="@style/JrzpCommonListItemView"
            android:onClick="@{onClickListener}"
            app:yui_accessory_type="chevron"
            app:yui_content_type="none"
            app:yui_title="@string/chat_setting_exchange_phone_text" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/civ_wechat_setting"
            style="@style/JrzpCommonListItemView"
            android:onClick="@{onClickListener}"
            app:yui_accessory_type="chevron"
            app:yui_content_type="none"
            app:yui_title="@string/chat_setting_exchange_wechat_text" />

    </LinearLayout>
</layout>