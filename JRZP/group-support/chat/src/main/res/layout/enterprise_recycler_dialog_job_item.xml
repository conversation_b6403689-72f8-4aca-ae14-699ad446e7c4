<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.enterprise.data.PositionItemBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ffffff"
        android:padding="@dimen/dp_18">

        <TextView
            android:id="@+id/tv_name"
            style="@style/Text.16sp.333333.Bold"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.name}"
            android:textColor="@color/cl_333333_to_fe6600_selector"
            app:layout_constraintEnd_toStartOf="@id/barrier"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_about"
            style="@style/Text.12sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_4"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.positionAbout}"
            android:textColor="@color/cl_888888_to_fe6600_selector"
            android:visibility="@{data.showAbout()?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/barrier"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="left"
            app:constraint_referenced_ids="iv_select,tv_setting_tag,iv_drag" />

        <ImageView
            android:id="@+id/iv_drag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/b_ic_list_item_drag"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_select"
            style="@style/wrap_wrap"
            android:src="@drawable/ic_selected"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_setting_tag"
            style="@style/Text.14sp.B5B5B5"
            android:text="@string/chat_job_reply_setting_tag"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>