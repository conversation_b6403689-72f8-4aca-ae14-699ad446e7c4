<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap"
  android:orientation="vertical">

  <LinearLayout
    android:id="@+id/ll_business_card"
    android:layout_width="match_parent"
    android:layout_height="@dimen/share_pic_layout_height"
    android:layout_marginStart="@dimen/share_pic_layout_margin"
    android:layout_marginEnd="@dimen/share_pic_layout_margin"
    android:layout_marginBottom="@dimen/dp_14"
    android:background="@drawable/bg_ffffff"
    android:gravity="start"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_36"
    android:paddingTop="@dimen/dp_36"
    android:paddingEnd="@dimen/dp_36">

    <FrameLayout
      android:layout_width="@dimen/common_dp_54"
      android:layout_height="@dimen/common_dp_54">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        style="@style/match_match"
        android:padding="0.5dp"
        app:shapeAppearance="@style/BusinessCardAvatar"
        app:strokeColor="@color/common_f4f4f4"
        app:strokeWidth="@dimen/dp_1" />

      <ImageView
        android:id="@+id/iv_auth_tag"
        style="@style/wrap_wrap"
        android:layout_gravity="end|bottom"
        android:visibility="gone" />

    </FrameLayout>

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.18sp.333333.Bold"
      android:layout_marginTop="@dimen/dp_10"
      android:ellipsize="end"
      android:lines="1" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_24"
      android:gravity="start"
      android:orientation="horizontal">

      <LinearLayout
        style="@style/wrap_wrap"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
          android:id="@+id/tv_notice_count"
          style="@style/Text.14sp.333333.Bold" />

        <TextView
          style="@style/Text.12sp.999999"
          android:text="@string/share_business_card_notice" />

      </LinearLayout>

      <LinearLayout
        android:id="@+id/ll_job_count"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_24"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
          android:id="@+id/tv_job_count"
          style="@style/Text.14sp.333333.Bold" />

        <TextView
          style="@style/Text.12sp.999999"
          android:text="@string/share_business_card_job" />

      </LinearLayout>

      <LinearLayout
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_24"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
          android:id="@+id/tv_like_count"
          style="@style/Text.14sp.333333.Bold" />

        <TextView
          style="@style/Text.12sp.999999"
          android:text="@string/share_business_card_like" />

      </LinearLayout>

      <LinearLayout
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_24"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
          android:id="@+id/tv_fans_count"
          style="@style/Text.14sp.333333.Bold" />

        <TextView
          style="@style/Text.12sp.999999"
          android:text="@string/share_business_card_fans" />

      </LinearLayout>

    </LinearLayout>

    <Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="2"
      android:orientation="vertical">

      <View style="@style/Line.Horizontal" />

      <TextView
        android:id="@+id/tv_desc"
        style="@style/Text.14sp.666666"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:textSize="@dimen/common_sp_13" />

      <View style="@style/Line.Horizontal" />

    </LinearLayout>

    <Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginBottom="@dimen/dp_12"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <LinearLayout
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="start|center_vertical"
        android:orientation="vertical">

        <ImageView
          style="@style/wrap_wrap"
          android:src="@drawable/share_ic_logo" />

        <TextView
          style="@style/Text.12sp.999999"
          android:text="@string/share_business_card_tips" />
      </LinearLayout>

      <ImageView
        android:id="@+id/iv_qr_code"
        android:layout_width="@dimen/common_dp_60"
        android:layout_height="@dimen/common_dp_60"
        android:scaleType="fitXY" />

    </LinearLayout>

  </LinearLayout>


  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/recycler_share_options"
    style="@style/match_wrap"
    android:background="@drawable/bg_share"
    android:overScrollMode="never" />

  <View style="@style/Line.Horizontal" />

  <TextView
    android:id="@+id/tv_cancel"
    style="@style/Text.18sp.333333.Bold"
    android:layout_width="match_parent"
    android:layout_height="@dimen/share_cancel_btn_height"
    android:background="@drawable/bg_ffffff"
    android:gravity="center"
    android:text="@string/share_cancel" />

</LinearLayout>