package com.bxkj.share.ui

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.view.animation.OvershootInterpolator
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.share.*
import com.hjq.toast.Toaster
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class StandardShareDialog private constructor(private val builder: Builder) : ShareDialog() {

  private var attachContext: Context? = null

  override fun onAttach(context: Context) {
    super.onAttach(context)
    attachContext = context
  }

  override fun getRootViewId(): Int = R.layout.share_dialog_normal

  class Builder : ShareDialog.Builder<Builder>() {

    override fun build(): StandardShareDialog {
      return StandardShareDialog(this)
    }

    override fun self(): Builder {
      return this
    }
  }

  override fun initView() {
    super.initView()
    setupCloseClickListener()
    setupShareOptions()
  }

  private fun setupCloseClickListener() {
    rootView.findViewById<TextView>(R.id.tv_cancel).setOnClickListener {
      dismiss()
    }
  }

  private fun setupShareOptions() {
    val optionItemEnterAnim =
      AnimationUtils.loadAnimation(activity, R.anim.anim_share_option_enter).apply {
        interpolator = OvershootInterpolator()
      }
    val shareOptions = getShareOptions()
    val shareOptionList = rootView.findViewById<RecyclerView>(R.id.recycler_share_options)
    shareOptionList.layoutAnimation = LayoutAnimationController(optionItemEnterAnim).apply {
      order = LayoutAnimationController.ORDER_NORMAL
      delay = 0.3f
    }
    shareOptionList.layoutManager = GridLayoutManager(activity, shareOptions.size)
    shareOptionList.adapter = ShareOptionsAdapter(context).apply {
      reset(shareOptions)
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val clickItem = data[position]
          if (builder.onShareItemClickListener?.onShareItemClick(
              this@StandardShareDialog, clickItem
            ) == true
          ) {
            return
          } else {
            handleShareItemClick(clickItem)
          }
        }
      })
    }
  }

  private fun handleShareItemClick(clickItem: ShareOption) {
    when (clickItem.opID) {
      ShareOpID.SHARE_WECHAT -> {
        loadShareBitmap(clickItem.opID)
      }

      ShareOpID.SHARE_WECHAT_MOMENT -> {
        loadShareBitmap(clickItem.opID)
      }

      ShareOpID.SHARE_COPY_LINK -> {
        copyLinkText()
      }
    }
    this.dismiss()
  }

  private fun copyLinkText() {
    val clipboardManager =
      activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val copyText = ClipData.newPlainText("Link", builder.shareUrl)
    clipboardManager.setPrimaryClip(copyText)
    Toaster.show("已复制到剪切板")
  }

  private fun loadShareBitmap(shareOpID: Int) {
    if (!builder.sharePic.isNullOrBlank()) {
      loadWebPic(builder.sharePic.getOrDefault()) { bitmap ->
        shareToWechat(shareOpID, bitmap)
      }
    } else {
      shareToWechat(shareOpID, builder.shareBitmap)
    }
  }

  private fun shareToWechat(
    shareOpID: Int,
    shareBitmap: Bitmap?
  ) {
    val finalShareUrl = "${builder.shareUrl}&fx=android"
    if (CheckUtils.isNullOrEmpty(builder.miniProgramPath)) {
      ShareUtils.shareUrlToWechat(
        attachContext,
        getShareTitle(shareOpID),
        getShareContent(),
        finalShareUrl,
        shareBitmap,
        getWechatShareScene(shareOpID)
      )
    } else {
      ShareUtils.shareMiniProgramToWechat(
        attachContext,
        getShareTitle(shareOpID),
        getShareContent(),
        finalShareUrl,
        builder.miniProgramPath,
        shareBitmap
      )
    }
  }

  private fun getWechatShareScene(shareOpID: Int): Int {
    return if (shareOpID == ShareOpID.SHARE_WECHAT) Req.WXSceneSession
    else Req.WXSceneTimeline
  }

  private fun getShareTitle(shareOpID: Int): String {
    return if (shareOpID == ShareOpID.SHARE_WECHAT) {
      builder.shareTitle ?: ""
    } else {
      if (CheckUtils.isNullOrEmpty(builder.shareMomentTitle)) {
        builder.shareTitle ?: ""
      } else {
        builder.shareMomentTitle ?: ""
      }
    }
  }

  private fun getShareContent(): String {
    return HtmlUtils.delHtmlTag(builder.shareContent)
  }

  private fun getShareOptions(): List<ShareOption> {
    return if (builder.shareOptions.isNullOrEmpty()) {
      getStandardShareOptions()
    } else {
      builder.shareOptions!!
    }
  }

  private fun getStandardShareOptions(): List<ShareOption> {
    return arrayListOf(
      ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat,
        "微信",
        ShareOpID.SHARE_WECHAT
      ),
      ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat_moment,
        "朋友圈",
        ShareOpID.SHARE_WECHAT_MOMENT
      ),
      ShareOption.get(
        R.drawable.ic_page_options_copy_link,
        "复制链接",
        ShareOpID.SHARE_COPY_LINK
      )
    )
  }
}