package com.sanjindev.mui.weiget.commonitemview

import android.content.Context
import android.text.InputFilter
import android.text.InputType
import android.text.TextUtils
import android.text.method.DigitsKeyListener
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.IntDef
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.TextViewCompat
import com.sanjindev.mui.R
import com.sanjindev.mui.getAttrColor
import com.sanjindev.mui.getAttrDimen
import com.sanjindev.mui.getAttrDrawable
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.LOCAL_VARIABLE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description: List组件
 * @author: YangXin
 * @date: 2020/11/30
 * @version: V1.0
 */
class YUICommonListItemView @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = R.attr.YUICommonListItemViewStyle
) : ConstraintLayout(context, attrs, defStyleAttr) {

  companion object {

    const val ACCESSORY_TYPE_NONE = 0
    const val ACCESSORY_TYPE_CHEVRON = 1
    const val ACCESSORY_TYPE_SWITCH = 2
    const val ACCESSORY_TYPE_CUSTOM = 3

    @IntDef(
      ACCESSORY_TYPE_NONE,
      ACCESSORY_TYPE_CHEVRON,
      ACCESSORY_TYPE_SWITCH,
      ACCESSORY_TYPE_CUSTOM
    )
    @Retention(SOURCE)
    @Target(VALUE_PARAMETER, LOCAL_VARIABLE)
    annotation class YUICommonListItemViewAccessoryType

    const val CONTENT_TYPE_NONE = 0
    const val CONTENT_TYPE_TEXT = 1
    const val CONTENT_TYPE_EDIT = 2

    @IntDef(
      CONTENT_TYPE_NONE,
      CONTENT_TYPE_TEXT,
      CONTENT_TYPE_EDIT
    )
    @Retention(SOURCE)
    @Target(VALUE_PARAMETER, LOCAL_VARIABLE)
    annotation class YUICommonListItemViewContentType
  }

  private var mListItemIcon: ImageView? = null
  private var mListItemTitle: TextView? = null
  private var mListItemAccessoryGroup: FrameLayout? = null
  private var mListItemContentGroup: FrameLayout? = null

  private var mContentView: TextView? = null

  private var mAccessoryType: Int =
    ACCESSORY_TYPE_NONE

  init {
    LayoutInflater.from(context).inflate(R.layout.yui_common_list_item_view, this, true)

    val array =
      context.obtainStyledAttributes(
        attrs,
        R.styleable.YUICommonListItemView,
        defStyleAttr,
        0
      )

    val initIcon = array.getDrawable(R.styleable.YUICommonListItemView_yui_icon)
    val initTitle = array.getString(R.styleable.YUICommonListItemView_yui_title)
    val initContentHint = array.getString(R.styleable.YUICommonListItemView_yui_content_hint)
    val initContentInputType =
      array.getInt(R.styleable.YUICommonListItemView_android_inputType, EditorInfo.TYPE_NULL)

    val initDigits = array.getString(R.styleable.YUICommonListItemView_android_digits)

    val initContentLines = array.getInt(R.styleable.YUICommonListItemView_yui_content_lines, 1)

    val initBorderMarginLeft =
      array.getDimensionPixelSize(R.styleable.YUICommonListItemView_yui_border_margin_left, 0)
    val initBorderMarginRight =
      array.getDimensionPixelSize(
        R.styleable.YUICommonListItemView_yui_border_margin_right,
        0
      )
    val initBorderBg = array.getDrawable(R.styleable.YUICommonListItemView_yui_border_bg)

    @YUICommonListItemViewContentType
    val contentType = array.getInt(
      R.styleable.YUICommonListItemView_yui_content_type,
      CONTENT_TYPE_NONE
    )

    @YUICommonListItemViewAccessoryType
    val accessoryType =
      array.getInt(R.styleable.YUICommonListItemView_yui_accessory_type, ACCESSORY_TYPE_NONE)
    array.recycle()

    initView()

    initIcon?.let {
      mListItemIcon?.let { icon ->
        icon.visibility = View.VISIBLE
        icon.setImageDrawable(it)
      }
    }
    initTitle?.let {
      mListItemTitle?.setText(it)
    }

    setupContent(contentType, initContentInputType, initContentLines, initDigits)
    setupAccessoryType(accessoryType)

    initContentHint?.let {
      mContentView?.hint = it
    }

    initBorderBg?.let {
      val border: View = findViewById(R.id.yui_list_item_border)
      border.background = it
      val tempLayoutParams = border.layoutParams as MarginLayoutParams
      tempLayoutParams.leftMargin = initBorderMarginLeft
      tempLayoutParams.rightMargin = initBorderMarginRight
      border.layoutParams = tempLayoutParams
    }
  }

  fun setContentEnabled(enabled: Boolean) {
    mContentView?.isEnabled = enabled
  }

  private fun initView() {
    mListItemIcon = findViewById(R.id.yui_list_item_icon)
    mListItemTitle = findViewById(R.id.yui_list_item_view_title)
    mListItemAccessoryGroup = findViewById(R.id.yui_list_item_accessory)
    mListItemContentGroup = findViewById(R.id.yui_list_item_content_group)
  }

  fun setContent(content: CharSequence?) {
    mContentView?.text = content
  }

  fun getContent(): CharSequence? {
    return mContentView?.text
  }

  fun getContentView(): TextView? {
    return mContentView
  }

  fun setContentTextColor(@ColorInt color: Int) {
    mContentView?.setTextColor(color)
  }

  private fun setupContent(
    contentType: Int,
    contentInputType: Int,
    contentLines: Int,
    digits: String?
  ) {
    when (contentType) {
      CONTENT_TYPE_TEXT -> {
        mContentView = setupContentLayoutParams(TextView(context)).apply {
          ellipsize = TextUtils.TruncateAt.END
        }
      }

      CONTENT_TYPE_EDIT -> {
        mContentView = setupContentLayoutParams(EditText(context)).apply {
          digits?.let {
            val digitsKeyListener = DigitsKeyListener.getInstance(it)
            keyListener = digitsKeyListener
          }
          TextViewCompat.setTextAppearance(this, android.R.style.Widget_EditText)
          background = null
          if (contentInputType != EditorInfo.TYPE_NULL) {
            inputType = contentInputType
          }
          setLines(contentLines)
          if (contentLines > 1) {
            inputType = InputType.TYPE_TEXT_FLAG_MULTI_LINE
            gravity = Gravity.START or Gravity.CENTER_VERTICAL
          }
        }
      }
    }
    mListItemContentGroup?.let { group ->
      mContentView?.let {
        group.removeAllViews()
        group.addView(mContentView)
      }
    }
  }

  private fun setupContentLayoutParams(contentView: TextView): TextView {
    contentView.gravity = Gravity.END
    return contentView.apply {
      layoutParams = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.MATCH_PARENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      setSingleLine(true)
      setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        getAttrDimen(R.attr.yui_list_item_content_text_size).toFloat()
      )
      setTextColor(getAttrColor(R.attr.yui_list_item_content_text_color))
      setHintTextColor(getAttrColor(R.attr.yui_list_item_content_hint_color))
    }
  }

  private fun setupAccessoryType(accessoryType: Int) {
    mListItemAccessoryGroup?.removeAllViews()
    mAccessoryType = accessoryType

    when (accessoryType) {
      ACCESSORY_TYPE_CHEVRON -> {
        val chevronImg =
          ImageView(context).apply {
            layoutParams = LayoutParams(
              FrameLayout.LayoutParams.WRAP_CONTENT,
              FrameLayout.LayoutParams.WRAP_CONTENT
            )
          }
        chevronImg.setImageDrawable(getAttrDrawable(R.attr.yui_list_item_accessory_chevron_icon))
        mListItemAccessoryGroup?.addView(chevronImg)
        mListItemAccessoryGroup?.visibility = View.VISIBLE
      }

      ACCESSORY_TYPE_SWITCH -> {
      }

      ACCESSORY_TYPE_CUSTOM -> {
      }
    }
  }
}