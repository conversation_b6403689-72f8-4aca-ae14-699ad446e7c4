package com.sanjindev.mui.weiget.commonitemview

import android.text.Editable
import android.text.Spanned
import android.text.TextWatcher
import androidx.databinding.BindingAdapter
import androidx.databinding.InverseBindingAdapter
import androidx.databinding.InverseBindingListener
import androidx.databinding.adapters.ListenerUtil
import androidx.databinding.adapters.TextViewBindingAdapter.AfterTextChanged
import androidx.databinding.adapters.TextViewBindingAdapter.BeforeTextChanged
import androidx.databinding.adapters.TextViewBindingAdapter.OnTextChanged
import com.sanjindev.mui.R

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/30
 * @version: V1.0
 */
@BindingAdapter("yui_content")
fun setYUIContent(view: YUICommonListItemView, text: CharSequence?) {
    val oldText: CharSequence? = view.getContent()
    if (text === oldText || text == null && oldText?.length == 0) {
        return
    }
    if (text is Spanned) {
        if (text == oldText) {
            return  // No change in the spans, so don't set anything.
        }
    } else if (!haveContentsChanged(text, oldText)) {
        return  // No content changes, so don't set anything.
    }
    view.setContent(text)
}

@BindingAdapter("yui_enabled")
fun setYUIEnabled(view: YUICommonListItemView, enabled: Boolean) {
    view.isEnabled = enabled
    view.getContentView()?.isEnabled = enabled
}

@InverseBindingAdapter(attribute = "yui_content", event = "android:textAttrChanged")
fun getYUIContentString(view: YUICommonListItemView): String? {
    return view.getContent().toString()
}

@BindingAdapter(
    value = ["android:beforeTextChanged", "android:onTextChanged", "android:afterTextChanged", "android:textAttrChanged"],
    requireAll = false
)
fun setTextWatcher(
    view: YUICommonListItemView, before: BeforeTextChanged?,
    on: OnTextChanged?, after: AfterTextChanged?,
    textAttrChanged: InverseBindingListener?,
) {
    val newValue: TextWatcher? =
        if (before == null && after == null && on == null && textAttrChanged == null) {
            null
        } else {
            object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                    before?.beforeTextChanged(s, start, count, after)
                }

                override fun onTextChanged(
                    s: CharSequence,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                    on?.onTextChanged(s, start, before, count)
                    textAttrChanged?.onChange()
                }

                override fun afterTextChanged(s: Editable) {
                    after?.afterTextChanged(s)
                }
            }
        }
    val oldValue =
        ListenerUtil.trackListener(view, newValue, R.id.textWatcher)
    if (oldValue != null) {
        view.getContentView()?.removeTextChangedListener(oldValue)
    }
    if (newValue != null) {
        view.getContentView()?.addTextChangedListener(newValue)
    }
}

private fun haveContentsChanged(
    str1: CharSequence?,
    str2: CharSequence?,
): Boolean {
    if (str1 == null != (str2 == null)) {
        return true
    } else if (str1 == null) {
        return false
    }
    val length = str1.length
    if (length != str2!!.length) {
        return true
    }
    for (i in 0 until length) {
        if (str1[i] != str2[i]) {
            return true
        }
    }
    return false
}