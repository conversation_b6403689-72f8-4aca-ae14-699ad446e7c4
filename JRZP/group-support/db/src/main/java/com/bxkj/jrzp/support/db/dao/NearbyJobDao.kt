package com.bxkj.jrzp.support.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.bxkj.jrzp.support.db.entry.NearbyJob

/**
 * Description:
 * Author:Sanjin
 * Date:2024/9/23
 **/
@Dao
interface NearbyJobDao {

  @Insert
  fun insertAll(nearbyJob: List<NearbyJob>)

  @Query("SELECT * FROM table_nearby_job")
  fun getAll(): List<NearbyJob>?

  @Query("DELETE FROM table_nearby_job")
  fun deleteAll()
}