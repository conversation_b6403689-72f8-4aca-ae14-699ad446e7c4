package com.bxkj.jrzp.support.feature.ui.sharejobdialog

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.LayoutParams
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter.TabItemConfig
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.dip
import com.bxkj.jrzp.support.feature.R
import com.bxkj.jrzp.support.feature.databinding.ActivityShareJobDialogBinding
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * Description: 分享职位
 * Author:Sanjin
 **/
class ShareJobDialogActivity :
  BaseDBActivity<ActivityShareJobDialogBinding, ShareJobDialogViewModel>() {

  private val jobId by lazy { intent.getIntExtra(EXTRA_JOB_ID, 0) }
  private val userId by lazy { intent.getIntExtra(EXTRA_USER_ID, 0) }

  override fun getViewModelClass(): Class<ShareJobDialogViewModel> =
    ShareJobDialogViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_share_job_dialog

  override fun initPage(savedInstanceState: Bundle?) {
    window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)

    statusBarManager.titleBar(viewBinding.indicator).init()

    viewBinding.ivClose.setOnClickListener {
      finish()
    }

    setupContent()
  }

  private fun setupContent() {
    viewBinding.indicator.navigator = CommonNavigator(this).apply {
      adapter = MagicIndicatorAdapter(
        arrayOf("职位海报", "仅链接"), tabItemConfig = TabItemConfig(
          itemPaddingLR = dip(26),
          textSize = 18f,
          scaleRatio = 0.1f,
          normalColor = R.color.common_666666,
          selectedColor = R.color.common_white,
          indicatorWidth = 24f,
          indicatorHeight = 3f,
          indicatorColor = R.color.cl_ff7405
        )
      ).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    }

    viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 2

      override fun createFragment(position: Int): Fragment {
        return if (position == 0) {
          JobPosterFragment.newInstance(jobId, userId)
        } else {
          JobLinkFragment.newIntent(jobId, userId)
        }
      }
    }

    viewBinding.vpContent.attachIndicator(viewBinding.indicator)
  }

  companion object {

    private const val EXTRA_JOB_ID = "JOB_ID"
    private const val EXTRA_USER_ID = "USER_ID"

    @JvmOverloads
    fun newIntent(context: Context, jobId: Int, userId: Int = UserUtils.getUserId()): Intent {
      return Intent(context, ShareJobDialogActivity::class.java).apply {
        putExtra(EXTRA_JOB_ID, jobId)
        putExtra(EXTRA_USER_ID, userId)
      }
    }
  }
}