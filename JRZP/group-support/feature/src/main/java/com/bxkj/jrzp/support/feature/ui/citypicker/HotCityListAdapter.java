package com.bxkj.jrzp.support.feature.ui.citypicker;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.jrzp.support.feature.R;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.HotCity;

import java.util.List;

public class HotCityListAdapter extends RecyclerView.Adapter<HotCityListAdapter.GridViewHolder> {
    public static final int SPAN_COUNT = 3;

    private Context mContext;
    private List<HotCity> mData;
    private OnCityItemClickListener mInnerListener;

    public HotCityListAdapter(Context context, List<HotCity> data) {
        this.mContext = context;
        this.mData = data;
    }

    @Override
    public GridViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.recycler_select_city_hot_city_item, parent, false);
        return new GridViewHolder(view);
    }

    @Override
    public void onBindViewHolder(GridViewHolder holder, int position) {
        final int pos = holder.getAdapterPosition();
        final City data = mData.get(pos);
        holder.tvHotCity.setText(data.getName());
        holder.tvHotCity.setOnClickListener(v -> {
            if (mInnerListener != null) {
                mInnerListener.onCityClick(v, data);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData == null ? 0 : mData.size();
    }

    public static class GridViewHolder extends RecyclerView.ViewHolder {
        TextView tvHotCity;

        public GridViewHolder(View itemView) {
            super(itemView);
            tvHotCity = itemView.findViewById(R.id.tv_hot_city);
        }
    }

    public void setOnCityItemClickListener(OnCityItemClickListener listener) {
        this.mInnerListener = listener;
    }
}
