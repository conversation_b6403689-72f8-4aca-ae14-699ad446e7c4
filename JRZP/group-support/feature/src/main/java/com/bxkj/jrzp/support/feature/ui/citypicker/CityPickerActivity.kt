package com.bxkj.jrzp.support.feature.ui.citypicker

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.SystemUtil.hideSoftKeyboard
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.location.LocationManager.OnLocationListener
import com.bxkj.common.util.location.LocationManager.getLocationInfo
import com.bxkj.common.util.location.ZPLocation
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout
import com.bxkj.jrzp.support.feature.R
import com.bxkj.jrzp.support.feature.databinding.ActivitySelelctCityBinding
import com.zaaach.citypicker.adapter.decoration.SectionItemDecoration
import com.zaaach.citypicker.db.DBManager
import com.zaaach.citypicker.model.City
import com.zaaach.citypicker.model.HotCity
import com.zaaach.citypicker.model.LocateState
import com.zaaach.citypicker.model.LocatedCity
import androidx.core.view.isGone
import androidx.lifecycle.lifecycleScope
import com.bxkj.common.util.location.GeocodingService
import kotlinx.coroutines.launch

/**
 * @Description: 城市选择
 * @TODO: TODO
 * @date 2019/2/21
 */
class CityPickerActivity
  : BaseDBActivity<ActivitySelelctCityBinding, CityPickerViewModel>() {
  private var mCityListAdapter: CityListAdapter? = null
  private var mSearchResultListAdapter: SearchCityResultListAdapter? = null
  private var mSearchResultStatusLayout: PageStatusLayout? = null

  override fun getLayoutId(): Int {
    return R.layout.activity_selelct_city
  }

  override fun getViewModelClass(): Class<CityPickerViewModel> {
    return CityPickerViewModel::class.java
  }

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.setCityPickerViewModel(viewModel)

    viewBinding.indexBar.setOverlayTextView(viewBinding.tvOverlay)
    subscribeCitiesChange()
    subscribeLocationChange()
    setupSearchResultList()
    viewModel.start(DBManager(getApplicationContext()))
  }

  private fun setupSearchResultList() {
    mSearchResultListAdapter =
      SearchCityResultListAdapter(this, R.layout.recycler_select_city_item)
    mSearchResultListAdapter!!.setOnItemClickListener(
      object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          backAndResultCity(
            mSearchResultListAdapter!!.getData().get(position)
          )
        }
      })
    val recyclerSearchResultList = viewBinding.recyclerSearchResult
    recyclerSearchResultList.setLayoutManager(LinearLayoutManager(this))
    recyclerSearchResultList.setAdapter(mSearchResultListAdapter)

    setupSearchResultStatusLayout()
    subscribeSearchResultListChange()
    subscribeSearchTextChange()
  }

  private fun setupSearchResultStatusLayout() {
    mSearchResultStatusLayout = PageStatusLayout.wrap(viewBinding.flCitiesContent)
  }

  private fun subscribeSearchResultListChange() {
    viewModel.getSearchResultCities().observe(this, Observer { cities: List<City?>? ->
      if (cities.isNullOrEmpty()) {
        mSearchResultStatusLayout!!.show(PageStatusConfigFactory.newEmptyConfig())
      } else {
        mSearchResultStatusLayout!!.hidden()
        if (viewBinding.recyclerSearchResult.isGone) {
          viewBinding.recyclerSearchResult.setVisibility(View.VISIBLE)
        }
        mSearchResultListAdapter!!.reset(cities)
      }
    })
  }

  private fun subscribeSearchTextChange() {
    viewBinding.etSearchContent.addTextChangedListener(object : TextWatcher {
      override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
      }

      override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
      }

      override fun afterTextChanged(s: Editable) {
        val keyword = s.toString()
        if (CheckUtils.isNullOrEmpty(keyword)) {
          mSearchResultStatusLayout!!.hidden()
          viewBinding.recyclerSearchResult.setVisibility(View.GONE)
        } else {
          viewModel.searchCityByKeyword(keyword)
        }
      }
    })
  }

  private fun subscribeLocationChange() {
    viewModel.getLocationCity().observe(this, Observer { city: City? ->
      if (city != null) {
        mCityListAdapter!!.updateLocate(
          LocatedCity(city.getName(), city.getProvince(), city.getCode())
        )
      }
    }
    )
  }

  private fun subscribeCitiesChange() {
    viewModel.getCities()
      .observe(this, Observer { cities: List<City?>? ->
        this.setupCityList(cities)
      })
  }

  private fun setupCityList(cities: List<City?>?) {
    mCityListAdapter = CityListAdapter(
      this, cities,
      this.hotCities
    )
    mCityListAdapter!!.setInnerListener(object : OnCityItemClickListener {
      override fun onCityClick(view: View?, city: City) {
        backAndResultCity(city)
      }

      override fun onLocateClick() {
        mCityListAdapter!!.updateLocateState(LocateState.LOCATING)
        startLocation()
      }
    })
    val recyclerCities = viewBinding.recyclerCities
    val linearLayoutManager = LinearLayoutManager(this)
    recyclerCities.setLayoutManager(linearLayoutManager)
    recyclerCities.addItemDecoration(SectionItemDecoration(this, cities))
    recyclerCities.setAdapter(mCityListAdapter)

    setupIndexBarIndexChangeListener(mCityListAdapter!!, linearLayoutManager)
    startLocation()
  }

  private fun startLocation() {
    checkVisitorMode {
      getLocationInfo(
        this@CityPickerActivity, "权限说明",
        "定位当前位置需要开启定位权限",
        object : OnLocationListener {
          override fun onSuccess(location: ZPLocation) {
            lifecycleScope.launch {
              GeocodingService().reverseGeocoding(location.longitude, location.latitude)?.let {
                if (it.status == "0") {
                  it.result?.addressComponent?.city?.let { city ->
                    viewModel.getDBLocationInfoByLocate(city)
                  }
                } else {
                  mCityListAdapter?.updateLocateState(LocateState.FAILURE)
                }
              }
            }
          }

          override fun onFailed() {
            mCityListAdapter?.updateLocateState(LocateState.FAILURE)
          }
        })
    }
  }

  private val hotCities: MutableList<HotCity?>
    get() {
      val hotCityList: MutableList<HotCity?> = ArrayList<HotCity?>()
      hotCityList.add(HotCity("北京", "北京", "3476"))
      hotCityList.add(HotCity("上海", "上海", "2"))
      hotCityList.add(HotCity("广州", "广东", "15759"))
      hotCityList.add(HotCity("深圳", "广东", "16537"))
      hotCityList.add(HotCity("天津", "天津", "9685"))
      hotCityList.add(HotCity("杭州", "浙江", "28590"))
      hotCityList.add(HotCity("南京", "江苏", "19841"))
      hotCityList.add(HotCity("成都", "四川", "7204"))
      hotCityList.add(HotCity("厦门", "厦门", "35587"))
      return hotCityList
    }

  private fun setupIndexBarIndexChangeListener(
    cityListAdapter: CityListAdapter,
    linearLayoutManager: LinearLayoutManager
  ) {
    viewBinding.indexBar.setOnIndexChangedListener(CityPickerSideIndexBar.OnIndexTouchedChangedListener { index: String?, position: Int ->
      if (cityListAdapter.data.isNullOrEmpty()) {
        return@OnIndexTouchedChangedListener
      }
      if (index.isNullOrEmpty()) {
        return@OnIndexTouchedChangedListener
      }
      for (i in cityListAdapter.data.indices) {
        if (index.substring(0, 1)
          == cityListAdapter.data.get(i).getSection().substring(0, 1)
        ) {
          linearLayoutManager.scrollToPositionWithOffset(i, 0)
          break
        }
      }
    })
  }

  private fun backAndResultCity(city: City) {
    if (CheckUtils.isNullOrEmpty(city.getCode())) {
      showToast("城市数据有误，请重新选择")
      return
    }
    if (getIntent().getBooleanExtra(EXTRA_CHANGE_GLOBAL_CITY, true)) {
      UserUtils.saveUserSelectedCityInfo(city.getCode().toInt(), city.getName())
    }
    hideSoftKeyboard(this)
    val intent = Intent()
    intent.putExtra(EXTRA_RESULT_CITY, city)
    setResult(RESULT_SELECT_CITY_SUCCESS, intent)
    finish()
  }

  companion object {
    private const val EXTRA_CHANGE_GLOBAL_CITY = "CHANGE_GLOBAL_CITY"

    @JvmField val RESULT_SELECT_CITY_SUCCESS: Int = RESULT_OK + 2
    const val EXTRA_RESULT_CITY: String = "RESULT_CITY"

    @JvmOverloads
    @JvmStatic
    fun newIntent(activity: Activity?, changeGlobalCity: Boolean = true): Intent {
      val intent = Intent(activity, CityPickerActivity::class.java)
      intent.putExtra(EXTRA_CHANGE_GLOBAL_CITY, changeGlobalCity)
      return intent
    }
  }
}
