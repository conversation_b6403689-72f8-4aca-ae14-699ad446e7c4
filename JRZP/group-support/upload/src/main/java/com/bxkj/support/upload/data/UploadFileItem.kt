package com.bxkj.support.upload.data

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/11/29
 */
data class UploadFileItem(
  /**
   * 类型
   * 1资讯
   * 2高校
   * 3社交
   * 4问答
   */
  var type: Int,
  /**
   * 类型
   * 1图片
   * 2视频
   */
  var type2: Int,
  var pic: String,
  var video: String,
  var width: Int,
  var height: Int
) {

  companion object {

    fun fromPicPath(path: String): UploadFileItem {
      return UploadFileItem(3, 1, path, "", 0, 0)
    }

    fun fromVideoPath(coverUrl: String, videoUrl: String, width: Int, height: Int): UploadFileItem {
      return UploadFileItem(3, 2, coverUrl, videoUrl, width, height)
    }
  }

}