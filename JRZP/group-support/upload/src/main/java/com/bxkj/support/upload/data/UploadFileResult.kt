package com.bxkj.support.upload.data

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import com.bxkj.common.util.kotlin.getOrDefault

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/14
 * @version: V1.0
 */
open class UploadFileResult(
  var id: Int = 0,
  var url: String = "",
  var url_s: String = ""
) : Parcelable {

  constructor(parcel: Parcel) : this(
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readString().getOrDefault()
  ) {
  }

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(id)
    parcel.writeString(url)
    parcel.writeString(url_s)
  }

  override fun describeContents(): Int {
    return 0
  }

  companion object CREATOR : Creator<UploadFileResult> {
    override fun createFromParcel(parcel: Parcel): UploadFileResult {
      return UploadFileResult(parcel)
    }

    override fun newArray(size: Int): Array<UploadFileResult?> {
      return arrayOfNulls(size)
    }
  }

}