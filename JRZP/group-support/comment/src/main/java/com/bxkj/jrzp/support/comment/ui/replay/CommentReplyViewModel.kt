package com.bxkj.jrzp.support.comment.ui.replay

import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.comment.CommentInfoType
import com.bxkj.jrzp.support.comment.R
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.repository.CommentRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.commentreply
 * @Description:
 * <AUTHOR>
 * @date 2019/10/23
 * @version V1.0
 */
class CommentReplyViewModel @Inject constructor(
  private val mCommentRepository: CommentRepository
) : BaseViewModel() {

  val listViewModel = RefreshListViewModel()
  val replyParentCommentEvent =
    LiveEvent<CommentItemData>()
  val addReplySuccessEvent = LiveEvent<Void>()
  val toUploadAvatarCommand = LiveEvent<Void>()
  val theCommentDeleteCommand = LiveEvent<Void>()
  var mParentComment: CommentItemData? = null
  private var mNewId: Int = 0
  private var mNewCommentCount = 0
  private var mCommentType: Int = CommentInfoType.NEWS

  fun start(intent: Intent) {
    mParentComment = intent.getParcelableExtra(CommentReplayNavigation.EXTRA_PARENT_COMMENT)
    mParentComment?.isParent = true
    mNewId = intent.getIntExtra(CommentReplayNavigation.EXTRA_NEWS_ID, 0)
    mCommentType = intent.getIntExtra(
      CommentReplayNavigation.EXTRA_COMMENT_TYPE,
      CommentInfoType.NEWS
    )
    setupListViewModel(mNewId, mParentComment)
    listViewModel.refresh()
  }

  private fun setupListViewModel(newsId: Int, parentComment: CommentItemData?) {
    parentComment?.let {
      listViewModel.setOnLoadDataListener { currentPage ->
        viewModelScope.launch {
          mCommentRepository.getCommentList(
            getSelfUserID(),
            mCommentType,
            newsId,
            parentComment.pid,
            currentPage,
            CommonApiConstants.DEFAULT_PAGE_SIZE
          ).handleResult({
            if (currentPage == 1) {
              (it as ArrayList<CommentItemData>).add(0, parentComment)
              listViewModel.reset(it)
            } else {
              listViewModel.addAll(it)
            }
          }, {
            if (it.errCode == 30007 || it.errCode == 30003) {
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          })
        }
      }
    }
  }

  fun commentPreCheck(method: () -> Unit) {
    viewModelScope.launch {
      showLoading()
      mCommentRepository.commentPreCheck(getSelfUserID())
        .handleResult({
          method.invoke()
        }, {
          if (it.errCode == 30002 || it.errCode == 30003) {
            toUploadAvatarCommand.call()
          } else {
            showToast(it.errMsg)
          }
        }, {
          hideLoading()
        })
    }
  }

  fun addComment() {
    replyParentCommentEvent.value = mParentComment
  }

  fun reply(comment: CommentItemData) {
    replyParentCommentEvent.value = comment
  }

  fun likeOrUnlikeComment(comment: CommentItemData) {
    if (checkLoginStateAndToLogin()) {
      viewModelScope.launch {
        mCommentRepository.likeOrUnlikeComment(getSelfUserID(), mCommentType, mNewId, comment.pid)
          .handleResult({
            comment.addLike()
          }, {
            if (it.errCode == 10002) {
              comment.removeLike()
            } else {
              showToast(it.errMsg)
            }
          })
      }
    }
  }

  fun addReply(parentComment: CommentItemData, content: String) {
    mParentComment?.let { comment ->
      viewModelScope.launch {
        mCommentRepository.addComment(
          getSelfUserID(),
          mNewId,
          mCommentType,
          comment.pid,
          if (parentComment.isParent) CommonApiConstants.NO_ID else parentComment.uid,
          parentComment.nickName,
          content
        ).handleResult({ result ->
          result?.let {
            mNewCommentCount += 1
            addReplySuccessEvent.call()
            comment.addReply(result.comment)
            if (listViewModel.childCount >= 16) {
              listViewModel.removeAt(listViewModel.childCount - 1)
            }
            listViewModel.add(1, result.comment)
            listViewModel.refreshLayoutViewModel.enableLoadMore(true)
          }
        }, {
          showToast(it.errMsg)
        })
      }
    }
  }

  fun getNewCommentCount(): Int {
    return mNewCommentCount
  }

  fun deleteComment(position: Int, item: CommentItemData) {
    viewModelScope.launch {
      mCommentRepository.deleteComment(item.uid, item.pid)
        .handleResult({
          showToast(R.string.common_delete_success)
          if (item.isParent) {
            theCommentDeleteCommand.call()
          } else {
            listViewModel.removeAt(position)
          }
        }, {
          showToast(it.errMsg)
        })
    }
  }
}