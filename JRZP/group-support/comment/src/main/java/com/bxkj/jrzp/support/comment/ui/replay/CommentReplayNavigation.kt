package com.bxkj.jrzp.support.comment.ui.replay

import android.app.Activity
import androidx.core.os.bundleOf
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.support.comment.CommentConstants
import com.bxkj.jrzp.support.comment.data.CommentItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/19
 * @version: V1.0
 */
class CommentReplayNavigation {

  companion object {
    const val PATH = "${CommentConstants.COMMENT_DIRECTORY}/commentreplay"

    const val EXTRA_NEWS_ID = "NEWS_ID"
    const val EXTRA_PARENT_COMMENT = "PARENT_COMMENT"
    const val EXTRA_NEW_COMMENT_COUNT = "NEW_COMMENT_COUNT"
    const val EXTRA_PARENT_POSITION = "PARENT_POSITION"
    const val EXTRA_COMMENT_TYPE = "COMMENT_TYPE"
    const val RESULT_COMMENT_CHANGED = Activity.RESULT_FIRST_USER + 1
    const val RESULT_COMMENT_DELETED = Activity.RESULT_FIRST_USER + 2

    fun navigate(
      commentType: Int,
      newsId: Int,
      parentPosition: Int,
      parentComment: CommentItemData
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .with(
          bundleOf(
            EXTRA_COMMENT_TYPE to commentType,
            EXTRA_NEWS_ID to newsId,
            EXTRA_PARENT_POSITION to parentPosition,
            EXTRA_PARENT_COMMENT to parentComment
          )
        )
    }
  }
}