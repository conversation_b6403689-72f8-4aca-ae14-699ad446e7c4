package com.bxkj.jrzp.support.feedback.ui.conversation

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.support.feedback.BR
import com.bxkj.jrzp.support.feedback.R
import com.bxkj.jrzp.support.feedback.R.drawable
import com.bxkj.jrzp.support.feedback.data.FeedbackConversationMsgItemData
import com.bxkj.jrzp.support.feedback.databinding.FeedbackActivityConversationBinding
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/21
 * @version: V1.0
 */
class FeedbackConversationActivity :
  BaseDBActivity<FeedbackActivityConversationBinding, FeedbackConversationViewModel>(),
  OnClickListener {

  companion object {
    private const val EXTRA_CONVERSATION_ID = "CONVERSATION_ID"

    internal fun newIntent(context: Context, conversationId: Int): Intent {
      return Intent(context, FeedbackConversationActivity::class.java)
        .apply {
          putExtra(EXTRA_CONVERSATION_ID, conversationId)
        }
    }
  }

  override fun getViewModelClass(): Class<FeedbackConversationViewModel> =
    FeedbackConversationViewModel::class.java

  override fun getLayoutId(): Int = R.layout.feedback_activity_conversation

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupFeedbackConversationListAdapter()

    subscribeViewModelEvent()

    viewModel.start(intent.getIntExtra(EXTRA_CONVERSATION_ID, 0))
  }

  private fun subscribeViewModelEvent() {
    viewModel.scrollToLastMsgPosition.observe(this, Observer {
      viewBinding.includeList.recyclerContent.scrollToPosition(it)
    })
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.iv_select_img) {
        PermissionUtils.requestPermission(
          this,
          getString(R.string.permission_tips_title),
          getString(R.string.permission_select_img_tips),
          object : PermissionUtils.OnRequestResultListener {
            override fun onRequestSuccess() {
              PictureSelector.create(this@FeedbackConversationActivity)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.getInstance())
                .setSandboxFileEngine(SandboxFileEngine.getInstance())
                .setCompressEngine(ImageCompressEngine.getInstance())
                .setImageSpanCount(4)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            }

            override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
              showToast(getString(R.string.cancel_operation))
            }
          },
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE
        )
      }
    }
  }

  private fun setupFeedbackConversationListAdapter() {
    val feedbackConversationListAdapter = FeedbackConversationListAdapter(this)

    feedbackConversationListAdapter.register(
      FeedbackConversationMsgItemData::class.java
    ).to(getUserMsgViewBinder().apply {
      setOnItemClickListener(object :
        DefaultViewBinder.OnItemClickListener<FeedbackConversationMsgItemData> {
        override fun onItemClicked(v: View, position: Int, item: FeedbackConversationMsgItemData) {
          if (v.id == R.id.iv_pic_msg) {
            toPhotoGallery(item.content)
          }
        }
      }, R.id.iv_pic_msg)
    }, getServicesMsgViewBinder().apply {
      setOnItemClickListener(object :
        DefaultViewBinder.OnItemClickListener<FeedbackConversationMsgItemData> {
        override fun onItemClicked(v: View, position: Int, item: FeedbackConversationMsgItemData) {
          if (v.id == R.id.iv_pic_msg) {
            toPhotoGallery(item.content)
          }
        }
      }, R.id.iv_pic_msg)
    }).withLinker { _, item ->
      if (item.isUserMsg()) {
        return@withLinker 0
      } else {
        return@withLinker 1
      }
    }

    val feedbackConversationList = viewBinding.includeList.recyclerContent
    feedbackConversationList.layoutManager = LinearLayoutManager(this)
    feedbackConversationList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          drawable.divider_f4f4f4_8
        ), LinearLayoutManager.VERTICAL
      )
    )

    viewModel.listViewModel.setAdapter(feedbackConversationListAdapter)
  }

  private fun toPhotoGallery(path: String?) {
    path?.let {
      var finalPath = path
      val imgUrlSegment = path.split("/")
      if (imgUrlSegment[imgUrlSegment.size - 1].contains("_s")) {
        finalPath = path.removeRange(path.lastIndexOf(".") - 2, path.lastIndexOf("."))
      }

      PictureSelector.create(this)
        .openPreview()
        .isHidePreviewDownload(false)
        .setImageEngine(GlideEngine.getInstance())
        .startActivityPreview(0, false, arrayListOf(LocalMedia().apply { setPath(finalPath) }))
    }
  }

  private fun getUserMsgViewBinder(): DefaultViewBinder<FeedbackConversationMsgItemData> {
    return DefaultViewBinder(R.layout.feedback_recycler_conversation_user_msg_item, BR.data, true)
  }

  private fun getServicesMsgViewBinder(): DefaultViewBinder<FeedbackConversationMsgItemData> {
    return DefaultViewBinder(
      R.layout.feedback_recycler_conversation_service_msg_item,
      BR.data,
      true
    )
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val pathList = PictureSelector.obtainSelectorList(data)
        viewModel.uploadPic(ZPFileUtils.getPictureSelectorPath(pathList[0]))
      }
    }
  }
}