package com.bxkj.jrzp.support.feedback.ui.feedback

import android.content.Context
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.jrzp.support.feedback.R
import java.io.File

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.postnews
 * @Description:
 * <AUTHOR>
 * @date 2019/12/4
 * @version V1.0
 */
class FeedbackPhotoListAdapter constructor(context: Context, layoutId: Int) :
  SimpleDBListAdapter<PhotoFileItem>(context, layoutId) {

  override fun convert(holder: SuperViewHolder, viewType: Int, item: PhotoFileItem, position: Int) {
    super.convert(holder, viewType, item, position)
    if (!item.isAddItem) {
      ImageLoader.loadImage(
        holder.itemView.context, GlideLoadConfig.Builder().url(File(item.filePath)).into(
          holder.findViewById(
            R.id.iv_photo
          )
        ).build()
      )
    }
  }
}