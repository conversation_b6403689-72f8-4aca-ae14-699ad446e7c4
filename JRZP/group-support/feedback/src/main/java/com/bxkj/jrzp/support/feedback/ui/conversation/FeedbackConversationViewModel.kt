package com.bxkj.jrzp.support.feedback.ui.conversation

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.feedback.repository.FeedbackRepository
import com.bxkj.support.upload.repository.UploadRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/21
 * @version: V1.0
 */
class FeedbackConversationViewModel @Inject constructor(
  private val mFeedbackRepository: FeedbackRepository,
  private val mUploadRepository: UploadRepository
) : BaseViewModel() {

  val listViewModel = RefreshListViewModel()

  val feedbackMsgContent = MutableLiveData<String>()

  val scrollToLastMsgPosition = LiveEvent<Int>()

  private var mFeedbackConversationId = 0

  private var mCurrentPage = 1

  init {
    setupFeedbackConversationListViewModel()
  }

  private fun setupFeedbackConversationListViewModel() {
    listViewModel.refreshLayoutViewModel.enableLoadMore(false)
    listViewModel.setOnLoadDataListener { currentPage ->
      getFeedbackConversationMsgList()
    }
  }

  private fun getFeedbackConversationMsgList() {
    viewModelScope.launch {
      mFeedbackRepository.getFeedbackConversationMsgList(
        getSelfUserID(),
        mFeedbackConversationId,
        mCurrentPage,
        1000
      ).handleResult({
        if (mCurrentPage == 1) {
          listViewModel.reset(it)
        } else {
          listViewModel.addAll(0, it)
        }
        if (mCurrentPage == 1) {
          scrollToLastMsgPosition.value = listViewModel.data.size - 1
        }
        mCurrentPage += 1
      }, {
        listViewModel.loadFinish()
        if (mCurrentPage > 1) {
          mCurrentPage -= 1
        }
      })
    }
  }

  fun start(feedbackConversationId: Int) {
    viewModelScope.launch {
      mFeedbackRepository.updateFeedbackMsgReadStatus(feedbackConversationId)
    }

    mFeedbackConversationId = feedbackConversationId
    refreshMsgList()
  }

  fun sendNormalMsg() {
    feedbackMsgContent.value?.let { content ->
      viewModelScope.launch {
        mFeedbackRepository.sendFeedbackMsg(getSelfUserID(), mFeedbackConversationId, content)
          .handleResult({
            feedbackMsgContent.value = ""
            refreshMsgList()
          }, {
            showToast(it.errMsg)
          })
      }
    }
  }

  fun uploadPic(picPath: String) {
    showLoading()
    viewModelScope.launch {
      mUploadRepository.uploadFile(
        picPath, UploadFileRequestParams.fromFileType(getSelfUserID(), UploadFileRequestParams.TYPE_IMG)
      ).handleResult({
        it?.let {
          sendPicMsg(CommonApiConstants.BASE_JRZP_IMG_URL + it)
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  private fun sendPicMsg(picUrl: String) {
    showLoading()
    viewModelScope.launch {
      mFeedbackRepository.sendFeedbackMsg(getSelfUserID(), mFeedbackConversationId, pic = picUrl)
        .handleResult({
          feedbackMsgContent.value = ""
          refreshMsgList()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  private fun refreshMsgList() {
    mCurrentPage = 1
    listViewModel.refresh()
  }
}
