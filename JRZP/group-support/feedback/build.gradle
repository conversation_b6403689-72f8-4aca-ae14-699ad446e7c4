plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'kotlin-kapt'
  id 'com.google.devtools.ksp'
}

android {

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()

    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  buildFeatures {
    dataBinding = true
  }
  namespace 'com.bxkj.jrzp.support.feedback'
}

dependencies {
  implementation fileTree(dir: "libs", include: ["*.jar"])

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor

  implementation project(":group-support:upload")
  implementation project(":lib-common")
}