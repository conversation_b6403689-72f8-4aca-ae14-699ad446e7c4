<?xml version="1.0" encoding="utf-8"?>
<layout>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingStart="@dimen/common_dp_12"
    android:paddingEnd="@dimen/common_dp_12"
    android:paddingBottom="@dimen/common_dp_16">

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_header"
      android:layout_width="@dimen/common_dp_60"
      android:layout_height="@dimen/common_dp_60"
      android:layout_marginTop="@dimen/common_dp_16"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:shapeAppearance="@style/ImageView.UserAvatar" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.17sp.000000"
      android:layout_marginStart="@dimen/common_dp_10"
      android:layout_marginTop="@dimen/common_dp_8"
      app:layout_constrainedWidth="true"
      app:layout_constraintEnd_toStartOf="@id/tv_basic_info"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintHorizontal_chainStyle="packed"
      app:layout_constraintStart_toEndOf="@id/iv_header"
      app:layout_constraintTop_toTopOf="@id/iv_header" />

    <TextView
      android:id="@+id/tv_basic_info"
      style="@style/Text.14sp.888888"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_name"
      app:layout_constraintEnd_toStartOf="@id/tv_edit_time"
      app:layout_constraintStart_toEndOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_edit_time"
      style="@style/Text.14sp.888888"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_basic_info"
      app:layout_constraintEnd_toEndOf="parent" />

    <TextView
      android:id="@+id/tv_about"
      style="@style/common_Text.14sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/common_dp_6"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <androidx.constraintlayout.widget.Barrier
      android:id="@+id/barrier_header_bottom"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      app:barrierDirection="bottom"
      app:constraint_referenced_ids="iv_header,tv_about"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />

    <View
      android:id="@+id/v_line"
      style="@style/common_Line.Horizontal"
      android:layout_marginTop="@dimen/common_dp_16"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/barrier_header_bottom" />

    <TextView
      android:id="@+id/tv_position"
      style="@style/Text.14sp.333333"
      android:layout_marginTop="@dimen/common_dp_16"
      app:layout_constraintTop_toBottomOf="@id/v_line" />

    <TextView
      android:id="@+id/tv_salary"
      style="@style/Text.14sp.333333"
      android:layout_marginTop="@dimen/common_dp_10"
      app:layout_constraintTop_toBottomOf="@id/tv_position" />
  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>