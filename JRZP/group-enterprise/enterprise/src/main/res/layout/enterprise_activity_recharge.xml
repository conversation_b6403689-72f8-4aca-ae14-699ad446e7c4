<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <TextView
        android:id="@+id/tv_account"
        style="@style/common_Text.FromHeader"
        android:textColor="@color/common_49C280" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_0"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingTop="@dimen/dp_17"
        android:paddingEnd="@dimen/common_dp_12">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/recharge_amount" />

        <RadioGroup
            android:id="@+id/rg_recharge_amount"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_36"
            android:layout_marginTop="@dimen/common_dp_16"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_amount_one"
                style="@style/Recharge_Amount_Items"
                android:layout_marginEnd="@dimen/common_dp_10"
                android:text="@string/recharge_amount_one" />

            <RadioButton
                android:id="@+id/rb_amount_two"
                style="@style/Recharge_Amount_Items"
                android:layout_marginEnd="@dimen/common_dp_10"
                android:text="@string/recharge_amount_two" />

            <RadioButton
                android:id="@+id/rb_amount_three"
                style="@style/Recharge_Amount_Items"
                android:layout_marginEnd="@dimen/common_dp_10"
                android:text="@string/recharge_amount_three" />

            <RadioButton
                android:id="@+id/rb_amount_four"
                style="@style/Recharge_Amount_Items"
                android:text="@string/recharge_amount_four" />
        </RadioGroup>

        <LinearLayout
            style="@style/common_wrap_wrap"
            android:layout_marginTop="@dimen/common_dp_16">

            <EditText
                android:id="@+id/et_other_amount"
                android:layout_width="120dp"
                android:layout_height="@dimen/common_dp_36"
                android:background="@drawable/frame_e8e8e8_to_ff865d_selector"
                android:hint="@string/recharge_other_amount_hint"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:paddingStart="@dimen/common_dp_10"
                android:paddingEnd="@dimen/common_dp_10"
                android:textColor="@color/cl_333333"
                android:textColorHint="@color/common_b5b5b5"
                android:textSize="@dimen/sp_14" />

            <TextView
                style="@style/common_Text.12sp.ff7647"
                android:layout_marginStart="@dimen/common_dp_8"
                android:text="@string/recharge_other_amount_tip" />
        </LinearLayout>

        <TextView
            style="@style/common_Text.12sp.767676"
            android:layout_marginTop="@dimen/common_dp_16"
            android:text="@string/recharge_tip" />

        <TextView
            android:id="@+id/tv_recharge_tips"
            style="@style/common_Text.12sp.767676"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/common_dp_10"
            android:lineSpacingExtra="@dimen/common_dp_10" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/common_dp_12">

        <TextView
            android:id="@+id/tv_consumption"
            style="@style/common_Text.14sp.000000"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="@dimen/common_dp_120"
            android:layout_height="match_parent"
            android:background="@drawable/enterprise_shape_ffbb96_to_ff865d"
            android:gravity="center"
            android:text="@string/common_confirm"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_sp_16" />
    </LinearLayout>

</LinearLayout>