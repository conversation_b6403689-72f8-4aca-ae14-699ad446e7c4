<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

  <data>

    <variable
      name="data"
      type="com.bxkj.enterprise.data.SchoolRecruitCityData" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:background="@drawable/frame_fe6600_round"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_8"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2">

    <TextView
      style="@style/Text.12sp.333333"
      android:layout_width="0dp"
      android:layout_marginEnd="@dimen/dp_4"
      android:layout_weight="1"
      android:autoSizeTextType="uniform"
      android:ellipsize="end"
      android:gravity="start"
      android:lines="1"
      android:text="@{data.name}" />

    <ImageView
      android:id="@+id/iv_delete"
      style="@style/wrap_wrap"
      android:src="@drawable/ic_delete_resume_receive_mailbox" />
  </LinearLayout>

</layout>