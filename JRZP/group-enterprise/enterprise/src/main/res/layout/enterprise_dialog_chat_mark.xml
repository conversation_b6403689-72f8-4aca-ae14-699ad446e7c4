<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.conversation.ChatMarkViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dialog_top_margin"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingEnd="@dimen/dp_12"
            android:paddingStart="@dimen/dp_18">

            <TextView
                style="@style/Text.DialogTitle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/enterprise_chat_mark_title" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_big_close" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_collection"
            style="@style/match_wrap"
            android:onClick="@{()->viewModel.collectOrUnCollect()}"
            android:padding="@dimen/dp_18">

            <ImageView
                android:id="@+id/iv_icon"
                style="@style/wrap_wrap"
                android:src="@{viewModel.resumeIsCollect?@drawable/chat_ic_collected:@drawable/chat_ic_collect}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text.16sp.333333"
                android:layout_marginStart="@dimen/dp_10"
                android:text="@{viewModel.resumeIsCollect?@string/enterprise_chat_collected:@string/enterprise_chat_collect}"
                app:layout_constraintBottom_toTopOf="@id/tv_desc"
                app:layout_constraintStart_toEndOf="@id/iv_icon"
                app:layout_constraintTop_toTopOf="@id/iv_icon"
                app:layout_constraintVertical_chainStyle="spread" />

            <TextView
                android:id="@+id/tv_desc"
                style="@style/Text.14sp.888888"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_4"
                android:text="@string/enterprise_chat_collect_desc"
                app:layout_constraintBottom_toBottomOf="@id/iv_icon"
                app:layout_constraintStart_toEndOf="@id/iv_icon"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

            <ImageView
                style="@style/wrap_wrap"
                android:src="@drawable/common_ic_next"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            style="@style/Line.Horizontal.Light"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_mark"
            style="@style/match_wrap"
            android:padding="@dimen/dp_18">

            <ImageView
                android:id="@+id/iv_mark"
                style="@style/wrap_wrap"
                android:src="@drawable/chat_ic_mark_improper"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_mark"
                style="@style/Text.16sp.333333"
                android:layout_marginStart="@dimen/dp_10"
                android:text="@string/enterprise_chat_mark"
                app:layout_constraintBottom_toTopOf="@id/tv_mark_desc"
                app:layout_constraintStart_toEndOf="@id/iv_mark"
                app:layout_constraintTop_toTopOf="@id/iv_mark"
                app:layout_constraintVertical_chainStyle="spread" />

            <TextView
                android:id="@+id/tv_mark_desc"
                style="@style/Text.14sp.888888"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_4"
                android:text="@string/enterprise_chat_mark_desc"
                app:layout_constraintBottom_toBottomOf="@id/iv_mark"
                app:layout_constraintStart_toEndOf="@id/iv_mark"
                app:layout_constraintTop_toBottomOf="@id/tv_mark" />

            <ImageView
                style="@style/wrap_wrap"
                android:src="@drawable/common_ic_next"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_improper_reason"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18" />

    </LinearLayout>

</layout>