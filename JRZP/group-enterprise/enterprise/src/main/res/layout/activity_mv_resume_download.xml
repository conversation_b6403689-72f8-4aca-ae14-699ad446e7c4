<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.resumedownload.MVResumeDownloadViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/resume_download_page_title" />

    <LinearLayout
      android:id="@+id/ll_job"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.16sp.333333"
        android:text="@string/resume_download_position" />

      <TextView
        style="@style/Text.InfoItem.Select"
        android:text="@{viewModel.selectedJob.name}" />

    </LinearLayout>

    <TextView
      style="@style/Text.InputTips"
      android:text="@string/resume_download_payment_method" />

    <LinearLayout
      style="@style/Layout.InfoItem"
      android:onClick="@{()->viewModel.changeDownloadMethod(1)}">

      <TextView
        style="@style/Text.16sp"
        bind:selected="@{viewModel.downloadPaymentMethod==1}"
        android:text="@string/resume_download_by_count"
        android:textColor="@color/common_333333_to_ff4100_selector" />

      <TextView
        style="@style/Text.14sp.999999"
        android:text="@{HtmlUtils.fromHtml(@string/resume_download_count_format(viewModel.vipInfo.look))}" />

      <Space
        style="@style/wrap_wrap"
        android:layout_weight="1" />

      <ImageView
        style="@style/wrap_wrap"
        android:src="@drawable/ic_select_sel"
        android:visibility="@{viewModel.downloadPaymentMethod==1?View.VISIBLE:View.GONE}" />
    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      style="@style/Layout.InfoItem"
      android:onClick="@{()->viewModel.changeDownloadMethod(2)}">

      <TextView
        style="@style/Text.16sp"
        bind:selected="@{viewModel.downloadPaymentMethod==2}"
        android:text="@string/resume_download_by_integral"
        android:textColor="@color/common_333333_to_ff4100_selector" />

      <TextView
        style="@style/Text.14sp.999999"
        android:text="@{HtmlUtils.fromHtml(@string/resume_download_integral_format(viewModel.vipInfo.douCount))}" />

      <Space
        style="@style/wrap_wrap"
        android:layout_weight="1" />

      <ImageView
        style="@style/wrap_wrap"
        android:src="@drawable/ic_select_sel"
        android:visibility="@{viewModel.downloadPaymentMethod==2?View.VISIBLE:View.GONE}" />
    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <Space
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52"
      android:gravity="center_vertical">

      <TextView
        style="@style/Text.14sp.999999"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_weight="1"
        android:text="@{HtmlUtils.fromHtml(viewModel.downloadPaymentMethod==1?@string/resume_download_total_count_format(viewModel.totalNumber):@string/resume_download_total_integral_format(viewModel.totalIntegral))}" />

      <TextView
        style="@style/Text.18sp.FFFFFF"
        android:layout_width="120dp"
        android:layout_height="match_parent"
        android:background="@color/cl_ff7405"
        android:gravity="center"
        android:onClick="@{()->viewModel.downloadResume()}"
        android:text="@string/confirm" />
    </LinearLayout>

  </LinearLayout>
</layout>