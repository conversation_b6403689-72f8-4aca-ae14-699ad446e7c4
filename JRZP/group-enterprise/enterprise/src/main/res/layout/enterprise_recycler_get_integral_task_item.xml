<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="taskItem"
            type="com.bxkj.enterprise.data.IntegralTaskItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/cl_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap">

        <TextView
            android:id="@+id/tv_name"
            style="@style/common_Text.15sp.333333"
            android:layout_marginStart="@dimen/common_dp_16"
            android:text="@{taskItem.name}"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginEnd="@dimen/common_dp_8"
            app:layout_constraintEnd_toStartOf="@id/tv_integral"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_integral"
            style="@style/common_Text.15sp.49c280"
            android:layout_marginEnd="@dimen/common_dp_14"
            android:text="@{@string/get_integral_task_reward_format(taskItem.integral)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_complete"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_complete"
            style="@style/common_Text.12sp"
            android:background="@{taskItem.record>0?@drawable/bg_ffffff_radius_2:@drawable/bg_49c280_radius_2}"
            android:paddingStart="@dimen/common_dp_6"
            android:paddingTop="@dimen/common_dp_2"
            android:paddingEnd="@dimen/common_dp_6"
            android:paddingBottom="@dimen/common_dp_2"
            android:layout_marginEnd="@dimen/common_dp_16"
            android:textColor="@{taskItem.record>0?@color/cl_888888:@color/common_white}"
            android:text="@{taskItem.record>0?@string/get_integral_task_complete:@string/get_integral_task_normal}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_marginTop="@dimen/common_dp_16"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginEnd="@dimen/common_dp_12"
            style="@style/common_Line.Horizontal"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>