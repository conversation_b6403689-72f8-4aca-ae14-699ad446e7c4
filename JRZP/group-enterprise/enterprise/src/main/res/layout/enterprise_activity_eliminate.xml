<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <TextView
        android:id="@+id/tv_eliminate_tips"
        style="@style/common_Text.15sp.767676"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:background="@drawable/bg_f4f4f4"
        android:gravity="center_vertical"
        android:paddingEnd="@dimen/common_dp_12"
        android:paddingStart="@dimen/common_dp_12" />

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_reason"
        style="@style/match_wrap" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_0"
        android:layout_weight="1"
        android:background="@drawable/bg_f4f4f4">

        <TextView
            android:id="@+id/tv_confirm"
            style="@style/common_Button.Basic"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/common_dp_30"
            android:layout_marginEnd="@dimen/common_dp_30"
            android:layout_marginStart="@dimen/common_dp_30"
            android:text="@string/common_confirm" />
    </FrameLayout>

</LinearLayout>