<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_0"
        android:layout_weight="1"/>

    <View style="@style/common_Line.Horizontal" />

    <FrameLayout
        android:id="@+id/fl_add"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_52"
        android:background="@drawable/bg_ffffff">

        <TextView
            style="@style/common_Text.14sp.888888"
            android:layout_gravity="center"
            android:drawableStart="@drawable/ic_resume_receive_mailbox_add"
            android:gravity="center"
            android:text="@string/hr_add" />
    </FrameLayout>
</LinearLayout>