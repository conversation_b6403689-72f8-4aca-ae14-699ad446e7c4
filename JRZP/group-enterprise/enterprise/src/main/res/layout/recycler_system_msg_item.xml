<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.SystemMsgData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:orientation="vertical"
        android:paddingTop="@dimen/common_dp_16">

        <TextView
            android:id="@+id/tv_title"
            style="@style/common_Text.16sp.333333"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginEnd="@dimen/common_dp_36"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.content}" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/common_Text.12sp.888888"
            android:text="@{data.date}"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginTop="@dimen/common_dp_10" />

        <View
            style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
            android:layout_marginTop="@dimen/common_dp_16" />

    </LinearLayout>
</layout>