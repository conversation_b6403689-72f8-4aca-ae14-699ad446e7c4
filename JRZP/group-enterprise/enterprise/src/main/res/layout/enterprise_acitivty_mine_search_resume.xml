<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_match"
  android:focusable="true"
  android:focusableInTouchMode="true"
  android:orientation="vertical">

  <include layout="@layout/enterprise_include_theme_title_bar" />

  <androidx.core.widget.NestedScrollView style="@style/match_wrap">

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="vertical">

      <com.bxkj.ecommon.widget.ClearEditText
        android:id="@+id/et_search_job"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/common_dp_30"
        android:layout_marginTop="@dimen/common_dp_30"
        android:layout_marginEnd="@dimen/common_dp_30"
        android:background="@drawable/common_bg_rounded_rectangle_f4f4f4"
        android:drawableStart="@drawable/common_ic_search"
        android:drawablePadding="@dimen/common_dp_10"
        android:gravity="center_vertical"
        android:hint="@string/search_resume_hint"
        android:imeOptions="actionDone"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingEnd="@dimen/common_dp_12"
        android:singleLine="true"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5"
        android:textSize="@dimen/sp_14" />

      <LinearLayout
        android:id="@+id/ll_select_area"
        style="@style/common_Layout.InfoItem"
        android:layout_marginStart="@dimen/common_dp_30"
        android:layout_marginTop="@dimen/common_dp_16"
        android:layout_marginEnd="@dimen/common_dp_30">

        <TextView
          android:id="@+id/tv_locate"
          style="@style/common_Text.14sp.888888"
          android:drawableStart="@drawable/ic_drawable_start_locate"
          android:drawablePadding="@dimen/common_dp_8" />

        <TextView
          android:id="@+id/tv_area"
          style="@style/common_Text.InfoItem"
          android:hint="@string/search_resume_area_hint" />
      </LinearLayout>


      <View
        style="@style/common_Line.Horizontal"
        android:layout_marginStart="@dimen/common_dp_30"
        android:layout_marginEnd="@dimen/common_dp_30" />

      <LinearLayout
        android:id="@+id/ll_more_options"
        style="@style/match_wrap"
        android:orientation="vertical"
        android:visibility="gone">

        <!--                <TextView-->
        <!--                    android:id="@+id/tv_expect_industry"-->
        <!--                    style="@style/SearchResumeOptions"-->
        <!--                    android:drawableStart="@drawable/ic_drawable_start_industry"-->
        <!--                    android:hint="@string/search_resume_industry_hint" />-->

        <!--                <View-->
        <!--                    style="@style/common_Line.Horizontal"-->
        <!--                    android:layout_marginStart="@dimen/common_dp_30"-->
        <!--                    android:layout_marginEnd="@dimen/common_dp_30" />-->

        <TextView
          android:id="@+id/tv_work_type"
          style="@style/SearchResumeOptions"
          android:drawableStart="@drawable/ic_drawable_start_work_type"
          android:hint="@string/search_resume_work_type_hint"
          android:visibility="gone" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginEnd="@dimen/common_dp_30"
          android:visibility="gone" />

        <TextView
          android:id="@+id/tv_expect_salary"
          style="@style/SearchResumeOptions"
          android:drawableStart="@drawable/ic_drawable_start_salary"
          android:hint="@string/search_resume_salary_hint" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginEnd="@dimen/common_dp_30" />

        <TextView
          android:id="@+id/tv_work_exp"
          style="@style/SearchResumeOptions"
          android:drawableStart="@drawable/ic_drawable_start_work_exp"
          android:hint="@string/search_resume_work_exp_hint" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginEnd="@dimen/common_dp_30" />

        <TextView
          android:id="@+id/tv_education"
          style="@style/SearchResumeOptions"
          android:drawableStart="@drawable/ic_drawable_start_education"
          android:hint="@string/search_resume_education_hint" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginEnd="@dimen/common_dp_30" />

        <TextView
          android:id="@+id/tv_sex"
          style="@style/SearchResumeOptions"
          android:drawableStart="@drawable/ic_drawable_start_sex"
          android:hint="@string/search_resume_sex_hint" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginEnd="@dimen/common_dp_30" />

      </LinearLayout>

      <TextView
        android:id="@+id/tv_expand_or_collapse"
        style="@style/common_Text.12sp.888888"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/common_dp_30"
        android:drawableEnd="@drawable/ic_search_job_expand_options"
        android:drawablePadding="@dimen/common_dp_9"
        android:text="@string/expand_more_options" />

      <TextView
        android:id="@+id/tv_search_resume"
        style="@style/Button.Basic"
        android:layout_marginStart="@dimen/common_dp_30"
        android:layout_marginTop="@dimen/common_dp_30"
        android:layout_marginEnd="@dimen/common_dp_30"
        android:text="@string/search_resume_start"
        android:textSize="@dimen/common_sp_16" />

      <LinearLayout
        android:id="@+id/ll_search_tool"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_18"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/common_Text.16sp.333333"
          android:layout_width="0dp"
          android:layout_height="match_parent"
          android:layout_marginStart="@dimen/common_dp_20"
          android:layout_weight="1"
          android:drawableStart="@drawable/ic_drawable_start_search_record"
          android:drawablePadding="@dimen/common_dp_8"
          android:gravity="center_vertical"
          android:text="@string/search_resume_record" />

        <TextView
          android:id="@+id/tv_clear_search_record"
          style="@style/common_Text.12sp.888888"
          android:layout_marginEnd="@dimen/common_dp_16"
          android:src="@drawable/ic_delete_item"
          android:text="@string/clear" />
      </LinearLayout>

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_search_record"
        style="@style/match_wrap"
        android:layout_marginBottom="@dimen/common_dp_30" />

    </LinearLayout>

  </androidx.core.widget.NestedScrollView>

</LinearLayout>