<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.businesscontact.BusinessContactInfoViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:right_text="@string/save"
      app:title="@string/enterprise_contact_info" />

    <LinearLayout
      android:id="@+id/ll_avatar_info"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:gravity="center_vertical"
      android:onClick="@{onClickListener}"
      android:orientation="horizontal"
      android:padding="@dimen/dp_16">

      <TextView
        style="@style/Text.22sp.333333"
        android:text="@string/enterprise_contact_info_avatar" />

      <androidx.legacy.widget.Space
        android:layout_width="@dimen/dp_0"
        android:layout_height="0dp"
        android:layout_weight="1" />

      <com.google.android.material.imageview.ShapeableImageView
        android:layout_width="66dp"
        android:layout_height="66dp"
        app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
        bind:imgUrl="@{viewModel.businessContactInfo.photo}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:padding="@dimen/dp_16">

      <TextView
        style="@style/Text.16sp.666666"
        android:text="@string/enterprise_contact_info_name" />

      <EditText
        style="@style/Text.18sp.333333"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@null"
        android:hint="@string/please_enter"
        android:text="@={viewModel.businessContactInfo.userName}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_contact_phone"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:onClick="@{()->viewModel.toUpdatePhoneNumber()}"
      android:orientation="vertical"
      android:padding="@dimen/dp_16">

      <TextView
        android:id="@+id/tv_contact_phone"
        style="@style/Text.16sp.666666"
        android:text="@string/enterprise_contact_info_phone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        style="@style/Text.18sp.333333"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@null"
        android:hint="@string/please_enter"
        android:text="@{viewModel.businessContactInfo.userMobile}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_contact_phone" />

      <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/common_ic_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:padding="@dimen/dp_16">

      <TextView
        style="@style/Text.16sp.666666"
        android:text="@string/enterprise_contact_info_wechat" />

      <EditText
        style="@style/Text.18sp.333333"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@null"
        android:hint="@string/please_enter"
        android:text="@={viewModel.businessContactInfo.weixin}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:padding="@dimen/dp_16">

      <TextView
        style="@style/Text.16sp.666666"
        android:text="@string/enterprise_contact_info_email" />

      <EditText
        style="@style/Text.18sp.333333"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@null"
        android:hint="@string/please_enter"
        android:text="@={viewModel.businessContactInfo.email}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_company"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:onClick="@{onClickListener}"
      android:orientation="vertical"
      android:padding="@dimen/dp_16">

      <TextView
        android:id="@+id/tv_company"
        style="@style/Text.16sp.666666"
        android:text="@string/enterprise_contact_info_company"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        style="@style/Text.18sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@null"
        android:ellipsize="end"
        android:hint="@string/no_content"
        android:text="@{viewModel.businessContactInfo.comName}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_company" />

      <ImageView
        android:id="@+id/iv_next"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/common_ic_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
      android:id="@+id/line_bottom"
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16" />
  </LinearLayout>

</layout>