<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <TextView
        android:id="@+id/tv_name_and_level"
        style="@style/Text.14sp.333333"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginEnd="@dimen/common_dp_20"
        android:layout_marginTop="@dimen/common_dp_16"
        android:layout_weight="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginTop="@dimen/common_dp_16" />

    <TextView
        android:id="@+id/tv_desc"
        style="@style/common_Text.14sp.888888"
        android:layout_marginEnd="@dimen/common_dp_25"
        android:layout_marginTop="@dimen/common_dp_16"
        android:maxLines="2"
        app:layout_constraintStart_toStartOf="@id/tv_name_and_level"
        app:layout_constraintTop_toBottomOf="@id/tv_name_and_level" />

</androidx.constraintlayout.widget.ConstraintLayout>