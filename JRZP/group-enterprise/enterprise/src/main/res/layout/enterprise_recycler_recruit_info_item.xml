<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="recruitInfoItem"
      type="com.bxkj.enterprise.data.RecruitInfoItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.ContentText"
      android:layout_width="@dimen/common_dp_0"
      android:layout_marginStart="@dimen/common_dp_12"
      android:layout_marginTop="@dimen/common_dp_16"
      android:layout_marginEnd="@dimen/common_dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{recruitInfoItem.name}"
      app:layout_constraintEnd_toStartOf="@id/iv_edit"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:id="@+id/iv_edit"
      style="@style/common_wrap_wrap"
      android:layout_marginEnd="@dimen/common_dp_2"
      android:src="@drawable/ic_edit_info_item"
      android:visibility="@{recruitInfoItem.openEdit?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.14sp.999999"
      android:layout_width="@dimen/common_dp_0"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/common_dp_12"
      android:layout_marginTop="@dimen/common_dp_10"
      android:layout_marginEnd="@dimen/common_dp_12"
      android:ellipsize="end"
      android:lineSpacingExtra="@dimen/common_dp_5"
      android:maxLines="2"
      android:text="@{recruitInfoItem.content}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <View
      style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
      android:layout_marginTop="@dimen/common_dp_16"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
