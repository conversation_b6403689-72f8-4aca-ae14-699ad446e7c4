<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingTop="@dimen/common_dp_16">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="100dp"
        android:layout_height="@dimen/common_dp_60"
        android:layout_marginStart="@dimen/common_dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/common_Text.15sp.333333"
        android:layout_marginStart="@dimen/common_dp_10"
        app:layout_constraintStart_toEndOf="@id/iv_cover"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_about"
        style="@style/common_Text.12sp.888888.SingleLine"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginEnd="@dimen/common_dp_28"
        android:layout_marginTop="@dimen/common_dp_5"
        android:lines="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <View
        android:id="@+id/v_line"
        style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
        android:layout_marginTop="@dimen/common_dp_16"
        app:layout_constraintTop_toBottomOf="@id/iv_cover" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_tags"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:layout_marginStart="@dimen/common_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_delete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line" />

    <TextView
        android:id="@+id/tv_delete"
        style="@style/Text.Options"
        android:layout_marginEnd="@dimen/common_dp_10"
        android:text="@string/common_delete"
        app:layout_constraintBottom_toBottomOf="@id/tv_edit"
        app:layout_constraintEnd_toStartOf="@id/tv_edit" />

    <TextView
        android:id="@+id/tv_edit"
        style="@style/Text.Options"
        android:layout_marginBottom="@dimen/common_dp_12"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:layout_marginTop="@dimen/common_dp_12"
        android:text="@string/common_edit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line" />

</androidx.constraintlayout.widget.ConstraintLayout>