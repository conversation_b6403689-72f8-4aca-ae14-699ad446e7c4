<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.enterprise.data.ResumeItemDataV2" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingBottom="@dimen/dp_12">

    <androidx.constraintlayout.widget.ConstraintLayout
      style="@style/match_wrap"
      android:visibility="@{data.hasVideo()?View.GONE:View.VISIBLE}">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar1"
        android:layout_width="@dimen/resume_list_item_avatar_size"
        android:layout_height="@dimen/resume_list_item_avatar_size"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_16"
        bind:imgUrl="@{data.ubInfo.fixAvatarUrl}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle" />

      <TextView
        android:id="@+id/tv_name1"
        style="@style/Text.17sp.000000"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawableEnd="@{data.ubInfo.man?@drawable/resume_ic_male_tag:@drawable/resume_ic_famale_tag}"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:text="@{data.ubInfo.name}"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/tv_salary1"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_avatar1"
        app:layout_constraintTop_toTopOf="@id/iv_avatar1"
        app:layout_constraintVertical_chainStyle="packed" />

      <TextView
        android:id="@+id/tv_about1"
        style="@style/Text.12sp.888888"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:text="@{data.applicantAboutInfo}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar1"
        app:layout_constraintEnd_toStartOf="@id/tv_salary1"
        app:layout_constraintStart_toEndOf="@id/iv_avatar1"
        app:layout_constraintTop_toBottomOf="@id/tv_name1" />

      <TextView
        android:id="@+id/tv_salary1"
        style="@style/Text.17sp.FE6600"
        android:layout_marginEnd="@dimen/dp_12"
        android:text="@{data.formatMoney}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_avatar1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
      style="@style/match_wrap"
      android:visibility="@{data.hasVideo()?View.VISIBLE:View.GONE}">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/resume_list_item_avatar_size"
        android:layout_height="@dimen/resume_list_item_avatar_size"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_16"
        bind:imgUrl="@{data.ubInfo.fixAvatarUrl}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle" />

      <TextView
        android:id="@+id/tv_name"
        style="@style/Text.17sp.000000"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawableEnd="@{data.ubInfo.man?@drawable/resume_ic_male_tag:@drawable/resume_ic_famale_tag}"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{data.ubInfo.name}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tv_salary"
        app:layout_constraintEnd_toStartOf="@id/fl_cover"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

      <TextView
        android:id="@+id/tv_salary"
        style="@style/common_Text.14sp.49C280.Bold"
        android:layout_marginStart="@dimen/dp_10"
        android:text="@{data.formatMoney}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

      <FrameLayout
        android:id="@+id/fl_cover"
        android:layout_width="@dimen/resume_list_item_cover_size"
        android:layout_height="@dimen/resume_list_item_cover_size"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
          android:id="@+id/iv_cover"
          style="@style/match_match"
          bind:loadRadiusImg="@{data.videoPic}" />

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:src="@drawable/ic_play_video" />
      </FrameLayout>

      <TextView
        android:id="@+id/tv_about"
        style="@style/Text.12sp.888888"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:text="@{data.applicantAboutInfo}"
        app:layout_constraintEnd_toStartOf="@id/fl_cover"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Space
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_2" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginStart="@dimen/common_dp_64"
      android:layout_marginTop="@dimen/dp_2"
      android:layout_marginEnd="@dimen/dp_14"
      android:orientation="horizontal"
      android:visibility="@{data.hasJobExp()?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.12sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_weight="1"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{@string/resume_list_info_format(data.jobExp.coname,data.jobExp.job)}" />

      <TextView
        style="@style/Text.12sp.888888"
        android:text="@{data.jobExp.date1}"
        android:typeface="monospace" />

    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginStart="@dimen/common_dp_64"
      android:layout_marginTop="@dimen/dp_2"
      android:layout_marginEnd="@dimen/dp_14"
      android:orientation="horizontal"
      android:visibility="@{data.hasEdu()?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.12sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_weight="1"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{@string/resume_list_info_format(data.edu.school,data.edu.proName1)}" />

      <TextView
        style="@style/Text.12sp.888888"
        android:text="@{data.edu.date1}"
        android:typeface="monospace" />

    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:gravity="center_vertical|bottom"
      android:orientation="horizontal">

      <TextView
        style="@style/Text.12sp.333333"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/common_dp_64"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_weight="1"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{@string/resume_list_expect_job_format(data.detailsName2)}" />

      <TextView
        android:id="@+id/tv_collect"
        style="@style/Text.10sp.FE6600"
        android:layout_marginEnd="@dimen/common_dp_14"
        android:drawableTop="@drawable/resume_list_ic_collect_selector"
        android:text="@string/resume_list_collect"
        android:visibility="gone"
        bind:selected="@{data.isResume==1}" />

      <TextView
        android:id="@+id/tv_call"
        style="@style/Text.10sp.FE6600"
        android:layout_marginEnd="@dimen/common_dp_14"
        android:drawableTop="@drawable/resume_list_ic_download"
        android:text="@{data.isapply==1?@string/resume_list_downloaded:@string/resume_list_download}"
        android:visibility="gone" />

      <TextView
        android:id="@+id/tv_say_hello"
        style="@style/Text.10sp.FE6600"
        android:layout_marginEnd="@dimen/common_dp_14"
        android:drawableTop="@drawable/resume_list_ic_say_hello"
        android:text="@string/resume_list_say_hello"
        android:visibility="gone" />

    </LinearLayout>

  </LinearLayout>
</layout>