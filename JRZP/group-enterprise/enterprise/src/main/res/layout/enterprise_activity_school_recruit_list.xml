<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.schoolrecruitlist.SchoolRecruitListViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:background="@color/common_49C280"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/common_wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_white_back" />

            <com.bxkj.ecommon.widget.ClearEditText
                android:id="@+id/et_search"
                style="@style/common_Text.14sp.888888"
                android:layout_width="@dimen/common_dp_0"
                android:hint="@string/school_recruit_search_hint"
                android:layout_height="@dimen/dp_32"
                android:layout_marginStart="@dimen/common_dp_10"
                android:layout_weight="1"
                android:background="@drawable/bg_ffffff_radius_4"
                android:drawableStart="@drawable/common_ic_search"
                android:drawablePadding="@dimen/common_dp_5"
                android:gravity="center_vertical"
                android:lines="1"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12" />

            <TextView
                android:id="@+id/tv_search"
                style="@style/common_Text.14sp.FFFFFF"
                android:layout_marginStart="@dimen/common_dp_16"
                android:layout_marginEnd="@dimen/common_dp_16"
                android:onClick="@{onClickListener}"
                android:text="@string/search" />
        </LinearLayout>

        <include
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.listViewModel}" />

    </LinearLayout>
</layout>
