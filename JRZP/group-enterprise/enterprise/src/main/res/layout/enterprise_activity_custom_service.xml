<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/service_name" />

        <TextView
            android:id="@+id/tv_name"
            style="@style/common_Text.InfoItem" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal" />

    <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/service_tel" />

        <TextView
            style="@style/common_Text.InfoItem.Select" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal" />

    <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/service_QQ" />

        <TextView
            style="@style/common_Text.InfoItem.Select" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal" />

</LinearLayout>