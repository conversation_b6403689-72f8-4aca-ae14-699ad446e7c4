<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap"
  android:paddingStart="@dimen/common_dp_12"
  android:paddingTop="@dimen/common_dp_16"
  android:paddingEnd="@dimen/common_dp_12"
  android:paddingBottom="@dimen/dp_10">

  <com.google.android.material.imageview.ShapeableImageView
    android:id="@+id/iv_header"
    android:layout_width="@dimen/dp_42"
    android:layout_height="@dimen/dp_42"
    android:scaleType="centerCrop"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:shapeAppearance="@style/roundedCornerImageStyle.Avatar" />

  <TextView
    android:id="@+id/tv_name"
    style="@style/common_Text.16sp.333333"
    android:layout_marginStart="@dimen/common_dp_10"
    app:layout_constraintStart_toEndOf="@id/iv_header"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_about"
    style="@style/common_Text.12sp.888888"
    android:layout_marginTop="@dimen/common_dp_5"
    app:layout_constraintStart_toStartOf="@id/tv_name"
    app:layout_constraintTop_toBottomOf="@id/tv_name" />

  <TextView
    android:id="@+id/tv_position"
    style="@style/common_Text.12sp.888888"
    android:layout_marginTop="@dimen/common_dp_5"
    app:layout_constraintStart_toStartOf="@id/tv_about"
    app:layout_constraintTop_toBottomOf="@id/tv_about" />

  <ImageView
    android:id="@+id/iv_delete"
    style="@style/common_wrap_wrap"
    android:src="@drawable/ic_delete_item"
    app:layout_constraintBottom_toBottomOf="@id/tv_position"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="@id/tv_position" />

</androidx.constraintlayout.widget.ConstraintLayout>