<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.postjob.PostJobV2ViewModel" />
  </data>

  <LinearLayout
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:title="@{viewModel.isEditMode()?@string/update_job_title:@string/post_job_title}" />

    <androidx.core.widget.NestedScrollView
      android:id="@+id/scroll_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          style="@style/PostJobItemInfoStyle"
          app:yui_content="@={viewModel.jobContentData.name}"
          app:yui_content_hint="@string/post_job_position_name_hint"
          app:yui_content_type="edit"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_position_name" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_job_type"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.formatTypeName}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_position_type" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_work_nature"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.natureName}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_work_nature" />

        <LinearLayout
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_52"
          android:gravity="center_vertical"
          android:orientation="horizontal"
          android:visibility="@{viewModel.jobContentData.natureId==2?View.VISIBLE:View.GONE}">

          <ImageView
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_12"
            android:src="@drawable/common_ic_required" />

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/post_job_identity_requirement" />

          <RadioGroup
            android:id="@+id/rg_identity_requirement"
            android:layout_width="@dimen/dp_0"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_weight="1"
            android:gravity="center_vertical|end"
            android:orientation="horizontal">

            <RadioButton
              android:id="@+id/rb_student"
              style="@style/wrap_wrap"
              android:checked="@{viewModel.jobContentData.identityRequirement==1}"
              android:text="@string/post_job_identity_student"
              android:textColor="@color/common_333333_to_ff7647_selector"
              tools:ignore="TouchTargetSizeCheck" />

            <RadioButton
              android:id="@+id/rb_no_student"
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_8"
              android:checked="@{viewModel.jobContentData.identityRequirement==2}"
              android:text="@string/post_job_identity_no_student"
              android:textColor="@color/common_333333_to_ff7647_selector"
              tools:ignore="TouchTargetSizeCheck" />

          </RadioGroup>

        </LinearLayout>

        <View
          style="@style/Line.Horizontal.Light"
          android:visibility="@{viewModel.jobContentData.natureId==2?View.VISIBLE:View.GONE}" />

        <LinearLayout
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_52"
          android:gravity="center_vertical"
          android:orientation="horizontal"
          android:visibility="@{viewModel.jobContentData.natureId==4?View.VISIBLE:View.GONE}">

          <ImageView
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_12"
            android:src="@drawable/common_ic_required" />

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/post_job_nature" />

          <RadioGroup
            android:id="@+id/rg_job_nature"
            android:layout_width="@dimen/dp_0"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_weight="1"
            android:gravity="center_vertical|end"
            android:orientation="horizontal">

            <RadioButton
              android:id="@+id/rb_partner"
              style="@style/wrap_wrap"
              android:checked="@{viewModel.jobContentData.partner==1}"
              android:text="@string/post_job_nature_partner"
              android:textColor="@color/common_333333_to_ff7647_selector"
              tools:ignore="TouchTargetSizeCheck" />

            <RadioButton
              android:id="@+id/rb_no_partner"
              style="@style/wrap_wrap"
              android:layout_marginStart="@dimen/dp_8"
              android:checked="@{viewModel.jobContentData.partner==2}"
              android:text="@string/post_job_nature_no_partner"
              android:textColor="@color/common_333333_to_ff7647_selector"
              tools:ignore="TouchTargetSizeCheck" />

          </RadioGroup>

        </LinearLayout>

        <View
          style="@style/Line.Horizontal.Light"
          android:visibility="@{viewModel.jobContentData.natureId==4?View.VISIBLE:View.GONE}" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_salary"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{()->viewModel.showSalaryPicker()}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.formatSalaryText}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_salary" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_desc"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{()->viewModel.toEditJobDesc()}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.des}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_desc" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{()->viewModel.toSelectAddress()}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.fullAddressText}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_working_place" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{()->viewModel.toSelectJobWelfare()}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.welfare}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_title="@string/post_job_fringe_benefits" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          style="@style/PostJobItemInfoStyle"
          android:inputType="number"
          android:visibility="@{viewModel.showMoreJobInfo?View.VISIBLE:View.GONE}"
          app:yui_content="@={viewModel.recruitNumber}"
          app:yui_content_hint="@string/please_enter"
          app:yui_content_type="edit"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_number_of_recruits" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_edu_req"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          android:visibility="@{viewModel.showMoreJobInfo?View.VISIBLE:View.GONE}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.quaName}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_educational_requirements" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_exp_req"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          android:visibility="@{viewModel.showMoreJobInfo?View.VISIBLE:View.GONE}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.wtName}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_work_exp" />

        <FrameLayout
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_42"
          android:background="@drawable/bg_fafafa"
          android:onClick="@{()->viewModel.switchMoreInfoShowState()}">

          <ImageView
            android:id="@+id/iv_expand"
            style="@style/wrap_wrap"
            android:layout_gravity="center"
            android:src="@drawable/enterprise_ic_post_job_expand_info" />
        </FrameLayout>

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_hr"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.hrName}"
          app:yui_content_hint="@string/common_please_select"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_HR" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_contract_phone"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.hrMobile}"
          app:yui_content_hint="@string/post_job_select_hr_tips"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_contract_phone" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
          android:id="@+id/yli_contract_email"
          style="@style/PostJobItemInfoStyle"
          android:onClick="@{onClickListener}"
          app:yui_accessory_type="chevron"
          app:yui_content="@{viewModel.jobContentData.hrEmail}"
          app:yui_content_hint="@string/post_job_select_hr_tips"
          app:yui_content_type="text"
          app:yui_icon="@drawable/common_ic_required"
          app:yui_title="@string/post_job_contract_mail" />

        <TextView
          style="@style/common_Text.15sp.888888"
          android:layout_width="match_parent"
          android:layout_height="@dimen/common_dp_60"
          android:background="@drawable/bg_f4f4f4"
          android:drawableStart="@drawable/ic_post_job_tips"
          android:drawablePadding="@dimen/common_dp_16"
          android:gravity="center_vertical"
          android:paddingStart="@dimen/common_dp_12"
          android:paddingEnd="@dimen/common_dp_12"
          android:text="@string/post_job_tips" />
      </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
      android:id="@+id/ll_bottom_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_52"
      android:background="@color/common_fbfbfb"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_18"
      android:paddingEnd="@dimen/dp_18"
      android:visibility="@{viewModel.isEditMode()?View.GONE:View.VISIBLE}">

      <TextView
        android:id="@+id/tv_release"
        style="@style/Button.Basic"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="@dimen/dp_42"
        android:layout_weight="1"
        android:gravity="center"
        android:onClick="@{()->viewModel.submit()}"
        android:text="@string/post_job_release" />
    </LinearLayout>

  </LinearLayout>
</layout>