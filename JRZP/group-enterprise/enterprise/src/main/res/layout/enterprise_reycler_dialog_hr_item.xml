<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.HrItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_18">

        <TextView
            android:id="@+id/tv_name"
            style="@style/Text.16sp.333333.Bold"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.name}"
            android:textColor="@color/cl_333333_to_fe6600_selector"
            app:layout_constraintEnd_toStartOf="@id/iv_edit"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_phone"
            style="@style/Text.12sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_4"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.mobile}"
            android:textColor="@color/cl_333333_to_fe6600_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_edit"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

        <ImageView
            android:id="@+id/iv_edit"
            style="@style/wrap_wrap"
            android:src="@drawable/ic_edit_info_item"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>