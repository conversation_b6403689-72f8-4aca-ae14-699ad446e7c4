<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap">

    <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <TextView
            style="@style/Text.Tips"
            android:text="@string/invoice_submit_tips" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/invoice_title" />

            <TextView
                android:id="@+id/tv_invoice_title"
                style="@style/common_Text.InfoItem" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/invoice_duty_paragraph" />

            <EditText
                android:id="@+id/et_duty_paragraph"
                style="@style/common_EditText.Basic.RightAlignment"
                android:digits="@string/common_A_Z_0_9"
                android:hint="@string/invoice_duty_paragraph_hint"
                android:imeOptions="actionNext"
                android:inputType="textEmailAddress" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_special"
            style="@style/match_wrap"
            android:orientation="vertical"
            android:visibility="gone">

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <include layout="@layout/enterprise_include_asterisk" />

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_registered_address" />

                <EditText
                    android:id="@+id/et_reg_address"
                    style="@style/common_EditText.Basic.RightAlignment"
                    android:hint="@string/invoice_registered_address_hint"
                    android:imeOptions="actionNext" />
            </LinearLayout>

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <include layout="@layout/enterprise_include_asterisk" />

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_registered_phone" />

                <EditText
                    android:id="@+id/et_reg_phone"
                    style="@style/common_EditText.Basic.RightAlignment"
                    android:hint="@string/invoice_registered_phone_hint"
                    android:imeOptions="actionNext"
                    android:inputType="phone" />
            </LinearLayout>

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <include layout="@layout/enterprise_include_asterisk" />

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_bank" />

                <EditText
                    android:id="@+id/et_bank_name"
                    style="@style/common_EditText.Basic.RightAlignment"
                    android:hint="@string/invoice_bank_hint"
                    android:imeOptions="actionNext" />
            </LinearLayout>

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <include layout="@layout/enterprise_include_asterisk" />

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_bank_account" />

                <EditText
                    android:id="@+id/et_bank_account"
                    style="@style/common_EditText.Basic.RightAlignment"
                    android:hint="@string/invoice_bank_account_hint"
                    android:imeOptions="actionNext"
                    android:inputType="number" />
            </LinearLayout>

        </LinearLayout>

        <TextView
            style="@style/common_Text.FromHeader"
            android:text="@string/invoice_mailing_address" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/invoice_addressee" />

            <EditText
                android:id="@+id/et_addressee"
                style="@style/common_EditText.Basic.RightAlignment"
                android:hint="@string/invoice_addressee_hint"
                android:imeOptions="actionNext" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/area" />

            <TextView
                android:id="@+id/tv_area"
                style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
            style="@style/match_wrap"
            android:paddingStart="@dimen/common_dp_12"
            android:paddingTop="@dimen/common_dp_16"
            android:paddingEnd="@dimen/common_dp_12"
            android:paddingBottom="@dimen/common_dp_16">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/details_address" />

            <EditText
                android:id="@+id/et_address"
                style="@style/match_wrap"
                android:layout_marginStart="@dimen/common_dp_12"
                android:background="@null"
                android:hint="@string/invoice_address_hint"
                android:imeOptions="actionNext"
                android:lines="3"
                android:textSize="15sp" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/zip_code" />

            <EditText
                android:id="@+id/et_zip_code"
                style="@style/common_EditText.Basic.RightAlignment"
                android:hint="@string/zip_code_hint"
                android:imeOptions="actionNext"
                android:inputType="number" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

            <include layout="@layout/enterprise_include_asterisk" />

            <TextView
                style="@style/common_Text.15sp.333333"
                android:text="@string/mobile" />

            <EditText
                android:id="@+id/et_mobile"
                style="@style/common_EditText.Basic.RightAlignment"
                android:hint="@string/mobile_hint"
                android:imeOptions="actionDone"
                android:inputType="number" />
        </LinearLayout>

        <FrameLayout
            style="@style/match_wrap"
            android:background="@drawable/bg_f4f4f4">

            <TextView
                android:id="@+id/tv_submit"
                style="@style/common_Button.Basic"
                android:layout_marginStart="@dimen/common_dp_30"
                android:layout_marginTop="@dimen/common_dp_36"
                android:layout_marginEnd="@dimen/common_dp_30"
                android:layout_marginBottom="@dimen/common_dp_30"
                android:text="@string/invoice_submit_application" />
        </FrameLayout>
    </LinearLayout>

</androidx.core.widget.NestedScrollView>