package com.bxkj.enterprise.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.enterprise.api.BusinessApi
import com.bxkj.enterprise.data.DailyTaskData
import com.bxkj.enterprise.data.IntegralRightsItemData
import com.bxkj.enterprise.data.IntegralTaskItemData
import com.bxkj.enterprise.data.source.datasource.IntegralDataSource
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @version V1.0
 */
class IntegralRepo @Inject constructor(private val mBusinessApi: BusinessApi) : BaseRepo(),
    IntegralDataSource {

    override fun getIntegralTaskList(
        userId: Int,
        callBack: ResultDataCallBack<List<IntegralTaskItemData>>
    ) {
        mBusinessApi.getIntegralTaskList(userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack.onError(respondThrowable)
                }
            })
    }

    override fun getDailyTaskInfo(userId: Int, callback: ResultDataCallBack<DailyTaskData>) {
        mBusinessApi.getDailyTaskInfo(userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.data))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    override fun getIntegralRightsList(
        userId: Int,
        callback: ResultDataCallBack<List<IntegralRightsItemData>>
    ) {
        mBusinessApi.getIntegralRightsList(userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    override fun checkIsSignIn(userId: Int, callBack: ResultCallBack) {
        mBusinessApi.checkIsSignIn(userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack.onError(respondThrowable)
                }
            })
    }

    override fun signIn(userId: Int, callback: ResultDataCallBack<String>) {
        mBusinessApi.signIn(userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(baseResponse.msg)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }
}
