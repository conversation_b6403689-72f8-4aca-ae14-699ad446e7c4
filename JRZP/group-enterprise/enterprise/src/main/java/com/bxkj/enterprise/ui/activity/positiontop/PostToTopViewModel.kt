package com.bxkj.enterprise.ui.activity.positiontop

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.PositionTopCityItemData
import com.bxkj.enterprise.data.PositionTopCostInfo
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.zaaach.citypicker.model.City
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * author:Sanjin
 * date:2024/12/4
 **/
class PostToTopViewModel @Inject constructor(
  private val myJobRepo: MyJobRepo,
  private val openUserRepository: OpenUserRepository
) : BaseViewModel() {

  val postToTopKeywords = MutableLiveData<String>()
  val postToTopCity = MutableLiveData<City?>()
  val postToTopDaysBalance = MutableLiveData<Int>()
  val beanBalance = MutableLiveData<Int>()
  val postToTopCostList = MutableLiveData<List<PositionTopCostInfo>>()
  val postToTopCostText = MutableLiveData<String>()
  val selectedPostToTopCost = MutableLiveData<PositionTopCostInfo?>()
  val toppedCityList = MutableLiveData<List<PositionTopCityItemData>>()

  val toAddKeywordsCommand = MutableLiveData<VMEvent<ToAddKeywordEvent>>()
  val topSuccessEvent = MutableLiveData<VMEvent<Unit>>()
  val topFailedEvent = MutableLiveData<VMEvent<String>>()
  val noVipTipsEvent = MutableLiveData<VMEvent<String>>()

  private var paymentMethod = PAY_BY_DAYS
  private var jobId: Int = 0

  fun start(jobId: Int) {
    this.jobId = jobId
    getToppedCityList()
    refreshPostToTopDaysBalance()
    refreshBeanBalance()
    getPostToTopCostList()
  }

  private fun getToppedCityList() {
    viewModelScope.launch {
      myJobRepo.getToppedCityList(getSelfUserID(), jobId)
        .handleResult({
          it?.let {
            toppedCityList.value = it
          }
        })
    }
  }

  fun refreshBalance() {
    refreshPostToTopDaysBalance()
    refreshBeanBalance()
  }

  fun toAddKeywords() {
    if (postToTopCity.value == null) {
      showToast("请先选择推广地区")
      return
    }
    if (selectedPostToTopCost.value == null) {
      showToast("请选择推广天数")
      return
    }
    toAddKeywordsCommand.value =
      VMEvent(
        ToAddKeywordEvent(
          postToTopCity.value!!.code.toInt(),
          selectedPostToTopCost.value!!.count,
          ""
        )
      )
  }

  fun setPayByDay() {
    paymentMethod = PAY_BY_DAYS
    selectTopDays(selectedPostToTopCost.value)
  }

  fun setPayByBean() {
    paymentMethod = PAY_BY_BEAN
    selectTopDays(selectedPostToTopCost.value)
  }

  /**
   * 选择置顶天数
   */
  fun selectTopDays(positionTopCostInfo: PositionTopCostInfo? = null) {
    if (paymentMethod == PAY_BY_DAYS) {
      postToTopCostText.value =
        "总计：<font color='#FF7647'>${positionTopCostInfo?.count ?: 0}</font>天"
    } else {
      postToTopCostText.value =
        "总计：<font color='#FF7647'>${positionTopCostInfo?.totalIntegral ?: 0}</font>招聘豆"
    }
    selectedPostToTopCost.value = positionTopCostInfo
  }

  fun setKeywords(keywords: String) {
    postToTopKeywords.value = keywords
  }

  fun setCity(city: City?) {
    toppedCityList.value?.let {
      if (it.find { item -> item.cityName == city?.name } != null) {
        showToast("该城市已经置顶，请重新选择")
        return
      }
    }
    postToTopCity.value = city
  }

  fun addPostTop() {
    val city = postToTopCity.value
    val keywords = postToTopKeywords.value
    if (city == null) {
      showToast("请先选择推广地区")
      return
    }
    if (selectedPostToTopCost.value == null) {
      showToast("请选择推广天数")
      return
    }
    if (keywords.isNullOrBlank()) {
      showToast("请输入关键词")
      return
    }
    showLoading()
    viewModelScope.launch {
      myJobRepo.addPostTop(
        getSelfUserID(),
        jobId,
        selectedPostToTopCost.value!!.count,
        paymentMethod,
        city.code.toInt(),
        keywords ?: ""
      ).handleResult({
        topSuccessEvent.value = VMEvent(Unit)
      }, {
        if (it.errCode == 30010) {  //不是会员
          noVipTipsEvent.value = VMEvent(it.errMsg)
        } else if (it.errCode == 30012 || it.errCode == 30015) { //次数不足
          topFailedEvent.value = it.errMsg?.let { VMEvent(it) }
        } else {
          showToast(it.errMsg)
        }
      }, {
        hideLoading()
      })
    }
  }

  private fun getPostToTopCostList() {
    viewModelScope.launch {
      myJobRepo.getPostTopCostList(getSelfUserID())
        .handleResult({
          it?.let {
            postToTopCostList.value = it
            selectTopDays(it.firstOrNull())
          }
        })
    }
  }

  private fun refreshBeanBalance() {
    viewModelScope.launch {
      openUserRepository.getEnterpriseVipAccountBalance(getSelfUserID())
        .handleResult({
          beanBalance.value = it?.douCount
        })
    }
  }

  private fun refreshPostToTopDaysBalance() {
    viewModelScope.launch {
      myJobRepo.getPostToTopBalance(getSelfUserID())
        .handleResult({
          postToTopDaysBalance.value = it
        })
    }
  }

  companion object {
    private const val PAY_BY_DAYS = 1
    private const val PAY_BY_BEAN = 2
  }

  data class ToAddKeywordEvent(val cityId: Int, val topDays: Int, val keywords: String)
}