package com.bxkj.enterprise.ui.activity.contractinfo;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.contractinfo
 * @Description: ContractInfo
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ContractInfoPresenter extends ContractInfoContract.Presenter {

    private static final String TAG = ContractInfoPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public ContractInfoPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void updateContractInfo(int userId, String contracts, String phone, String qq) {
        mBusinessApi.updateContractInfo(userId, contracts, phone, qq)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
