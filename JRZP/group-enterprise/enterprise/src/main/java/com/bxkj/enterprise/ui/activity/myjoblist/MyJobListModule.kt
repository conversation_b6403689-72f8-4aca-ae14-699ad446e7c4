package com.bxkj.enterprise.ui.activity.myjoblist

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.enterprise.ui.fragment.positionlist.PositionListFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

@Module
abstract class MyJobListModule {

//    @PerFragment
//    @ContributesAndroidInjector
//    abstract fun positionFragment(): PositionFragment

    @PerFragment
    @ContributesAndroidInjector
    abstract fun positionListFragment(): PositionListFragment
}