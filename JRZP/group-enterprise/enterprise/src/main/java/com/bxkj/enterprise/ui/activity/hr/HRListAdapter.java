package com.bxkj.enterprise.ui.activity.hr;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.HrItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.hr
 * @Description: hr列表适配器
 * @TODO: TODO
 * @date 2018/7/30
 */
public class HRListAdapter extends SuperAdapter<HrItemData> {

  public static final int NO_SELECTED = -1;

  private boolean mShowSelector;
  private int mSelectPosition;

  public HRListAdapter(Context context, List<HrItemData> list, int layoutResId,
      boolean showSelector) {
    super(context, layoutResId, list);
    mShowSelector = showSelector;
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, HrItemData hrItemData, int position) {
    ImageView ivSelector = holder.findViewById(R.id.iv_selected);
    ivSelector.setVisibility(mShowSelector ? View.VISIBLE : View.GONE);
    ivSelector.setSelected(mSelectPosition == position);
    ImageLoader.loadImage(mContext, new GlideLoadConfig.Builder().url(
            MTextUtils.appendImgUrl(hrItemData.getDomain(), hrItemData.getPhoto()))
        .into(holder.findViewById(R.id.iv_header)).circle().build());
    holder.setText(R.id.tv_name, hrItemData.getName());
    holder.setText(R.id.tv_mobile, hrItemData.getMobile());
    holder.setText(R.id.tv_email, hrItemData.getEmail());

    holder.itemView.setOnClickListener(v -> {
      mSelectPosition = position;
      notifyDataSetChanged();
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(v, position);
      }
    });

    setOnChildClickListener(position, holder.findViewById(R.id.tv_delete),
        holder.findViewById(R.id.tv_edit));
  }

  public int getSelectedPosition() {
    return mSelectPosition;
  }
}
