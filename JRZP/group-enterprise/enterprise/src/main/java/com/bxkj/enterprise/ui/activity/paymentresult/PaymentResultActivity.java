package com.bxkj.enterprise.ui.activity.paymentresult;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.LoadingView;
import com.bxkj.ecommon.util.AnimationUtils;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.OrderItemData;
import com.bxkj.enterprise.ui.activity.ordermanagement.OrderManagementActivity;
import com.bxkj.personal.ui.activity.main.PersonalMainNavigation;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.paymentresult
 * @Description: 訂單支付結果
 * @TODO: TODO
 * @date 2018/10/18
 */
public class PaymentResultActivity extends BaseDaggerActivity
    implements PaymentResultContract.View {

  private static final String ORDER_ID = "order_id";

  @Inject
  PaymentResultPresenter mPaymentResultPresenter;

  private LoadingView loadingView;
  private ImageView ivPaymentResult;
  private TextView tvPaymentResult;
  private TextView tvPaymentWay;
  private TextView tvPaymentType;
  private TextView tvPaymentContent;
  private TextView tvPaymentDate;
  private TextView tvPaymentMoney;
  private TextView tvPaymentResultOptionOne;
  private TextView tvPaymentResultOptionTwo;
  private LinearLayout llBottomOptionsBar;

  private String mPaymentOrderId;
  private boolean mOrderIsPay;

  public static Intent newIntent(Context context, String orderId) {
    Intent starter = new Intent(context, PaymentResultActivity.class);
    starter.putExtra(ORDER_ID, orderId);
    return starter;
  }

  @Override
  protected void initIntent(Intent intent) {
    mPaymentOrderId = intent.getStringExtra(ORDER_ID);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.payment_result_processing));
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_payment_result;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mPaymentResultPresenter);
    return presenters;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    if (CheckUtils.isNullOrEmpty(mPaymentOrderId)) {
      showToast(getString(R.string.payment_result_order_id_error));
      return;
    }
    mPaymentResultPresenter.getPaymentResult(mPaymentOrderId);
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_payment_result_option_one) {
      OrderManagementActivity.Companion.start(this, mOrderIsPay);
    } else {
      if (mOrderIsPay) {
        PersonalMainNavigation.Companion.navigate().start();
      } else {
        finish();
      }
    }
  }

  @Override
  public void getPaymentResultSuccess(OrderItemData orderItemData) {
    loadingView.setVisibility(View.GONE);
    ivPaymentResult.setVisibility(View.VISIBLE);
    AnimationUtils.alphaToVisibility(llBottomOptionsBar, 500);
    tvPaymentResultOptionOne.setText(getString(R.string.member_order_management));
    if (orderItemData.getIspay() == OrderItemData.PAID) {
      mOrderIsPay = true;
      getTitleBarManager().setTitle(getString(R.string.payment_result_success));
      ivPaymentResult.setImageResource(R.drawable.ic_payment_successful);
      tvPaymentResult.setText(getString(R.string.payment_result_success));
      tvPaymentResultOptionTwo.setText(getString(R.string.payment_result_back_to_home_page));
    } else {
      mOrderIsPay = false;
      getTitleBarManager().setTitle(getString(R.string.payment_result_fail));
      ivPaymentResult.setImageResource(R.drawable.ic_payment_failed);
      tvPaymentResult.setText(getString(R.string.payment_result_fail));
      tvPaymentResultOptionTwo.setText(getString(R.string.payment_result_continue_to_pay));
    }

    tvPaymentWay.setText(getString(R.string.payment_alipay));
    tvPaymentType.setText(orderItemData.getTypeName());
    tvPaymentContent.setText(orderItemData.getContent());
    tvPaymentDate.setText(orderItemData.getTime());
    tvPaymentMoney.setText(getString(R.string.price_format, orderItemData.getPrice()));
  }

  @Override protected void onStop() {
    super.onStop();
    ((LoadingView) findViewById(R.id.loading)).stop();
  }

  @Override
  public void finish() {
    if (mOrderIsPay) {
      setResult(RESULT_OK);
    }
    super.finish();
  }

  private void bindView(View bindSource) {
    loadingView = bindSource.findViewById(R.id.loading);
    ivPaymentResult = bindSource.findViewById(R.id.iv_payment_result);
    tvPaymentResult = bindSource.findViewById(R.id.tv_payment_result);
    tvPaymentWay = bindSource.findViewById(R.id.tv_payment_way);
    tvPaymentType = bindSource.findViewById(R.id.tv_payment_type);
    tvPaymentContent = bindSource.findViewById(R.id.tv_payment_content);
    tvPaymentDate = bindSource.findViewById(R.id.tv_payment_date);
    tvPaymentMoney = bindSource.findViewById(R.id.tv_payment_money);
    tvPaymentResultOptionOne = bindSource.findViewById(R.id.tv_payment_result_option_one);
    tvPaymentResultOptionTwo = bindSource.findViewById(R.id.tv_payment_result_option_two);
    llBottomOptionsBar = bindSource.findViewById(R.id.ll_bottom_options_bar);
    bindSource.findViewById(R.id.tv_payment_result_option_one)
      .setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_payment_result_option_two)
      .setOnClickListener(v -> onViewClicked(v));
  }
}
