package com.bxkj.enterprise.ui.activity.buyfunctionpackages

import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.view.marginTop
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.PackagesDesc
import com.bxkj.enterprise.databinding.BActivityBuyFunctionPackagesBinding
import com.bxkj.enterprise.ui.activity.paymentweb.PaymentWebNavigation
import com.bxkj.personal.ui.activity.web.WebNavigation

/**
 * Description:
 * Author:Sanjin
 * Date:2024/2/1
 **/
@Route(path = BuyFunctionPackageNavigation.PATH)
class BuyFunctionPackagesActivity :
    BaseDBActivity<BActivityBuyFunctionPackagesBinding, BuyFunctionPackagesViewModel>() {

    companion object {

        fun newIntent(context: Context, type: Int): Intent {
            return Intent(context, BuyFunctionPackagesActivity::class.java).apply {
                putExtra(BuyFunctionPackageNavigation.EXTRA_TYPE, type)
            }
        }
    }

    override fun getViewModelClass(): Class<BuyFunctionPackagesViewModel> = BuyFunctionPackagesViewModel::class.java

    override fun getLayoutId(): Int = R.layout.b_activity_buy_function_packages

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        statusBarManager.titleBar(viewBinding.titleBar).init()

        setupTopLayoutParams()

        subscribeViewModelEvent()
    }

    override fun onResume() {
        super.onResume()
        viewModel.start(intent.getIntExtra(BuyFunctionPackageNavigation.EXTRA_TYPE, 0))
    }

    private fun setupTopLayoutParams() {
        viewBinding.titleBar.post {
            viewBinding.tvTitle.apply {
                layoutParams = (layoutParams as MarginLayoutParams).apply {
                    topMargin = viewBinding.titleBar.height + viewBinding.tvTitle.marginTop
                }
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.purchaseSuccessEvent.observe(this, EventObserver {
            ActionDialog.Builder()
                .setContent("您已购买完成，去我的权益查看详情")
                .setConfirmText("查看")
                .setCancelable(false)
                .setOnConfirmClickListener {
                    RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.BUY_BEAN_PACKAGE_SUCCESS))
                    finish()
                    startActivity(
                        WebNavigation.navigate("https://jrzpapi2.jdzj.com/page/Quanyi/Quanyi.aspx").createIntent(this)
                            .apply {
                                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                            })
                }.build().show(supportFragmentManager)
        })

        viewModel.beanNotSufficientFundsEvent.observe(this, EventObserver {
            ActionDialog.Builder()
                .setContent(it)
                .setConfirmText("前往购买")
                .setOnConfirmClickListener {
                    PaymentWebNavigation.create().start()
                }.build().show(supportFragmentManager)
        })

        viewModel.functionPackagesInfo.observe(this, Observer { functionPackagesInfo ->
            viewBinding.tvPaymentMethod.setTextColor(Color.parseColor("#${functionPackagesInfo.ztColor}"))
            viewBinding.tvPaymentMethod.background = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                cornerRadius = dip(6).toFloat()
                color = ColorStateList.valueOf(Color.parseColor("#${functionPackagesInfo.color}"))
                setStroke(
                    dip(1), Color.parseColor("#${functionPackagesInfo.ztColor}")
                )
            }

            viewBinding.tvPayment.background = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                cornerRadius = dip(6).toFloat()
                color = ColorStateList.valueOf(Color.parseColor("#${functionPackagesInfo.ztColor}"))
            }

            if (viewBinding.recyclerPackageList.adapter == null) {
                viewBinding.recyclerPackageList.apply {
                    layoutManager =
                        GridLayoutManager(this@BuyFunctionPackagesActivity, 3)
                    addItemDecoration(GridItemDecoration(getResDrawable(R.drawable.divider_4)))
                    adapter = object : SimpleDiffListAdapter<PackagesDesc>(
                        R.layout.b_recycler_function_package_item,
                        PackagesDesc.DiffCallback()
                    ) {
                        private var _selectedIndex = 0

                        override fun bind(holder: SuperViewHolder, item: PackagesDesc, position: Int) {
                            super.bind(holder, item, position)
                            holder.findViewById<TextView>(R.id.tv_count).apply {
                                setTextColor(
                                    ColorStateList(
                                        arrayOf(
                                            intArrayOf(android.R.attr.state_selected), intArrayOf()
                                        ),
                                        intArrayOf(
                                            Color.parseColor("#${functionPackagesInfo.ztColor}"),
                                            getResColor(R.color.cl_333333)
                                        )
                                    )
                                )
                            }

                            holder.findViewById<TextView>(R.id.tv_price).apply {
                                setTextColor(
                                    ColorStateList(
                                        arrayOf(
                                            intArrayOf(android.R.attr.state_selected), intArrayOf()
                                        ),
                                        intArrayOf(
                                            Color.parseColor("#${functionPackagesInfo.ztColor}"),
                                            getResColor(R.color.cl_333333)
                                        )
                                    )
                                )
                            }

                            val tvValidity = holder.findViewById<TextView>(R.id.tv_validity)

                            if (item.hasValidity()) {
                                tvValidity.background = GradientDrawable().apply {
                                    shape = GradientDrawable.RECTANGLE
                                    cornerRadii = floatArrayOf(
                                        dip(6).toFloat(),
                                        dip(6).toFloat(),
                                        0f,
                                        0f,
                                        dip(6).toFloat(),
                                        dip(6).toFloat(),
                                        0f,
                                        0f
                                    )
                                    color =
                                        ColorStateList.valueOf(Color.parseColor("#${functionPackagesInfo.ztColor}"))
                                }

                                tvValidity.visibility = View.VISIBLE
                                tvValidity.text = item.tianshu
                            } else {
                                tvValidity.visibility = View.GONE
                            }

                            holder.itemView.background = GradientDrawable().apply {
                                shape = GradientDrawable.RECTANGLE
                                cornerRadius = dip(6).toFloat()
                                color = ColorStateList(
                                    arrayOf(
                                        intArrayOf(android.R.attr.state_selected), intArrayOf()
                                    ),
                                    intArrayOf(
                                        Color.parseColor("#${functionPackagesInfo.color}"),
                                        getResColor(R.color.common_f4f4f4)
                                    )
                                )
                                setStroke(
                                    dip(1), ColorStateList(
                                        arrayOf(
                                            intArrayOf(android.R.attr.state_selected), intArrayOf()
                                        ),
                                        intArrayOf(
                                            Color.parseColor("#${functionPackagesInfo.ztColor}"),
                                            getResColor(R.color.common_f4f4f4)
                                        )
                                    )
                                )
                            }

                            holder.itemView.isSelected = _selectedIndex == position

                            holder.itemView.setOnClickListener {
                                if (_selectedIndex != position) {
                                    _selectedIndex = position
                                    notifyDataSetChanged()
                                    viewModel.selectPackage(item)
                                }
                            }
                        }
                    }
                }
            }
        })
    }
}