package com.bxkj.enterprise.ui.activity.postjob

import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/7/15
 */
class SalaryPickerViewModel @Inject constructor() : BaseViewModel() {

  val isCustomMode = MutableLiveData<Boolean>().apply { value = false }

  val showExtraOptions = MutableLiveData<Boolean>().apply { value = false }

  var salaryData: SalaryData? = null

  val confirmSalaryEvent = MutableLiveData<VMEvent<SalaryData>>()

  fun start(
    showExtraOptions: Boolean,
    initData: SalaryData?,
  ) {
    this.showExtraOptions.value = showExtraOptions
    initData?.let {
      if (it.hasCustomSalary()) {
        isCustomMode.value = true
      }
      salaryData = it
    } ?: let { salaryData = SalaryData() }
  }

  fun currentCustomMode(): Boolean {
    return isCustomMode.value ?: false
  }

  fun switchCustomMode() {
    isCustomMode.value?.let {
      val targetCustomMode = it.not()
      if (targetCustomMode) {

      } else {
        salaryData?.apply {
          minSalary = ""
          maxSalary = ""
        }
      }
      isCustomMode.value = targetCustomMode
    }
  }

  fun setSalaryRange(
    index: Int,
    name: String,
  ) {
    salaryData?.apply {
      salaryRangeId = index + 2
      salaryRangeName = name
    }
  }

  fun setSalaryUnit(
    index: Int,
    name: String,
  ) {
    salaryData?.apply {
      salaryUnitId = index
      salaryUnitName = name
    }
  }

  fun setPaymentCycle(
    index: Int,
    name: String,
  ) {
    salaryData?.apply {
      paymentCycleId = index
      paymentCycleName = name
    }
  }

  fun confirm() {
    salaryData?.let { salaryData ->
      if (isCustomMode.value == true) {
        if (salaryData.minSalary.isBlank()) {
          showToast("请填写最低薪资")
          return
        }

        if (salaryData.maxSalary.isBlank()) {
          showToast("请填写最高薪资")
          return
        }

        if (salaryData.maxSalary.toInt() < salaryData.minSalary.toInt()) {
          showToast("最高薪资须大于等于最低薪资")
          return
        }
      } else {
        salaryData.minSalary = "0"
        salaryData.maxSalary = "0"
      }

      confirmSalaryEvent.value = VMEvent(salaryData)
    }
  }
}