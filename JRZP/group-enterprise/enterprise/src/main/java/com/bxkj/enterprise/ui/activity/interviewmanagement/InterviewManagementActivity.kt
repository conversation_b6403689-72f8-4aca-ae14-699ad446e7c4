package com.bxkj.enterprise.ui.activity.interviewmanagement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityInterviewManagementBinding
import com.bxkj.enterprise.ui.activity.invitationinterview.InterviewDetailsActivity
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import com.bxkj.personal.ui.activity.interviewdetails.GeekInterviewDetailsNavigation

@Route(path = InterviewManagementNavigation.PATH)
class InterviewManagementActivity :
    BaseDBActivity<EnterpriseActivityInterviewManagementBinding, InterviewManagementViewModel>() {

    companion object {

        fun newIntent(context: Context): Intent =
            Intent(context, InterviewManagementActivity::class.java)
    }

    private val _interviewDetailsLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == RESULT_OK) {
                viewModel.listViewModel.refresh()
            }
        }

    override fun getViewModelClass(): Class<InterviewManagementViewModel> =
        InterviewManagementViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_interview_management

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupInterviewList()

        viewModel.start()
    }

    private fun setupInterviewList() {
        viewBinding.includeInterviewManagement.recyclerContent.apply {
            layoutManager = LinearLayoutManager(this@InterviewManagementActivity)
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_12))
                    .drawHeader(true)
                    .drawFoot(true).build()
            )
        }
        viewModel.listViewModel.setAdapter(
            SimpleDBListAdapter<InterviewInfoBean>(
                this@InterviewManagementActivity,
                R.layout.enterprise_recycler_interview_management_item
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        data[position]?.let {
                            if (UserUtils.isPersonalRole()) {
                                _interviewDetailsLauncher.launch(
                                    GeekInterviewDetailsNavigation.create(it.relId, it.resId).createIntent(this@InterviewManagementActivity)
                                )
                            } else {
                                _interviewDetailsLauncher.launch(
                                    InterviewDetailsActivity.newIntent(
                                        this@InterviewManagementActivity,
                                        it.relId,
                                        it.resId
                                    )
                                )
                            }
                        }
                    }
                })
            }
        )
    }
}