package com.bxkj.enterprise.ui.fragment.schooldetails;

import android.os.Bundle;

import com.bxkj.ecommon.base.EBaseDBFragment;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.databinding.EnterpriseFragmentSchoolDetailsBinding;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.schooldetails
 * @Description: 校园招聘详情
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolDetailsFragment extends
    EBaseDBFragment<EnterpriseFragmentSchoolDetailsBinding, SchoolDetailsViewModel> {

    private static final String EXTRA_SCHOOL_DETAILS = "SCHOOL_DETAILS";

    public static SchoolDetailsFragment newInstance(String schoolDetails) {
        Bundle args = new Bundle();
        args.putString(EXTRA_SCHOOL_DETAILS, schoolDetails);
        SchoolDetailsFragment fragment = new SchoolDetailsFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected Class<SchoolDetailsViewModel> getViewModelClass() {
        return SchoolDetailsViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.enterprise_fragment_school_details;
    }

    @Override
    protected void initPage() {
        if (getArguments() != null) {
            String content = getArguments().getString(EXTRA_SCHOOL_DETAILS);
            if (!CheckUtils.isNullOrEmpty(content)) {
                getDataBinding().webContent.loadDataWithBaseURL(null, content, "text/html", "utf-8", null);
            } else {
                getDataBinding().pageStatusLayout.show(PageStatusConfigFactory.newEmptyConfig());
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getDataBinding().webContent.resumeTimers();
        getDataBinding().webContent.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        getDataBinding().webContent.pauseTimers();
        getDataBinding().webContent.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (getDataBinding().webContent != null) {
            getDataBinding().webContent.destroy();
        }
    }
}
