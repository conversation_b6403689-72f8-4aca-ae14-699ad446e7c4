package com.bxkj.enterprise.ui.fragment.collection

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.api.parameters.GetReceivedResumeListParameters
import com.bxkj.enterprise.data.source.AccountInfoRepo
import com.bxkj.enterprise.data.source.ResumeRepo
import com.bxkj.enterprise.data.ResumeItemDataV2
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/3/21
 **/
class FavoritesResumeListViewModel @Inject constructor(
  private val resumeRepo: ResumeRepo,
  private val accountInfoRepo: AccountInfoRepo
) : BaseViewModel() {

  val listViewModel = RefreshListViewModel()

  private val getReceivedResumeListParams =
    GetReceivedResumeListParameters().apply {
      resumeTypes = "3,4"
    }

  fun start(pageType: Int) {
    if (pageType == FavoritesResumeListFragment.PAGE_TYPE_FAVORITES) {
      listViewModel.setOnLoadDataListener { pageIndex ->
        viewModelScope.launch {
          resumeRepo.getFavoritesResumeList(getSelfUserID(), pageIndex, 16)
            .handleResult({
              listViewModel.autoAddAll(it)
            }, {
              if (it.isNoDataError) {
                listViewModel.noMoreData()
              } else {
                listViewModel.loadError()
              }
            })
        }
      }
    } else {
      listViewModel.setOnLoadDataListener { pageIndex ->
        viewModelScope.launch {
          resumeRepo.getReceivedResumeList(
            getSelfUserID(),
            getReceivedResumeListParams,
            pageIndex,
            16
          ).handleResult({
            listViewModel.autoAddAll(it)
          }, {
            if (it.isNoDataError) {
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          })
        }
      }
    }
    listViewModel.refresh()
  }

  fun refreshList() {
    listViewModel.refresh()
  }

  fun cancelFavoriteResume(resume: ResumeItemDataV2) {
    viewModelScope.launch {
      showLoading()
      resumeRepo.addOrRemoveFavoriteResume(getSelfUserID(), resume.id)
        .handleResult({

        }, {
          if (it.errCode == 10002) {
            showToast("已取消收藏")
            if (listViewModel.remove(resume) == 0) {
              listViewModel.refresh()
            }
          } else {
            showToast(it.errMsg)
          }
        }, {
          hideLoading()
        })
    }
  }
}