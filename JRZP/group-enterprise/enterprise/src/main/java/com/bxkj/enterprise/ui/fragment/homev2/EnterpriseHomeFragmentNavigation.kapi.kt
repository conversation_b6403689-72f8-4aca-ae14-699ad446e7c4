package com.bxkj.enterprise.ui.fragment.homev2

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2022/7/29
 */
class EnterpriseHomeFragmentNavigation {

    companion object {
        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/home"

        fun create(): Fragment {
            return Router.getInstance().to(PATH).createFragment() as Fragment
        }
    }
}