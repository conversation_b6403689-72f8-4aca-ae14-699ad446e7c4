package com.bxkj.enterprise.api.parameters;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.api.parameters
 * @Description:
 * @TODO: TODO
 * @date 2018/8/9
 */
public class GetReceivedResumeListParameters {
  //是否被查看过，-1全部，0否，1是
  private int isLook = -1;
  /**
   * 类型，多个以英文逗号拼接
   * 1用户主动投递；
   * 2系统推荐
   * 3企业主动下载(来源系统推荐)
   * 4企业主动下载
   * 5邀请投递
   */
  private String resumeTypes;
  private int resumeType;
  /**
   * 处理状态，多个以英文逗号拼接
   * 0未处理
   * 1初选合格
   * 2淘汰
   * 3面试
   * 4面试被拒绝
   * 5面试通过
   * 6已发offer
   * 7已拒offer
   * 8接受offer
   * 9已入职
   */
  private String resumeStates;
  private int resumeState;
  //职位编号
  private int positionId;

  public int getIsLook() {
    return isLook;
  }

  public void setIsLook(int isLook) {
    this.isLook = isLook;
  }

  public String getResumeTypes() {
    return resumeTypes;
  }

  public void setResumeTypes(String resumeTypes) {
    this.resumeTypes = resumeTypes;
  }

  public int getResumeType() {
    return resumeType;
  }

  public void setResumeType(int resumeType) {
    this.resumeType = resumeType;
  }

  public String getResumeStates() {
    return resumeStates;
  }

  public void setResumeStates(String resumeStates) {
    this.resumeStates = resumeStates;
  }

  public int getResumeState() {
    return resumeState;
  }

  public void setResumeState(int resumeState) {
    this.resumeState = resumeState;
  }

  public int getPositionId() {
    return positionId;
  }

  public void setPositionId(int positionId) {
    this.positionId = positionId;
  }
}
