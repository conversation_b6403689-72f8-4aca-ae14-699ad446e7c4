package com.bxkj.enterprise.weight.filterareapopup;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.adapter.superadapter.SuperItemClickListener;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.widget.filterpopup.FilterRecyclerItemAdapter;
import com.bxkj.enterprise.R;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;
import java.util.List;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.filterareapopup
 * @Description:
 * @TODO: TODO
 * @date 2019/3/16
 */
public class FilterAreaView extends ConstraintLayout {

  private TextView tvCurrentCity;

  private FilterRecyclerItemAdapter mAreaAdapter;

  public FilterAreaView(Context context) {
    this(context, null);
  }

  public FilterAreaView(Context context, AttributeSet attrs) {
    this(context, attrs, 0);
  }

  public FilterAreaView(Context context, AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
    init();
  }

  private void init() {
    inflate(getContext(), R.layout.enterprise_popup_filter_area, this);

    tvCurrentCity = findViewById(R.id.tv_current_city);
    findViewById(R.id.ll_change_current_city).setOnClickListener(view -> {
      if (mAreaItemClickListener != null) {
        mAreaItemClickListener.onChangeCityClicked();
      }
    });

    mAreaAdapter = new FilterRecyclerItemAdapter(getContext(), R.layout.common_recycler_filter_options_wrap_item, null
    );
    mAreaAdapter.setOnItemClickListener(new SuperItemClickListener() {
      @Override
      public void onClick(@NonNull View v, int position) {
        mAreaItemClickListener.onAreaItemClicked(position);
      }
    });

    final RecyclerView rvArea = findViewById(R.id.recycler_filter_area);
    FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(getContext());
    layoutManager.setFlexWrap(FlexWrap.WRAP);
    rvArea.setLayoutManager(layoutManager);
    rvArea.setAdapter(mAreaAdapter);
  }

  /**
   * 设置区级数据
   *
   * @param optionsDataList
   */
  public void setAreaData(List<AreaOptionsData> optionsDataList) {
    mAreaAdapter.resetPosition();
    mAreaAdapter.setData(optionsDataList);
    mAreaAdapter.notifyDataSetChanged();
  }

  public List<AreaOptionsData> getAreaData() {
    return mAreaAdapter.getData();
  }

  /**
   * 设置当前城市
   *
   * @param city
   */
  public void setCurrentCity(String city) {
    tvCurrentCity.setText(city);
  }

  private OnAreaItemClickListener mAreaItemClickListener;

  public void setOnAreaItemClickListener(OnAreaItemClickListener onAreaItemClickListener) {
    mAreaItemClickListener = onAreaItemClickListener;
  }


  public interface OnAreaItemClickListener {

    void onAreaItemClicked(int position);

    void onChangeCityClicked();
  }
}
