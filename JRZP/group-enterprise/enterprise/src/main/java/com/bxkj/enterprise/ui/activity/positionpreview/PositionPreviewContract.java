package com.bxkj.enterprise.ui.activity.positionpreview;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.PositionPreviewData;
import com.bxkj.video.data.OnlineVideoData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.positionpreview
 * @Description: PositionPreview
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface PositionPreviewContract {
  interface View extends BaseView {
    void getPositionPreviewSuccess(PositionPreviewData positionPreviewData);

    void getJobLinkVideosSuccess(List<OnlineVideoData> linkVideos);

    void noLinkVideos();
  }

  abstract class Presenter extends BaseMvpPresenter<View> {
    abstract void getPositionPreviewInfo(int positionId);

    abstract void getJobLinkVideos(int userId, int jobId);
  }
}
