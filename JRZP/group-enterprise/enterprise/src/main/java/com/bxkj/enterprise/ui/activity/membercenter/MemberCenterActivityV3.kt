package com.bxkj.enterprise.ui.activity.membercenter

import android.R.attr
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.BenefitsItem
import com.bxkj.enterprise.data.MemberBenefitsData
import com.bxkj.enterprise.data.MemberPackageData
import com.bxkj.enterprise.databinding.BActivityMemberCenterV3Binding
import com.bxkj.enterprise.ui.activity.paymentweb.PaymentWebNavigation
import net.lucode.hackware.magicindicator.FragmentContainerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator

/**
 * Description: 会员中心
 * Author:Sanjin
 * Date:2024/3/13
 **/
@Route(path = MemberCenterNavigation.PATH)
class MemberCenterActivityV3 :
  BaseDBActivity<BActivityMemberCenterV3Binding, MemberCenterViewModelV3>(), OnClickListener {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, MemberCenterActivityV3::class.java)
    }
  }

  private var listAdapter: SimpleDBListAdapter<MemberBenefitsData>? = null

  override fun getViewModelClass(): Class<MemberCenterViewModelV3> =
    MemberCenterViewModelV3::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_member_center_v3

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    setupMemberPackageListAdapter()

    setupMemberBenefitsListAdapter()
  }

  private fun setupMemberTypeIndicator(list: List<MemberPackageData>) {
    val fragmentContainerHelper = FragmentContainerHelper(viewBinding.indicatorMemberType)
    viewBinding.indicatorMemberType.apply {
      navigator = CommonNavigator(this@MemberCenterActivityV3).apply {
        isAdjustMode = true
        adapter = object : MagicIndicatorAdapter(
          list.map { it.name }.toTypedArray(),
          TabItemConfig(
            normalColor = R.color.cl_888888,
            selectedColor = R.color.common_white
          )
        ) {

          override fun getIndicator(context: Context): IPagerIndicator {
            return (super.getIndicator(context) as LinePagerIndicator).apply {
              setColors(getResColor(R.color.cl_b3ff7405))
              mode = LinePagerIndicator.MODE_MATCH_EDGE
              roundRadius = dip(10).toFloat()
              lineHeight = dip(38).toFloat()
              yOffset = dip(2).toFloat()
              xOffset = dip(2).toFloat()
            }
          }
        }.apply {
          setOnTabClickListener(object : OnTabClickListener {
            override fun onTabClicked(v: View, index: Int) {
              fragmentContainerHelper.handlePageSelected(index)
              viewModel.switchPackage(index)
            }
          })
        }
      }
    }
  }

  override fun onResume() {
    super.onResume()
    viewModel.start()
  }

  override fun onClick(v: View?) {
  }

  private fun subscribeViewModelEvent() {
    viewModel.memberPackageList.observe(this) {
      setupMemberTypeIndicator(it)
    }

    viewModel.createOrderSuccess.observe(this, EventObserver {
      PaymentWebNavigation.create(
        "${CommonApiConstants.PAYMENT_URL}AppPay/confirmPay.aspx?ids=${it}&paras=${
          AESOperator.safeEncrypt(
            UserUtils.getUserId().toString()
          )
        }"
      ).start()
    })

    viewModel.showMemberPackageList.observe(this) {
      listAdapter?.reset(it)
    }
  }

  private fun setupMemberBenefitsListAdapter() {
    viewBinding.recyclerMemberBenefits.apply {
      layoutManager = GridLayoutManager(this@MemberCenterActivityV3, 4)
      adapter = SimpleDiffListAdapter<BenefitsItem>(
        R.layout.b_recycler_member_benefits_item,
        BenefitsItem.DiffCallback()
      )
    }
  }

  private fun setupMemberPackageListAdapter() {
    listAdapter = object : SimpleDBListAdapter<MemberBenefitsData>(
      this@MemberCenterActivityV3,
      R.layout.b_recycler_member_package_item,
    ) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: MemberBenefitsData,
        position: Int
      ) {
        super.convert(holder, viewType, item, position)
        val packageName = holder.findViewById<TextView>(R.id.tv_package_name)
        packageName.background =
          GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadii = floatArrayOf(
              0f,
              0f,
              0f,
              0f,
              dip(10).toFloat(),
              dip(10).toFloat(),
              dip(10).toFloat(),
              dip(10).toFloat()
            )
            color = ColorStateList(
              arrayOf(
                intArrayOf(attr.state_selected), intArrayOf()
              ),
              intArrayOf(
                Color.parseColor("#${item.color}"),
                Color.parseColor("#d9d9d9")
              )
            )
          }
        packageName.isSelected = viewModel.equalsSelected(item)
        holder.itemView.background = GradientDrawable().apply {
          shape = GradientDrawable.RECTANGLE
          cornerRadius = dip(8).toFloat()
          color = ColorStateList(
            arrayOf(
              intArrayOf(attr.state_selected), intArrayOf()
            ),
            intArrayOf(
              getResColor(R.color.common_white),
              getResColor(R.color.common_f4f4f4)
            )
          )
          setStroke(
            dip(1), ColorStateList(
              arrayOf(
                intArrayOf(attr.state_selected), intArrayOf()
              ),
              intArrayOf(
                Color.parseColor("#${item.color}"),
                getResColor(R.color.common_f4f4f4)
              )
            )
          )
        }
        holder.itemView.isSelected = viewModel.equalsSelected(item)
      }
    }.apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          data.get(position)?.let {
            viewModel.setSelectedPackage(it)
            notifyDataSetChanged()
          }
        }
      })
    }
    viewBinding.recyclerPackageList.apply {
      layoutManager = GridLayoutManager(this@MemberCenterActivityV3, 3)
      addItemDecoration(GridItemDecoration(getResDrawable(R.drawable.divider_10)))
      adapter = listAdapter
    }
  }
}