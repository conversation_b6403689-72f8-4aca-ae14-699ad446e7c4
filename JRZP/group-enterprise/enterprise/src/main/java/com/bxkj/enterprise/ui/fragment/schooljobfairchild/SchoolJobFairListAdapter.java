package com.bxkj.enterprise.ui.fragment.schooljobfairchild;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.BR;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.SchoolJobFairItemData;
import com.bxkj.enterprise.ui.activity.jobfairregistration.JobFairRegistrationActivity;
import com.bxkj.enterprise.ui.activity.schoolrecruitdetails.ESchoolRecruitDetailsActivity;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.schooljobfairchild
 * @Description:
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolJobFairListAdapter extends SuperAdapter<SchoolJobFairItemData> {
  private int mJobFairType;

  public SchoolJobFairListAdapter(Context context, int layoutId, int jobFairType) {
    super(context, layoutId);
    mJobFairType = jobFairType;
  }

  @Override
  protected void convert(@NonNull SuperViewHolder holder, int viewType,
      SchoolJobFairItemData schoolJobFairItemData, int position) {
    holder.bind(BR.jobFairItem, schoolJobFairItemData);
    TextView tvStatus = holder.findViewById(R.id.tv_status);
    if (mJobFairType == 2) {
      tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.common_767676));
      tvStatus.setText(mContext.getResources()
          .getString(R.string.school_recruit_date_format, schoolJobFairItemData.getKsdate(),
              schoolJobFairItemData.getJsdate()));
    } else {
      switch (schoolJobFairItemData.getDateNameFlag()) {
        case 0:
          tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.common_ff4100));
          tvStatus.setText(mContext.getString(R.string.school_recruit_processing));
          break;
        case 1:
          //                tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.common_ff4100));
          //                long diffSecond = TimeUtils.getSecondDifference(Date(), TimeUtils.stringToDate("yyyy-MM-dd HH:mm:ss", schoolJobFairItemData.getKsdate()));
          //                tvStatus.startCountdown(diffSecond)
          break;
        case 2:
          tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.common_0b73de));
          tvStatus.setText(mContext.getString(R.string.tomorrow));
          break;
        case 3:
          tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.common_767676));
          tvStatus.setText(mContext.getResources()
              .getString(R.string.school_recruit_date_format, schoolJobFairItemData.getKsdate(),
                  schoolJobFairItemData.getJsdate()));
          break;
      }
    }
    holder.itemView.setOnClickListener(v -> mContext.startActivity(
        ESchoolRecruitDetailsActivity.newIntent(mContext, schoolJobFairItemData.getId())));
    holder.findViewById(R.id.tv_sign_up)
        .setOnClickListener(v -> mContext.startActivity(
            JobFairRegistrationActivity.newIntent(mContext, schoolJobFairItemData.getId())));
  }
}
