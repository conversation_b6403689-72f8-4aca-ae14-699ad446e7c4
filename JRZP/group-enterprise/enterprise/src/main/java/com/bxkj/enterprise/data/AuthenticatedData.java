package com.bxkj.enterprise.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 认证状态
 * @TODO: TODO
 * @date 2018/9/12
 */
public class AuthenticatedData {
    private int state;
    private String msg;

    public AuthenticatedData(int state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean canToCertificate() {
        return state != 30003;
    }

    public String getCertificateText() {
        String text;
        switch (state) {
            case 30002:
                text = "马上认证";
                break;
            case 30003:
                text = "确定";
                break;
            case 30004:
                text = "重新认证";
                break;
            case 30005:
                text = "重新认证";
                break;
            default:
                text = "马上认证";
                break;
        }
        return text;
    }
}
