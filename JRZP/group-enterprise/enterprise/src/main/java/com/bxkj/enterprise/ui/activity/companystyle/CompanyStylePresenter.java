package com.bxkj.enterprise.ui.activity.companystyle;


import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.CompanyStyleItemData;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.companystyle
 * @Description: CompanyStyle
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CompanyStylePresenter extends CompanyStyleContract.Presenter {

    private static final String TAG = CompanyStylePresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public CompanyStylePresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void getCompanyStyle(int userId) {
        mBusinessApi.getCompanyStyle(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getCompanyStyleSuccess((List<CompanyStyleItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() != 30002) {
                            mView.onError(respondThrowable.getErrMsg());
                        } else {
                            mView.getCompanyStyleSuccess(new ArrayList<>());
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void addCompanyStyle(int userId, String picture) {
        mBusinessApi.addCompanyStyle(userId, ECommonApiConstants.IMG_UPLOAD_PREFIX + picture)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        if (baseResponse.getIntegral()>0){
                            mView.hasIntegralReward(baseResponse.getIntegral());
                        }else {
                            mView.addCompanyStyleSuccess();
                        }
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void updateCompanyStyle(int userId, int styleId, String picture) {
        mBusinessApi.updateCompanyStyle(userId, styleId, picture)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateCompanyStyleSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void deleteCompanyStyle(int userId, int styleId) {
        mBusinessApi.deleteCompanyStyle(userId, styleId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.deleteCompanyStyleSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
