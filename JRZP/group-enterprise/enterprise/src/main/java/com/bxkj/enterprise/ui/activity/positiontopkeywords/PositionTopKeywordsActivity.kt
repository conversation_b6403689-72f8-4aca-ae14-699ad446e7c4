package com.bxkj.enterprise.ui.activity.positiontopkeywords

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.PositionTopKeywordData
import com.bxkj.enterprise.databinding.EnterpriseActivityPositionTopKeywordsBinding

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.positiontopkeywords
 * @Description:
 * <AUTHOR>
 * @date 2019/10/26
 * @version V1.0
 */
class PositionTopKeywordsActivity :
  EBaseDBActivity<EnterpriseActivityPositionTopKeywordsBinding, PositionTopKeywordsViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_TOP_CITY = "top_city"
    const val EXTRA_TOP_DAYS = "top_days"
    const val EXTRA_KEYWORDS = "keywords"
    const val RESULT_SUCCESS = Activity.RESULT_OK + 1

    fun newIntent(
      context: Context,
      topCityId: Int,
      topDays: Int,
      keywords: ArrayList<PositionTopKeywordData>? = null
    ): Intent {
      val intent = Intent(context, PositionTopKeywordsActivity::class.java)
      intent.putExtra(EXTRA_TOP_CITY, topCityId)
      intent.putExtra(EXTRA_TOP_DAYS, topDays)
      intent.putParcelableArrayListExtra(EXTRA_KEYWORDS, keywords)
      return intent
    }
  }

  private lateinit var mKeywordListAdapter: KeywordListAdapter

  override fun getViewModelClass(): Class<PositionTopKeywordsViewModel> =
    PositionTopKeywordsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_position_top_keywords

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this
    setupKeywordListAdapter()
    subscribeInitKeywordsChange()
    subscribeViewModelEvent()
    viewModel.start(intent)
  }

  private fun subscribeViewModelEvent() {
    viewModel.backCommend.observe(this, Observer {
      val backIntent = Intent()
      backIntent.putParcelableArrayListExtra(
        EXTRA_KEYWORDS,
        mKeywordListAdapter.data as ArrayList<PositionTopKeywordData>
      )
      setResult(RESULT_SUCCESS, backIntent)
      finish()
    })
  }

  override fun onClick(v: View?) {
    if (v?.id == R.id.tv_save) {
      viewModel.checkHasNoSubmitData(mKeywordListAdapter.data)
    }
  }

  private fun setupKeywordListAdapter() {
    mKeywordListAdapter = KeywordListAdapter(this, viewModel)
    viewBinding.recyclerKeyword.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerKeyword.adapter = mKeywordListAdapter
  }

  private fun subscribeInitKeywordsChange() {
    viewModel.initKeywords.observe(this, Observer {
      mKeywordListAdapter.addAll(it)
    })
  }

  override fun dispatchTouchEvent(event: MotionEvent): Boolean {
    if (event.action == MotionEvent.ACTION_DOWN) {
      val v = currentFocus
      if (v is EditText) {
        val outRect = Rect()
        v.getGlobalVisibleRect(outRect)
        if (!outRect.contains(event.rawX.toInt(), event.rawY.toInt())) {
          v.isFocusable = false
          v.isFocusableInTouchMode = true
          val imm = applicationContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
          imm.hideSoftInputFromWindow(v.windowToken, 0)
        }
      }
    }
    return super.dispatchTouchEvent(event)
  }
}
