package com.bxkj.enterprise.di;

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.di.ViewModelKey;
import com.bxkj.enterprise.ui.activity.addressmanagement.AddressManagementViewModel;
import com.bxkj.enterprise.ui.activity.adjustjobranking.AdjustJobRankingViewModel;
import com.bxkj.enterprise.ui.activity.beanmall.BeanMallViewModel;
import com.bxkj.enterprise.ui.activity.beanusagerecord.BeanUsageRecordViewModel;
import com.bxkj.enterprise.ui.activity.businesscontact.BusinessContactInfoViewModel;
import com.bxkj.enterprise.ui.activity.buyfunctionpackages.BuyFunctionPackagesViewModel;
import com.bxkj.enterprise.ui.activity.candidate.CandidateListViewModel;
import com.bxkj.enterprise.ui.activity.communicatedpeople.CommunicatedPeopleViewModel;
import com.bxkj.enterprise.ui.activity.companyhomepage.CompanyHomePageViewModel;
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoViewModel;
import com.bxkj.enterprise.ui.activity.companyinfo.searchcompany.SearchCompanyViewModel;
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentViewModel;
import com.bxkj.enterprise.ui.activity.conversation.ChatMarkViewModel;
import com.bxkj.enterprise.ui.activity.conversationreport.EConversationReportViewModel;
import com.bxkj.enterprise.ui.activity.crawlsetting.CrawlSettingViewModel;
import com.bxkj.enterprise.ui.activity.customservice.CustomServiceViewModel;
import com.bxkj.enterprise.ui.activity.editrecruitinfo.EditRecruitInfoViewModel;
import com.bxkj.enterprise.ui.activity.hredit.HREditViewModel;
import com.bxkj.enterprise.ui.activity.interviewmanagement.InterviewManagementViewModel;
import com.bxkj.enterprise.ui.activity.invitationinterview.InterviewDetailsViewModel;
import com.bxkj.enterprise.ui.activity.invitationinterview.InviteInterviewViewModel;
import com.bxkj.enterprise.ui.activity.invitationinterview.InviteInterviewViewModelV2;
import com.bxkj.enterprise.ui.activity.invitefriends.InviteFriendsViewModel;
import com.bxkj.enterprise.ui.activity.invitingdelivery.InvitationDeliveryViewModel;
import com.bxkj.enterprise.ui.activity.jobfairregistration.JobFairRegistrationViewModel;
import com.bxkj.enterprise.ui.activity.membercenter.MemberCenterViewModelV3;
import com.bxkj.enterprise.ui.activity.myvideomanagement.MyVideoManagementViewModel;
import com.bxkj.enterprise.ui.activity.news.NewsViewModel;
import com.bxkj.enterprise.ui.activity.positionpreview.JobPreviewViewModel;
import com.bxkj.enterprise.ui.activity.positiontop.PostToTopViewModel;
import com.bxkj.enterprise.ui.activity.positiontopkeywords.PositionTopKeywordsViewModel;
import com.bxkj.enterprise.ui.activity.postjob.PostJobV2ViewModel;
import com.bxkj.enterprise.ui.activity.postjob.SalaryPickerViewModel;
import com.bxkj.enterprise.ui.activity.receivedresumelist.ReceivedResumeViewModel;
import com.bxkj.enterprise.ui.activity.rechargeintegral.RechargeIntegralViewModel;
import com.bxkj.enterprise.ui.activity.recruitmentdata.RecruitmentDataViewModel;
import com.bxkj.enterprise.ui.activity.reportreason.EnterpriseReportReasonViewModel;
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailViewModel;
import com.bxkj.enterprise.ui.activity.resumedownload.MVResumeDownloadViewModel;
import com.bxkj.enterprise.ui.activity.schoolhome.SchoolHomeViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitdetails.SchoolRecruitDetailsViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruiteditor.SchoolRecruitEditorViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.SchoolRecruitJobViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.cityselect.CitySelectViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.editor.SchoolRecruitJobEditorViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitlist.SchoolRecruitListViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitmanagement.SchoolRecruitManagementViewModel;
import com.bxkj.enterprise.ui.activity.schoolrecruitresume.SchoolRecruitResumeViewModel;
import com.bxkj.enterprise.ui.activity.searchresumeresult.SearchResumeResultV2ViewModel;
import com.bxkj.enterprise.ui.activity.seenmeapplicant.SeenMeApplicantViewModel;
import com.bxkj.enterprise.ui.activity.selectaddressbymap.SelectAddressByMapViewModel;
import com.bxkj.enterprise.ui.activity.selectrealtejob.SelectRelateJobViewModel;
import com.bxkj.enterprise.ui.activity.selectsayhellojob.SelectSayHelloJobViewModel;
import com.bxkj.enterprise.ui.activity.sharejob.ShareJobViewModel;
import com.bxkj.enterprise.ui.activity.signupuser.SignUpUserViewModel;
import com.bxkj.enterprise.ui.activity.systemmsg.SystemMsgViewModel;
import com.bxkj.enterprise.ui.activity.udpatephonenumber.UpdatePhoneNumberViewModel;
import com.bxkj.enterprise.ui.activity.videosignupmsg.VideoSignUpMsgViewModel;
import com.bxkj.enterprise.ui.activity.welfare.WelfareViewModel;
import com.bxkj.enterprise.ui.fragment.collection.FavoritesResumeListViewModel;
import com.bxkj.enterprise.ui.fragment.conversationmsg.BusinessContactListViewModel;
import com.bxkj.enterprise.ui.fragment.conversationmsg.BusinessContactViewModel;
import com.bxkj.enterprise.ui.fragment.conversationmsg.ConversationMsgViewModel;
import com.bxkj.enterprise.ui.fragment.facultyrecruit.FacultyRecruitViewModel;
import com.bxkj.enterprise.ui.fragment.homev2.BusinessHomeViewModelV3;
import com.bxkj.enterprise.ui.fragment.inviterecord.InviteRecordViewModel;
import com.bxkj.enterprise.ui.fragment.message.MessageViewModel;
import com.bxkj.enterprise.ui.fragment.receivedresume.ReceivedResumeListViewModelV2;
import com.bxkj.enterprise.ui.fragment.recommendresume.LatestResumeViewModel;
import com.bxkj.enterprise.ui.fragment.recommendresume.RecommendResumeViewModelV2;
import com.bxkj.enterprise.ui.fragment.resumelist.ResumeListViewModelV3;
import com.bxkj.enterprise.ui.fragment.schooldetails.SchoolDetailsViewModel;
import com.bxkj.enterprise.ui.fragment.schooljobfair.SchoolJobFairViewModel;
import com.bxkj.enterprise.ui.fragment.schooljobfairchild.SchoolJobFairChildViewModel;
import com.bxkj.enterprise.ui.fragment.schoolrecruitlist.SchoolRecruitListV2ViewModel;
import com.bxkj.enterprise.ui.fragment.signupuser.SignUpUserChildViewModel;
import com.bxkj.enterprise.ui.fragment.talentpool.TalentPoolViewModel;
import com.bxkj.enterprise.weight.hrlist.HRListDialogViewModel;
import com.bxkj.enterprise.weight.sendsms.SendSmsViewModel;
import com.bxkj.jrzp.support.chat.widget.selectjob.JobListDialogViewModel;
import dagger.Binds;
import dagger.Module;
import dagger.multibindings.IntoMap;

/**
 * @Description:
 * @TODO: TODO
 */
@Module
public abstract class EnterpriseVMModule {

  @Binds
  @IntoMap
  @ViewModelKey(ApplicantResumeDetailViewModel.class)
  abstract BaseViewModel bindApplicantResumeDetailViewModel(
    ApplicantResumeDetailViewModel applicantResumeDetailViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PostToTopViewModel.class)
  abstract BaseViewModel bindPositionTopViewModel(PostToTopViewModel postToTopViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobPreviewViewModel.class)
  abstract BaseViewModel bindJobPreviewViewModel(JobPreviewViewModel jobPreviewViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecruitmentDataViewModel.class)
  abstract BaseViewModel bindRecruitmentDataViewModel(
    RecruitmentDataViewModel recruitmentDataViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FavoritesResumeListViewModel.class)
  abstract BaseViewModel bindFavoritesResumeListViewModel(
    FavoritesResumeListViewModel favoritesResumeListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MemberCenterViewModelV3.class)
  abstract BaseViewModel bindMemberCenterViewModelV3(
    MemberCenterViewModelV3 memberCenterViewModelV3);

  @Binds
  @IntoMap
  @ViewModelKey(CommunicatedPeopleViewModel.class)
  abstract BaseViewModel bindCommunicatedPeopleViewModel(
    CommunicatedPeopleViewModel communicatedPeopleViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AdjustJobRankingViewModel.class)
  abstract BaseViewModel bindAdjustJobRankingViewModel(
    AdjustJobRankingViewModel adjustJobRankingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ResumeListViewModelV3.class)
  abstract BaseViewModel bindResumeListViewModelV3(ResumeListViewModelV3 resumeListViewModelV3);

  @Binds
  @IntoMap
  @ViewModelKey(CandidateListViewModel.class)
  abstract BaseViewModel bindCandidateListViewModel(CandidateListViewModel candidateListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BeanUsageRecordViewModel.class)
  abstract BaseViewModel bindBeanUsageRecordViewModel(
    BeanUsageRecordViewModel beanUsageRecordViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BuyFunctionPackagesViewModel.class)
  abstract BaseViewModel bindBuyFunctionPackagesViewModel(
    BuyFunctionPackagesViewModel buyFunctionPackagesViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CustomServiceViewModel.class)
  abstract BaseViewModel bindCustomServiceViewModel(CustomServiceViewModel customServiceViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BeanMallViewModel.class)
  abstract BaseViewModel bindBeanMallViewModel(BeanMallViewModel beanMallViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ReceivedResumeViewModel.class)
  abstract BaseViewModel bindReceivedResumeViewModel(
    ReceivedResumeViewModel receivedResumeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ReceivedResumeListViewModelV2.class)
  abstract BaseViewModel bindReceivedResumeListViewModelV2(
    ReceivedResumeListViewModelV2 receivedResumeListViewModelV2);

  @Binds
  @IntoMap
  @ViewModelKey(UpdatePhoneNumberViewModel.class)
  abstract BaseViewModel bindUpdatePhoneNumberViewModel(
    UpdatePhoneNumberViewModel updatePhoneNumberViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessContactInfoViewModel.class)
  abstract BaseViewModel bindBusinessContactInfoViewModel(
    BusinessContactInfoViewModel businessContactInfoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessContactViewModel.class)
  abstract BaseViewModel bindBusinessContactViewModel(
    BusinessContactViewModel businessContactViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SendSmsViewModel.class)
  abstract BaseViewModel bindSendSmsViewModel(SendSmsViewModel sendSmsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InterviewManagementViewModel.class)
  abstract BaseViewModel bindInterviewManagementViewModel(
    InterviewManagementViewModel interviewManagementViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ChatMarkViewModel.class)
  abstract BaseViewModel chatMarkViewModel(ChatMarkViewModel chatMarkViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InterviewDetailsViewModel.class)
  abstract BaseViewModel bindInterviewDetailsViewModel(
    InterviewDetailsViewModel interviewDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HRListDialogViewModel.class)
  abstract BaseViewModel bindHRListDialogViewModel(HRListDialogViewModel hrListDialogViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobListDialogViewModel.class)
  abstract BaseViewModel bindJobListDialogViewModel(JobListDialogViewModel jobListDialogViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InviteInterviewViewModelV2.class)
  abstract BaseViewModel bindInviteInterviewViewModelV2(
    InviteInterviewViewModelV2 inviteInterviewViewModelV2);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessContactListViewModel.class)
  abstract BaseViewModel bindConversationViewModelV2(
    BusinessContactListViewModel businessContactListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CompanyHomePageViewModel.class)
  abstract BaseViewModel bindCompanyHomePageViewModel(
    CompanyHomePageViewModel companyHomePageViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(TalentPoolViewModel.class)
  abstract BaseViewModel bindTalentPoolViewModel(TalentPoolViewModel talentPoolViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InvitationDeliveryViewModel.class)
  abstract BaseViewModel bindInvitationDeliveryViewModel(
    InvitationDeliveryViewModel invitationDeliveryViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessBasicInfoViewModel.class)
  abstract BaseViewModel bindCompanyInfoViewModelV2(
    BusinessBasicInfoViewModel businessBasicInfoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HREditViewModel.class)
  abstract BaseViewModel bindHREditViewModel(HREditViewModel hrEditViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(LatestResumeViewModel.class)
  abstract BaseViewModel bindLatestResumeViewModel(LatestResumeViewModel latestResumeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecommendResumeViewModelV2.class)
  abstract BaseViewModel bindRecommendResumeViewModelV2(
    RecommendResumeViewModelV2 recommendResumeViewModelV2);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessHomeViewModelV3.class)
  abstract BaseViewModel bindEnterpriseHomeV3ViewModel(
    BusinessHomeViewModelV3 businessHomeViewModelV3);

  @Binds
  @IntoMap
  @ViewModelKey(SelectAddressByMapViewModel.class)
  abstract BaseViewModel bindSelectAddressByMapViewModel(
    SelectAddressByMapViewModel selectAddressByMapViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AddressManagementViewModel.class)
  abstract BaseViewModel bindAddressManagementViewModel(
    AddressManagementViewModel addressManagementViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CrawlSettingViewModel.class)
  abstract BaseViewModel bindCrawlSettingViewModel(CrawlSettingViewModel crawlSettingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchResumeResultV2ViewModel.class)
  abstract BaseViewModel bindSearchResumeResultViewModel(
    SearchResumeResultV2ViewModel searchResumeResultV2ViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SalaryPickerViewModel.class)
  abstract BaseViewModel bindSalaryPickerViewModel(SalaryPickerViewModel salaryPickerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PostJobV2ViewModel.class)
  abstract BaseViewModel bindPostJobV2ViewModel(PostJobV2ViewModel postJobV2ViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CitySelectViewModel.class)
  abstract BaseViewModel bindCitySelectViewModel(CitySelectViewModel citySelectViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitJobEditorViewModel.class)
  abstract BaseViewModel bindSchoolRecruitJobEditorViewModel(
    SchoolRecruitJobEditorViewModel schoolRecruitJobEditorViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitJobViewModel.class)
  abstract BaseViewModel bindSchoolRecruitJobViewModel(
    SchoolRecruitJobViewModel schoolRecruitJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitEditorViewModel.class)
  abstract BaseViewModel bindSchoolRecruitEditorViewModel(
    SchoolRecruitEditorViewModel schoolRecruitEditorViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitResumeViewModel.class)
  abstract BaseViewModel bindSchoolRecruitResumeViewModel(
    SchoolRecruitResumeViewModel schoolRecruitResumeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitListV2ViewModel.class)
  abstract BaseViewModel bindSchoolRecruitListV2ViewModel(
    SchoolRecruitListV2ViewModel schoolRecruitListV2ViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitManagementViewModel.class)
  abstract BaseViewModel bindSchoolRecruitManagementViewModel(
    SchoolRecruitManagementViewModel schoolRecruitManagementViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MVResumeDownloadViewModel.class)
  abstract BaseViewModel bindMVResumeDownloadViewModel(
    MVResumeDownloadViewModel mvResumeDownloadViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitListViewModel.class)
  abstract BaseViewModel bindSchoolRecruitListViewModel(
    SchoolRecruitListViewModel schoolRecruitListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitDetailsViewModel.class)
  abstract BaseViewModel bindSchoolRecruitDetailsViewModel(
    SchoolRecruitDetailsViewModel schoolRecruitDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolHomeViewModel.class)
  abstract BaseViewModel bindSchoolHomeViewModel(SchoolHomeViewModel schoolHomeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolDetailsViewModel.class)
  abstract BaseViewModel bindSchoolDetailsViewModel(SchoolDetailsViewModel schoolDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolJobFairViewModel.class)
  abstract BaseViewModel bindSchoolJobFairViewModel(SchoolJobFairViewModel schoolJobFairViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FacultyRecruitViewModel.class)
  abstract BaseViewModel bindFacultyRecruitViewModel(
    FacultyRecruitViewModel facultyRecruitViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolJobFairChildViewModel.class)
  abstract BaseViewModel bindSchoolJobFairChildViewModel(
    SchoolJobFairChildViewModel schoolJobFairChildViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobFairRegistrationViewModel.class)
  abstract BaseViewModel bindJobFairRegistrationViewModel(
    JobFairRegistrationViewModel jobFairRegistrationViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(EditRecruitInfoViewModel.class)
  abstract BaseViewModel bindEditRecruitInfoViewModel(
    EditRecruitInfoViewModel editRecruitInfoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(NewsViewModel.class)
  abstract BaseViewModel bindNewsViewModel(NewsViewModel newsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InviteFriendsViewModel.class)
  abstract BaseViewModel bindInviteFriendsViewModel(InviteFriendsViewModel inviteFriendsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MessageViewModel.class)
  abstract BaseViewModel bindMessageViewModel(MessageViewModel messageViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RechargeIntegralViewModel.class)
  abstract BaseViewModel bindRechageIntegralViewModel(
    RechargeIntegralViewModel rechargeIntegralViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InviteRecordViewModel.class)
  abstract BaseViewModel bindInviteRecordViewModel(InviteRecordViewModel inviteRecordViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(WelfareViewModel.class)
  abstract BaseViewModel bindWelfareViewModel(WelfareViewModel welfareViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PositionTopKeywordsViewModel.class)
  abstract BaseViewModel bindPositionTopKeywordsViewModel(
    PositionTopKeywordsViewModel positionTopKeywordsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ConversationMsgViewModel.class)
  abstract BaseViewModel bindConversationMsgViewModel(
    ConversationMsgViewModel conversationMsgViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(VideoSignUpMsgViewModel.class)
  abstract BaseViewModel bindVideoSignUpMsgViewModel(
    VideoSignUpMsgViewModel videoSignUpMsgViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SignUpUserViewModel.class)
  abstract BaseViewModel bindSignUpUserViewModel(SignUpUserViewModel signUpUserViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SignUpUserChildViewModel.class)
  abstract BaseViewModel bindSignUpUserChildViewModel(
    SignUpUserChildViewModel signUpUserChildViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BusinessChatContentViewModel.class)
  abstract BaseViewModel bindConversationViewModel(BusinessChatContentViewModel viewModel);

  @Binds
  @IntoMap
  @ViewModelKey(EConversationReportViewModel.class)
  abstract BaseViewModel bindConversationReportViewModel(
    EConversationReportViewModel conversationReportViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(EnterpriseReportReasonViewModel.class)
  abstract BaseViewModel bindReportReasonViewModel(
    EnterpriseReportReasonViewModel enterpriseReportReasonViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelectSayHelloJobViewModel.class)
  abstract BaseViewModel bindSelectSayHelloJobViewModel(
    SelectSayHelloJobViewModel selectSayHelloJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SeenMeApplicantViewModel.class)
  abstract BaseViewModel bindSeenMeApplicantViewModel(
    SeenMeApplicantViewModel seenMeApplicantViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SystemMsgViewModel.class)
  abstract BaseViewModel bindSystemMsgViewModel(SystemMsgViewModel systemMsgViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelectRelateJobViewModel.class)
  abstract BaseViewModel bindSelectVideoLinkViewModel(
    SelectRelateJobViewModel selectVideoLinkJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyVideoManagementViewModel.class)
  abstract BaseViewModel bindMyVideoManagementViewModel(
    MyVideoManagementViewModel myVideoManagementViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchCompanyViewModel.class)
  abstract BaseViewModel bindSearchCompanyViewModel(SearchCompanyViewModel searchCompanyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ShareJobViewModel.class)
  abstract BaseViewModel bindShareJobViewModel(ShareJobViewModel shareJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InviteInterviewViewModel.class)
  abstract BaseViewModel bindInviteInterviewViewModel(
    InviteInterviewViewModel inviteInterviewViewModel);
}
