package com.bxkj.enterprise.ui.fragment.facultyrecruit;

import android.app.Application;
import androidx.lifecycle.MutableLiveData;
import android.os.Bundle;
import androidx.annotation.NonNull;

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.callback.ResultListCallBack;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.data.FacultyRecruitItemData;
import com.bxkj.enterprise.data.FacultyTypeItemData;
import com.bxkj.enterprise.data.source.SchoolDetailsRepo;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.facultyrecruit
 * @Description:
 * @TODO: TODO
 * @date 2019/6/14
 */
public class FacultyRecruitViewModel extends BaseViewModel {

    @Inject
    SchoolDetailsRepo mSchoolDetailsRepo;

    private RefreshListViewModel mListViewModel;
    private MutableLiveData<List<FacultyTypeItemData>> mFacultyTypeListData = new MutableLiveData<>();
    private int mSchoolId, mFacultyTypeId;

    @Inject
    public FacultyRecruitViewModel(@NotNull Application application) {
        super();
        setupListViewModel();
    }

    private void setupListViewModel() {
        mListViewModel = new RefreshListViewModel();
        mListViewModel.getRefreshLayoutViewModel().enableRefresh(false);
        mListViewModel.setOnLoadDataListener(currentPage ->
                mSchoolDetailsRepo.getFacultyRecruitList(mSchoolId, mFacultyTypeId, "", currentPage
                        , ECommonApiConstants.DEFAULT_PAGE_SIZE, new ResultListCallBack<List<FacultyRecruitItemData>>() {
                            @Override
                            public void onSuccess(List<FacultyRecruitItemData> data) {
                                if (currentPage == 1) {
                                    mListViewModel.reset(data);
                                } else {
                                    mListViewModel.addAll(data);
                                }
                            }

                            @Override
                            public void onNoMoreData() {
                                mListViewModel.noMoreData();
                            }

                            @Override
                            public void onError(RespondThrowable respondThrowable) {
                                mListViewModel.loadError();
                            }
                        }));
    }

    /**
     * 获取教职工招聘类型列表
     */
    public void getFacultyTypeList() {
        mSchoolDetailsRepo.getFacultyTypeList(new ResultDataCallBack<List<FacultyTypeItemData>>() {
            @Override
            public void onSuccess(List<FacultyTypeItemData> data) {
                data.add(0,new FacultyTypeItemData(0,"全部"));
                mFacultyTypeListData.setValue(data);
                refreshFacultyRecruitListByTypeId(data.get(0).getId());
            }

            @Override
            public void onError(@NonNull RespondThrowable respondThrowable) {
                showToast(respondThrowable.getErrMsg());
            }
        });
    }

    public void setSchoolId(Bundle bundle) {
        mSchoolId = bundle.getInt(FacultyRecruitFragment.EXTRA_SCHOOL_ID);
    }

    /**
     * 根据类型id获取教职工招聘列表
     * @param typeId
     */
    public void refreshFacultyRecruitListByTypeId(int typeId) {
        mFacultyTypeId = typeId;
        mListViewModel.refresh();
    }

    public MutableLiveData<List<FacultyTypeItemData>> getFacultyTypeListData() {
        return mFacultyTypeListData;
    }

    public RefreshListViewModel getListViewModel() {
        return mListViewModel;
    }
}
