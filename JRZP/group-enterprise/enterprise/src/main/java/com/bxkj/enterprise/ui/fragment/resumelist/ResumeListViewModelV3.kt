package com.bxkj.enterprise.ui.fragment.resumelist

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.enterprise.R
import com.bxkj.enterprise.api.parameters.SearchResumeParameters
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.source.ResumeRepo
import com.bxkj.enterprise.data.ResumeItemDataV2
import com.bxkj.jrzp.user.repository.OpenUserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

class ResumeListViewModelV3 @Inject constructor(
  private val _resumeRepo: ResumeRepo,
  private val _openUserRepo: OpenUserRepository
) : BaseViewModel() {

  private val _selectedResume = ArrayList<ResumeItemDataV2>()
  private var _searchResumeParams = SearchResumeParameters()
  val inviteBalance = MutableLiveData("剩余0条")
  val selectedCount = MutableLiveData(0)
  val resumeCount = MutableLiveData(0)
  val allSelected = MutableLiveData(false)

  val showNoInviteBalanceTipsCommand = MutableLiveData<VMEvent<String>>()

  val showNoMemberPermissionTipsCommand = MutableLiveData<VMEvent<String>>()

  val refreshSelectedStatusCommand = MutableLiveData<VMEvent<Unit>>()

  val toInviteCommand = MutableLiveData<VMEvent<List<ResumeItemDataV2>>>()

  val resumeListViewModel = RefreshListViewModel()

  init {
    resumeListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        _resumeRepo.getResumeList(
          getSelfUserID(), _searchResumeParams.apply {
            city = UserUtils.getUserSelectedCityId()
          },
          currentPage, 16
        ).handleResult({
          if (it.isNullOrEmpty()) {
            resumeListViewModel.noMoreData()
          } else {
            if (currentPage == 1) {
              _selectedResume.clear()
            }
            resumeListViewModel.autoAddAll(it)
          }
        }, {
          if (it.isNoDataError) {
            resumeListViewModel.noMoreData()
          } else {
            resumeListViewModel.loadError()
          }
        }, {
          setResumeCount(resumeListViewModel.childCount)
        })
      }
    }
    refreshInviteBalance()
  }

  fun start() {
    resumeListViewModel.refresh()
  }

  fun selectResume(resume: ResumeItemDataV2) {
    if (_selectedResume.contains(resume)) {
      _selectedResume.remove(resume)
    } else {
      _selectedResume.add(resume)
    }
    refreshSelectedState()
  }

  fun hasSelected(resume: ResumeItemDataV2): Boolean {
    return _selectedResume.contains(resume)
  }

  fun selectedAll(items: List<ResumeItemDataV2>) {
    allSelected.value = allSelected.value?.not()
    _selectedResume.clear()
    if (allSelected.value == true) {
      _selectedResume.addAll(items)
    }
    refreshSelectedState()
  }

  fun multiInvite() {
    if (_selectedResume.isEmpty()) {
      showToast(R.string.enterprise_talent_pool_selected_empty_tips)
    } else {
      toInviteCommand.value = VMEvent(_selectedResume)
    }
  }

  fun inviteSendResume(job: PositionItemBean, item: ResumeItemDataV2) {
    viewModelScope.launch {
      showLoading()
      _resumeRepo.sendSayHelloMsg(getSelfUserID(), job.id, item.id.toString(), "你好")
        .handleResult({
          it?.let {
            showToast(it.msg)
          }
          if (resumeListViewModel.remove(item) == 0) {
            resumeListViewModel.refresh()
          }
          setResumeCount(resumeListViewModel.childCount)
          handleInviteSuccess()
        }, {
          when (it.errCode) {
            30004, 30007 -> {
              showNoMemberPermissionTipsCommand.value = VMEvent(it.errMsg)
            }

            30005, 30008 -> {
              showNoInviteBalanceTipsCommand.value = VMEvent(it.errMsg)
            }

            else -> {
              showToast(it.errMsg)
            }
          }
        }, {
          hideLoading()
        })
    }
  }

  fun inviteSendResume(job: PositionItemBean, resumeIds: String) {
    viewModelScope.launch {
      showLoading()
      _resumeRepo.sendSayHelloMsg(getSelfUserID(), job.id, resumeIds, "你好")
        .handleResult({
          it?.let {
            showToast(it.msg)
          }
          handleInviteSuccess()
          resumeListViewModel.refresh()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun setupFilterParams(filterParams: SearchResumeParameters) {
    _searchResumeParams = filterParams
  }

  fun handleInviteSuccess() {
    resetResumeSelectState()
    refreshInviteBalance()
  }

  fun refreshInviteBalance() {
    viewModelScope.launch {
      showLoading()
      _openUserRepo.getSayHelloBalance(getSelfUserID())
        .handleResult({
          it?.let {
            if (it.isVip()) {
              inviteBalance.value = "今日剩余${it.ZhiliaoCount}条"
            } else {
              inviteBalance.value = "剩余${it.ZhiliaoCount}条"
            }
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  private fun resetResumeSelectState() {
    _selectedResume.clear()
    refreshSelectedState()
  }

  private fun setResumeCount(itemCount: Int? = 0) {
    resumeCount.value = itemCount
    refreshSelectedState()
  }

  private fun refreshSelectedState() {
    selectedCount.value = _selectedResume.size
    allSelected.value = selectedCount.value == resumeCount.value
    refreshSelectedStatusCommand.value = VMEvent(Unit)
  }
}