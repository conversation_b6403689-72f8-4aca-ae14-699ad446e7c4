package com.bxkj.enterprise.ui.activity.invitationinterview

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.picker.PickerUtils
import com.bxkj.common.util.qmui.QMUIKeyboardHelper
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxBus.Message
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityInviteInterviewBinding
import com.bxkj.enterprise.ui.activity.interviewremark.InterviewRemarkActivity
import java.util.Calendar
import java.util.Date

/**
 *  邀请面试
 * @author: YangXin
 * @date: 2021/3/16
 */
//@Route(path = InviteInterviewNavigation.PATH)
class InviteInterviewActivity :
    BaseDBActivity<EnterpriseActivityInviteInterviewBinding, InviteInterviewViewModel>(),
    OnClickListener {

    companion object {
        const val TO_EDIT_REMARK_CODE = 1
    }

    override fun getViewModelClass(): Class<InviteInterviewViewModel> =
        InviteInterviewViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_invite_interview

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        subscribeViewModelEvent()

        viewModel.start(
            getIntentJobId(),
            getIntentResumeId(),
            getIntentInviteUserName(),
            getIntentInterviewType()
        )

        if (getIntentInterviewType() == InviteInterviewNavigation.INTERVIEW_VIDEO) {
            viewBinding.clvAddress.visibility = View.GONE
            viewBinding.clvRemark.visibility = View.GONE
            viewBinding.tvMsg.text = getString(R.string.invitation_video_interview_msg)
        }
    }

    private fun getIntentInterviewType(): Int {
        return intent.getIntExtra(
            InviteInterviewNavigation.EXTRA_INTERVIEW_METHOD,
            InviteInterviewNavigation.INTERVIEW_NORMAL
        )
    }

    private fun subscribeViewModelEvent() {
        viewModel.inviteSuccessEvent.observe(this, Observer {
            showToast(getString(R.string.enterprise_invite_interview_success))
            RxBus.get().post(Message.fromCode(RxMsgCode.ACTION_CHAT_INVITE_SUCCESS))
            setResult(RESULT_OK)
            finish()
        })
    }

    private fun getIntentInviteUserName(): String {
        return intent.getStringExtra(InviteInterviewNavigation.EXTRA_INVITE_USER_NAME).getOrDefault()
    }

    private fun getIntentResumeId(): Int {
        return intent.getIntExtra(InviteInterviewNavigation.EXTRA_RESUME_ID, 0)
    }

    private fun getIntentJobId(): Int {
        return intent.getIntExtra(InviteInterviewNavigation.EXTRA_JOB_ID, 0)
    }

    override fun onClick(v: View?) {
        if (v != null) {
            QMUIKeyboardHelper.hideKeyboard(v)
            when (v.id) {
                R.id.clv_interview_time -> {
                    PickerUtils.applyMyConfig(TimePickerBuilder(
                        this
                    ) { date: Date?, _: View? ->
                        viewModel.setupInterviewTime(
                            TimeUtils.formatDate(
                                date,
                                "yyyy-MM-dd HH:mm"
                            )
                        )
                    })
                        .setType(booleanArrayOf(true, true, true, true, true, false))
                        .setRangDate(
                            Calendar.getInstance(),
                            TimeUtils.getDateOfBeforeOrAfterNMonth(6)
                        )
                        .isCenterLabel(true)
                        .build().show()
                }
                R.id.tv_change_contract_way -> {
                    val smsNoticeOptions = arrayOf("手机", "电话")
                    MenuPopup.Builder(this)
                        .setData(smsNoticeOptions)
                        .setOnItemClickListener { _, position ->
                            viewModel.switchContactMethod(position)
                            viewBinding.tvChangeContractWay.text = smsNoticeOptions[position]
                        }.build().show()
                }
                R.id.clv_remark -> {
                    startActivityForResult(
                        InterviewRemarkActivity.newIntent(
                            this,
                            viewModel.getSelectedRemarkTips(),
                            viewModel.getOtherRemark()
                        ),
                        TO_EDIT_REMARK_CODE
                    )
                }
                R.id.clv_sms_notice -> {
                    val smsNoticeOptions = arrayOf("是", "否")
                    MenuPopup.Builder(this)
                        .setData(smsNoticeOptions)
                        .setOnItemClickListener { _, position ->
                            viewModel.openSmsNotice(position == 0)
                            viewBinding.clvSmsNotice.setContent(smsNoticeOptions[position])
                        }.build().show()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        viewModel.handleActivityResult(requestCode, resultCode, data)
    }

}