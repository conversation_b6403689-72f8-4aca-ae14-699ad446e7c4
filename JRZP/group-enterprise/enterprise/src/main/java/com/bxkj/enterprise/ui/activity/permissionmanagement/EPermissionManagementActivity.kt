package com.bxkj.enterprise.ui.activity.permissionmanagement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.SystemUtil
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.id
import com.bxkj.enterprise.R.layout
import com.bxkj.enterprise.databinding.ActivityPermissionManagementBinding
import com.bxkj.enterprise.ui.activity.permissionmanagement.PermissionItem.DiffCallBack
import com.bxkj.enterprise.ui.activity.web.WebActivity
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions

/**
 * @Project: gzgk
 * @Description: 权限设置
 * @author:45457
 * @date: 2020/7/3
 * @version: V1.0
 */
class EPermissionManagementActivity :
  EBaseDBActivity<ActivityPermissionManagementBinding, BaseViewModel>() {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, EPermissionManagementActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_permission_management

  override fun initPage(savedInstanceState: Bundle?) {
    setupPermissionList()
    viewBinding.list = getPermissionList()
  }

  private fun getPermissionList(): List<PermissionItem> {
    return listOf(
      PermissionItem.create(
        "允许今日招聘访问位置信息",
        "查看详细位置权限信息使用规则",
        "https://jrzpapi2.jdzj.com/page/weizhi.html",
        XXPermissions.isGranted(this, Permission.ACCESS_FINE_LOCATION)
      ),
      PermissionItem.create(
        "允许今日招聘使用相机功能",
        "查看详细相机功能使用规则",
        "https://jrzpapi2.jdzj.com/page/xiangji.html",
        XXPermissions.isGranted(this, Permission.CAMERA)
      ),
      PermissionItem.create(
        "允许今日招聘获取设备状态权限",
        "查看详细获取设备状态权限使用规则",
        "https://jrzpapi2.jdzj.com/page/shebei.html",
        XXPermissions.isGranted(this, Permission.READ_PHONE_STATE)
      ),
      PermissionItem.create(
        "允许今日招聘获取文件储存和访问权限",
        "查看详细获取文件储存和访问权限使用规则",
        "https://jrzpapi2.jdzj.com/page/wenjian.html",
        XXPermissions.isGranted(
          this,
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE
        )
      )
    )
  }

  private fun setupPermissionList() {
    val permissionListAdapter =
      SimpleDiffListAdapter(
        layout.enterprise_recyceler_permission_item,
        DiffCallBack()
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getData()?.let {
              if (v.id == id.tv_desc) {
                startActivity(
                  WebActivity.newIntent(
                    this@EPermissionManagementActivity,
                    "权限说明",
                    it[position].permissionDescUrl
                  )
                )
              } else {
                SystemUtil.toSettingPage(this@EPermissionManagementActivity)
              }
            }
          }
        }, R.id.tv_desc)
      }
    viewBinding.recyclerPermissionList.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerPermissionList.adapter = permissionListAdapter
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewBinding.list = getPermissionList()
  }
}