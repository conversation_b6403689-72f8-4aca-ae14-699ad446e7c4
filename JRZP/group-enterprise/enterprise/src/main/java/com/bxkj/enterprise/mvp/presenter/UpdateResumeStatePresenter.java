package com.bxkj.enterprise.mvp.presenter;


import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.mvp.contract.UpdateResumeStateContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.presenter
 * @Description: UpdateResumeState
 * @TODO: TODO
 * @date 2018/3/27
 */

public class UpdateResumeStatePresenter extends UpdateResumeStateContract.Presenter {

    private static final String TAG = UpdateResumeStatePresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public UpdateResumeStatePresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void updateResumeState(int userId, int positionId, int resumeId, int state, String reason) {
        mBusinessApi.updateResumeStates(userId, positionId, resumeId, state, reason)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
