package com.bxkj.enterprise.ui.activity.sharejob

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.data.RecommendResumeData
import com.bxkj.enterprise.data.source.AccountInfoRepo
import com.bxkj.enterprise.data.source.ResumeRepo
import com.bxkj.jrzp.user.repository.OpenUserRepository
import kotlinx.coroutines.launch
import java.lang.StringBuilder
import javax.inject.Inject

/**
 * @Description: 分享职位
 * @author: YangXin
 * @date: 2021/1/5
 * @version: V1.0
 */
class ShareJobViewModel @Inject constructor(
  private val mResumeRepo: ResumeRepo,
  private val mAccountInfoRepo: AccountInfoRepo,
  private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

  val showShareDialogCommand =
    LiveEvent<ShareInfoData>()
  val inviteSuccessEvent = LiveEvent<Void>()
  val inviteFailedEvent = LiveEvent<Void>()
  val inviteBalanceNotEnoughEvent = MutableLiveData<VMEvent<String>>()
  val inviteBalance = MutableLiveData<Int>()

  val recommendResumeList = MutableLiveData<List<RecommendResumeData>>()

  private val _selectedResumeList = ArrayList<RecommendResumeData>()
  val selectResumeList = MutableLiveData<List<RecommendResumeData>>()

  private var mJobId: Int = 0
  private var mJobTypeId: Int = 0
  private var mJobName: String = ""
  private var mRecommendResumeIds: String = ""

  fun start(jobId: Int, jobTypeId: Int, jobName: String) {
    mJobId = jobId
    mJobTypeId = jobTypeId
    mJobName = jobName
    refreshRecommendResumeList()
    refreshInviteBalance()
  }

  private fun refreshInviteBalance() {
    viewModelScope.launch {
      mOpenUserRepository.getUserInviteBalance(getSelfUserID())
        .handleResult({
          inviteBalance.value = it?.yaoyueCount.getOrDefault()
        })
    }
  }

  fun refreshRecommendResumeList() {
    showLoading()

    _selectedResumeList.clear()
    notifySelectResumeChange()

    viewModelScope.launch {
      mResumeRepo.getRecommendResumeByJobId(
        getSelfUserID(),
        mJobId,
        mJobTypeId,
        mRecommendResumeIds
      ).handleResult({
        it?.let {
          mRecommendResumeIds = it.resIds
          recommendResumeList.value = ArrayList(it.data)
          allSelect()
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  fun getShareInfo() {
    showLoading()
    viewModelScope.launch {
      mAccountInfoRepo.getShareContent(CommonApiConstants.SHARE_JOB, 0, mJobId)
        .handleResult({
          it?.let {
            it.shareUrl = "http://m.jrzp.com/jobfenxiang/view.aspx?relID=$mJobId"
            showShareDialogCommand.value = it
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun selectItem(item: RecommendResumeData) {
    if (_selectedResumeList.contains(item)) {
      _selectedResumeList.remove(item)
    } else {
      _selectedResumeList.add(item)
    }
    notifySelectResumeChange()
  }

  fun getSelectedResume(): ArrayList<RecommendResumeData> {
    return _selectedResumeList
  }

  fun allSelect() {
    recommendResumeList.value?.let { resumeList ->
      if (_selectedResumeList.size != resumeList.size) {
        _selectedResumeList.clear()
        _selectedResumeList.addAll(resumeList)
      } else {
        _selectedResumeList.clear()
      }
      notifySelectResumeChange()
    }
  }

  fun invite() {
    viewModelScope.launch {
      showLoading()
      mResumeRepo.inviteMultipleResume(
        getSelfUserID(),
        mJobId,
        getSelectedResumeIDs(),
        "你好,本公司正在诚招${mJobName}"
      ).handleResult({
        inviteSuccessEvent.call()
      }, {
        showToast(it.errMsg)
        if (it.errCode == 30002 || it.errCode == 30003) {
          inviteFailedEvent.call()
        } else if (it.errCode == 30004 || it.errCode == 30005) {
          inviteBalanceNotEnoughEvent.value = VMEvent(it.errMsg)
        }
      }, {
        hideLoading()
      })
    }
  }

  private fun getSelectedResumeIDs(): String {
    val resumeIDsBuilder = StringBuilder()
    _selectedResumeList.forEach {
      resumeIDsBuilder.append(it.id).append(",")
    }
    return resumeIDsBuilder.substring(0, resumeIDsBuilder.length - 1)
  }

  private fun notifySelectResumeChange() {
    selectResumeList.value = ArrayList(_selectedResumeList)
  }
}