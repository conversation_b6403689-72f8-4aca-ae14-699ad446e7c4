package com.bxkj.enterprise.ui.activity.schoolhome;

import android.app.Application;
import androidx.lifecycle.MutableLiveData;
import androidx.annotation.NonNull;

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.data.SchoolDetailsData;
import com.bxkj.enterprise.data.source.SchoolDetailsRepo;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolhome
 * @Description:
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolHomeViewModel extends BaseViewModel {

    @Inject
    SchoolDetailsRepo mSchoolDetailsRepo;

    private final MutableLiveData<SchoolDetailsData> mSchoolDetailsData = new MutableLiveData<>();

    @Inject
    public SchoolHomeViewModel(@NotNull Application application) {
        super();
    }

    public void getSchoolDetailsInfo(int schoolId) {
        mSchoolDetailsRepo.getSchoolDetailsInfo(schoolId, new ResultDataCallBack<SchoolDetailsData>() {
            @Override
            public void onSuccess(SchoolDetailsData data) {
                mSchoolDetailsData.setValue(data);
            }

            @Override
            public void onError(@NonNull RespondThrowable respondThrowable) {
                showToast(respondThrowable.getErrMsg());
            }
        });
    }

    public MutableLiveData<SchoolDetailsData> getSchoolDetailsData() {
        return mSchoolDetailsData;
    }
}
