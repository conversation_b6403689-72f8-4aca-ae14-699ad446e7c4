package com.bxkj.enterprise.ui.activity.welfare

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.util.DensityUtils
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.ecommon.util.recyclerutil.RecycleViewGridDivider
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.color
import com.bxkj.enterprise.data.WelfareItemData
import com.bxkj.enterprise.databinding.EnterpriseActivityWelfareBinding
import com.zaaach.citypicker.adapter.decoration.GridItemDecoration
import java.util.ArrayList

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.welfare
 * @Description: 福利待遇
 * <AUTHOR>
 * @date 2019/9/23
 * @version V1.0
 */
class WelfareActivity : EBaseDBActivity<EnterpriseActivityWelfareBinding, WelfareViewModel>() {

  companion object {
    const val EXTRA_RESULT_WELFARE = "result_welfare"
    const val EXTRA_RESULT_OTHER_WELFARE = "result_other_welfare"
    const val EXTRA_SELECTED_WELFARE = "selected_welfare"
    const val EXTRA_OTHER_WELFARE = "other_welfare"
    const val EXTRA_IS_EDIT = "is_edit"
    const val EXTRA_JOB_ID = "job_id"

    fun newIntent(
      context: Context,
      jobId: Int,
      selectedWelfare: List<WelfareItemData>?,
      otherWelfare: String?,
      isEdit: Boolean
    ): Intent {
      val intent = Intent(context, WelfareActivity::class.java)
      intent.putExtra(EXTRA_JOB_ID, jobId)
      selectedWelfare?.let {
        intent.putParcelableArrayListExtra(EXTRA_SELECTED_WELFARE, it as ArrayList<out Parcelable>)
      }
      intent.putExtra(EXTRA_OTHER_WELFARE, otherWelfare)
      intent.putExtra(EXTRA_IS_EDIT, isEdit)
      return intent
    }
  }

  override fun getViewModelClass(): Class<WelfareViewModel> = WelfareViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_welfare

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewModel.start(intent)
    subscribeWelfareListChange()
    setupOtherWelfareList()
    subscribeViewModelEvent()
  }

  private fun subscribeWelfareListChange() {
    viewModel.welfareList.observe(this, Observer { welfareList ->
      welfareList?.let {
        val welfareListAdapter = WelfareListAdapter(
          this,
          welfareList,
          R.layout.enterprise_recycler_select_time_period_item,
          viewModel
        )
        viewBinding.recyclerWelfare.layoutManager = GridLayoutManager(this, 3)
        viewBinding.recyclerWelfare.addItemDecoration(
          RecycleViewGridDivider(
            DensityUtils.dp2px(this, 10f),
            getResColor(color.common_white),
            true
          )
        )
        viewBinding.recyclerWelfare.adapter = welfareListAdapter
      }
    })

    viewModel.saveWelfareSuccessEvent.observe(this, Observer {
      val intent = Intent()
      val bundle = Bundle()
      bundle.putParcelableArrayList(
        EXTRA_RESULT_WELFARE,
        viewModel.getSelectedWelfareList() as ArrayList<out Parcelable>
      )
      bundle.putParcelableArrayList(
        EXTRA_RESULT_OTHER_WELFARE,
        viewModel.getOtherWelfareListValue() as ArrayList<out Parcelable>
      )
      intent.putExtras(bundle)
      setResult(Activity.RESULT_OK, intent)
      finish()
    })
  }

  private fun setupOtherWelfareList() {
    val otherWelfareListAdapter =
      OtherWelfareListAdapter(R.layout.enterprise_recycler_other_welfare_item, viewModel)
    viewBinding.recyclerOtherWelfare.layoutManager = GridLayoutManager(this, 3)
    viewBinding.recyclerOtherWelfare.addItemDecoration(
      GridItemDecoration(
        3,
        DensityUtils.dp2px(this, 10f)
      )
    )
    viewBinding.recyclerOtherWelfare.adapter = otherWelfareListAdapter
  }

  private fun subscribeViewModelEvent() {
    viewModel.showAddOtherWelfareDialogEvent.observe(this, Observer {
      EActionDialog.Builder()
        .setActionType(EActionDialog.EDIT_TYPE)
        .setEditHeight(DensityUtils.dp2px(this, 98f))
        .setTitle(getString(R.string.welfare_input_other))
        .setHint(getString(R.string.welfare_input_other_hint))
        .setOnConfirmClickListener { actionDialog, inputText ->
          if (viewModel.checkWelfare(inputText)) {
            actionDialog.dismiss()
            viewModel.addOtherWelfare(inputText)
          }
        }.build().show(supportFragmentManager, EActionDialog.TAG)
    })
  }
}