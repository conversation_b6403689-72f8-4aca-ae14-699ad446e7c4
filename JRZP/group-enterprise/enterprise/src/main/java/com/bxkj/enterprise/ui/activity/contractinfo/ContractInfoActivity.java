package com.bxkj.enterprise.ui.activity.contractinfo;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.therouter.router.Route;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.EnterpriseInfoData;
import com.bxkj.enterprise.mvp.contract.EnterpriseInfoContract;
import com.bxkj.enterprise.mvp.presenter.EnterpriseInfoPresenter;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.contractinfo
 * @Description: 公司联系资料
 * @TODO: TODO
 * @date 2018/8/13
 */
@Route(path = ContractInfoNavigation.PATH)
public class ContractInfoActivity extends BaseDaggerActivity
  implements EnterpriseInfoContract.View, ContractInfoContract.View {

  @Inject
  EnterpriseInfoPresenter mEnterpriseInfoPresenter;
  @Inject
  ContractInfoPresenter mContractInfoPresenter;

  private TextView tvUserName;
  private EditText etContracts;
  private EditText etContractPhone;
  private EditText etContractQQ;

  public static void start(Context context) {
    Intent starter = new Intent(context, ContractInfoActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mEnterpriseInfoPresenter);
    presenters.add(mContractInfoPresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.contract_information))
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> {
        mContractInfoPresenter.updateContractInfo(getMUserID(), etContracts.getText().toString(),
          etContractPhone.getText().toString(), etContractQQ.getText().toString());
      });
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_contract_info;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    mEnterpriseInfoPresenter.getEnterpriseInfo(getMUserID());
  }

  @Override
  public void getEnterpriseInfoSuccess(EnterpriseInfoData enterpriseInfoData) {
    tvUserName.setText(enterpriseInfoData.getName());
    etContracts.setText(enterpriseInfoData.getLxr());
    etContractPhone.setText(enterpriseInfoData.getPhone());
    etContractQQ.setText(enterpriseInfoData.getQq());
  }

  @Override
  public void updateSuccess() {
    showToast(getString(R.string.common_update_success));
    finish();
  }

  private void bindView(View bindSource) {
    tvUserName = bindSource.findViewById(R.id.tv_user_name);
    etContracts = bindSource.findViewById(R.id.et_contracts);
    etContractPhone = bindSource.findViewById(R.id.et_contract_phone);
    etContractQQ = bindSource.findViewById(R.id.et_contract_qq);
  }
}
