package com.bxkj.enterprise.data

/**
 * Description:
 * Author:Sanjin
 * Date:2024/3/11
 **/
data class CommunicatedPeopleData(
  var id: Int,
  var userID: Int,
  var relName: String,
  var resID: Int,
  var resName: String,
  var userName: String,
  var userSex: Int,
  var userPhoto: String,
  var age: Int,
  var quaName: String,
  var cityID: Int,
  var cityName: String,
  var wtName: String,
  var GoalScore: String,
  var editDate: String,
) {
  fun getTransformResumeInfo(): ApplicantResumeData {
    val transformResume = ApplicantResumeData()
    transformResume.desiredJob = relName
    transformResume.selfIntro = GoalScore
    transformResume.id = resID
    transformResume.wtName = wtName
    transformResume.age = age
    // transformResume.edate1 = date.replace("-".toRegex(), "/")
    // transformResume.applyState = userData.applyState
    val userInfo = ResumeBaseInfoData()
    userInfo.cityJZName = cityName
    userInfo.tx = userPhoto
    userInfo.sex = age
    userInfo.name = userName
    userInfo.quaName = quaName
    transformResume.ubInfo = userInfo
    return transformResume
  }
}