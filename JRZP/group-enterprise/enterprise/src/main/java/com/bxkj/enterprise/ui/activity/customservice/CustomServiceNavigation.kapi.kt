package com.bxkj.enterprise.ui.activity.customservice

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/1/31
 **/
class CustomServiceNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/customservice"

    const val EXTRA_TITLE = "extra_title"

    fun create(title: String = "专属客服"): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(EXTRA_TITLE, title)
    }
  }
}