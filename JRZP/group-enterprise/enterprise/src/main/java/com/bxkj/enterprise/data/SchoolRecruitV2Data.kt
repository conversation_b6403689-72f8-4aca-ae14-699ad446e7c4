package com.bxkj.enterprise.data

import android.graphics.Color
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.share.BR

/**
 *
 * @author: sanjin
 * @date: 2022/4/15
 */
data class SchoolRecruitV2Data(
    var id: Int = 0,
    var title: String? = null,
    var toudiCount: Int = 0,
    @get:Bindable
    var content: String? = null,
    var createTime: String? = null,
    @get:Bindable
    var jsdate: String? = null,
    var isdelete2: Int = 0,
    var isdeleteMsg: String? = null,
    var jobName: List<JobItem>? = null
) : BaseObservable() {

    @get:Bindable
    var checked: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.checked)
        }

    fun getStateText(): String {
        return when (isdelete2) {
            0 -> "审核通过"
            1 -> "待审核"
            2 -> "审核失败"
            else -> "状态未知"
        }
    }

    fun getStateTextColor(): Int {
        return when (isdelete2) {
            0 -> Color.parseColor("#FF7405")
            1 -> Color.parseColor("#49C280")
            2 -> Color.parseColor("#FF4100")
            else -> Color.parseColor("#333333")
        }
    }

    @Bindable
    fun getJobsText(): String {
        return jobName?.map { it.jobName.getOrDefault() }.appendItem()
    }

    fun setJobList(list: List<JobItem>) {
        jobName = list
        notifyPropertyChanged(BR.jobsText)
    }

    fun updateContent(content: String) {
        this.content = content
        notifyPropertyChanged(BR.convertHtmlText)
    }

    @Bindable
    fun getConvertHtmlText(): String {
        return HtmlUtils.delHtmlTag(content.getOrDefault())
    }

    fun setExpiration(expiration: String) {
        jsdate = expiration
        notifyPropertyChanged(BR.jsdate)
    }

    //
    fun isReviewing(): Boolean {
        return isdelete2 == 1
    }

    fun getEmptyTips(): String {
        return when {
            title.isNullOrBlank() -> {
                "请填写标题"
            }
            jsdate.isNullOrBlank() -> {
                "请选择过期时间"
            }
            jobName.isNullOrEmpty() -> {
                "请添加招聘职位"
            }
            content.isNullOrBlank() -> {
                "请完善详情"
            }
            else -> {
                ""
            }
        }
    }

    fun isFailed(): Boolean {
        return isdelete2 == 2
    }
}