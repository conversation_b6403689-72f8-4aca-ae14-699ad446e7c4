package com.bxkj.enterprise.mvp.presenter;

import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.mvp.contract.IndustryContract;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.mvp.presenter
 * @Description: Industry
 * @TODO: TODO
 * @date 2018/3/27
 */

public class IndustryPresenter extends IndustryContract.Presenter {

    private static final String TAG = IndustryPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public IndustryPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getIndustryList() {
        mBusinessApi.getIndustryList()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getIndustryListSuccess((List<PickerOptionsData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
