package com.bxkj.enterprise.ui.fragment.linvoice

import com.bxkj.common.di.module.ApplicationModule
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.RegularUtils
import com.bxkj.enterprise.api.BusinessApi
import com.bxkj.enterprise.data.InvoiceData
import io.reactivex.disposables.Disposable
import javax.inject.Inject
import javax.inject.Named

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.linvoice
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/10/15
 * @version V1.0
 */
class InvoicePresenter @Inject constructor(
  @Named(ApplicationModule.USER_ID) userId: Int,
  api: BusinessApi
) : InvoiceContract.Presenter() {

  val mUserId = userId
  val mBusinessApi: BusinessApi = api

  override fun applyInvoice(orderIds: String, invoiceData: InvoiceData) {
    mView.showLoading()
    mBusinessApi.applyInvoice(mUserId, orderIds, invoiceData)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          mView.applyInvoiceSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mView.hiddenLoading()
          mView.onError(respondThrowable.errMsg)
        }

        override fun onComplete() {
          mView.hiddenLoading()
        }
      })
  }

  override fun reapplyInvoice(orderId: String, invoiceData: InvoiceData) {
    mView.showLoading()
    mBusinessApi.reapplyInvoice(mUserId, orderId, invoiceData)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          mView.applyInvoiceSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mView.hiddenLoading()
          mView.onError(respondThrowable.errMsg)
        }

        override fun onComplete() {
          mView.hiddenLoading()
        }
      })
  }

  fun checkParameters(invoiceData: InvoiceData): Boolean {
    if (CheckUtils.isNullOrEmpty(invoiceData.invoiceNo)) {
      mView.onError("税号不可为空")
      return false
    } else if (invoiceData.type == 1) {
      if (CheckUtils.isNullOrEmpty(invoiceData.regAddress)) {
        mView.onError("注册地址不可为空")
        return false
      } else if (CheckUtils.isNullOrEmpty(invoiceData.regPhone)) {
        mView.onError("注册电话不可为空")
        return false
      } else if (!RegularUtils.isPhone(invoiceData.regPhone) && !RegularUtils.isMobile(
          invoiceData.regPhone
        )
      ) {
        mView.onError("注册电话格式有误")
        return false
      } else if (CheckUtils.isNullOrEmpty(invoiceData.bankName)) {
        mView.onError("开户银行不可为空")
        return false
      } else if (CheckUtils.isNullOrEmpty(invoiceData.bankAccount)) {
        mView.onError("银行账号不可为空")
        return false
      }
    } else if (CheckUtils.isNullOrEmpty(invoiceData.name)) {
      mView.onError("收件人姓名不可为空")
      return false
    } else if (invoiceData.county == 0) {
      mView.onError("地区未选择")
      return false
    } else if (CheckUtils.isNullOrEmpty(invoiceData.address)) {
      mView.onError("详细地址不可为空")
      return false
    } else if (CheckUtils.isNullOrEmpty(invoiceData.mobile)) {
      mView.onError("手机号码不可为空")
      return false
    } else if (!RegularUtils.isMobile(invoiceData.mobile)) {
      mView.onError("手机号码格式有误")
      return false
    }
    return true
  }

  override fun getCompanyNameById() {
    mView.showLoading()
    mBusinessApi.getCompanyNameById(mUserId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          mView.getCompanyNameSuccess(baseResponse.msg)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mView.hiddenLoading()
          mView.onError(respondThrowable.errMsg)
        }

        override fun onComplete() {
          mView.hiddenLoading()
        }
      })
  }
}