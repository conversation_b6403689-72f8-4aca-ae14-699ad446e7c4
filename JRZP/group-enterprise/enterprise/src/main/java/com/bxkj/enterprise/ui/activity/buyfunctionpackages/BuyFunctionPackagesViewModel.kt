package com.bxkj.enterprise.ui.activity.buyfunctionpackages

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.toIntOrDefault
import com.bxkj.enterprise.data.FunctionPackagesBean
import com.bxkj.enterprise.data.PackagesDesc
import com.bxkj.enterprise.data.source.RechargeRepo
import com.bxkj.enterprise.ui.activity.beanmall.BeanMallFunctionItem
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/2/1
 **/
class BuyFunctionPackagesViewModel @Inject constructor(
    private val rechargeRepo: RechargeRepo
) : BaseViewModel() {

    val beanNotSufficientFundsEvent = MutableLiveData<VMEvent<String>>()
    val purchaseSuccessEvent = MutableLiveData<VMEvent<Unit>>()

    val functionPackagesInfo = MutableLiveData<FunctionPackagesBean>()
    val selectedPackage = MutableLiveData<PackagesDesc?>()

    val paymentAmount = MediatorLiveData<String>().apply {
        addSource(selectedPackage) {
            functionPackagesInfo.value?.let { packageInfo ->
                val amountValue = it?.count.toIntOrDefault(0) - packageInfo.douCount.toIntOrDefault(0)
                if (amountValue > 0) {
                    value = amountValue.toString()
                } else {
                    value = "0"
                }
            }
        }
    }

    private var _packageType: Int = BeanMallFunctionItem.TOP

    fun start(type: Int) {
        _packageType = type
        getFunctionPackageInfo(type)
    }

    fun selectPackage(packageDesc: PackagesDesc) {
        selectedPackage.value = packageDesc
    }

    fun buyFunctionPackage() {
        selectedPackage.value?.let {
            showLoading()
            viewModelScope.launch {
                rechargeRepo.buyFunctionPackage(_packageType, it.dlx).handleResult({
                    refreshBeanBalance()
                    purchaseSuccessEvent.value = VMEvent(Unit)
                }, {
                    if (it.errCode == 30007) {
                        beanNotSufficientFundsEvent.value = VMEvent(it.errMsg)
                    } else {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
            }
        }
    }

    private fun refreshBeanBalance() {
        start(_packageType)
    }

    private fun getFunctionPackageInfo(type: Int) {
        viewModelScope.launch {
            rechargeRepo.getFunctionPackagesInfo(type)
                .handleResult({
                    it?.let {
                        functionPackagesInfo.value = it
                        selectedPackage.value = it.resultDic?.get(0)
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }
}