package com.bxkj.enterprise.data.source

import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.ResumeStateData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.enterprise.api.BusinessApiV2
import com.bxkj.enterprise.api.parameters.GetReceivedResumeListParameters
import com.bxkj.enterprise.api.parameters.SearchResumeParameters
import com.bxkj.enterprise.data.*
import com.bxkj.enterprise.ui.fragment.recommendresume.ResumeFilterParams
import com.bxkj.enterprise.data.ResumeItemDataV2
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/10
 * @version: V1.0
 */
class ResumeRepo @Inject constructor(private val mCoroutinesApi: BusinessApiV2) : BaseRepo() {

  suspend fun addOrRemoveFavoriteResume(userID: Int, resumeID: Int): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.collectionResume(ZPRequestBody().apply {
        put("cuid", userID)
        put("resid", resumeID)
      })
    }
  }

  /**
   * 检查简历收藏
   */
  suspend fun checkResumeCollect(userId: Int, resumeId: Int): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.checkResumeCollect(ZPRequestBody().apply {
        put("cuid", userId)
        put("resid", resumeId)
      })
    }
  }

  suspend fun getResumeWorkExp(
    userId: Int,
    resumeId: Int,
    snapshotID: Int,
    sendHistoryID: Int,
  ): ReqResponse<List<ResumeWorkExpData>> {
    return httpRequest {
      mCoroutinesApi.getResumeWorkExp(ZPRequestBody().apply {
        put("uid", userId)
        put("resid", resumeId)
        put("kzid", snapshotID)
        put("jaID", sendHistoryID)
      })
    }
  }

  suspend fun getResumeEduBg(
    userId: Int,
    resumeId: Int,
    snapshotID: Int,
    sendHistoryID: Int,
  ): ReqResponse<List<ResumeEduBackgroundData>> {
    return httpRequest {
      mCoroutinesApi.getResumeEduBg(ZPRequestBody().apply {
        put("uid", userId)
        put("resid", resumeId)
        put("kzid", snapshotID)
        put("jaID", sendHistoryID)
      })
    }
  }

  suspend fun checkHasConversation(
    selfUserID: Int,
    userID: Int,
    resumeID: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.checkHasConversation(ZPRequestBody().apply {
        put("cUserID", selfUserID)
        put("userID", userID)
        put("resID", resumeID)
      }.objectEncrypt())
    }
  }

  suspend fun checkResumeIsInvitationDelivery(userID: Int, resumeID: Int): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.checkResumeIsInvitationDelivery(ZPRequestBody().apply {
        put("cuid", userID)
        put("resid", resumeID)
      })
    }
  }

  suspend fun checkResumeState(userID: Int, resumeID: Int): ReqResponse<ResumeStateData> {
    return httpRequest {
      mCoroutinesApi.checkResumeState(ZPRequestBody().apply {
        put("cuid", userID)
        put("resid", resumeID)
      })
    }
  }

  /**
   * 获取收藏的简历
   */
  suspend fun getFavoritesResumeList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<ResumeItemDataV2>> {
    return httpRequest {
      mCoroutinesApi.getFavoritesResumeList(
        ZPRequestBody().apply {
          put("cuid", userId)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }
      )
    }
  }

  /**
   * 获取沟通过的人才列表
   */
  suspend fun getCommunicatedPeopleList(
    jobId: Int,
    dateFlag: Int,
    name: String,
    sort: Int,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<List<CommunicatedPeopleData>> {
    return httpRequest {
      mCoroutinesApi.getCommunicatedPeopleList(
        ZPRequestBody().apply {
          put("relid", jobId)
          put("time", dateFlag)
          put("name", name)
          put("px", sort)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        })
    }
  }

  /**
   * 删除收到的简历
   */
  suspend fun deleteReceivedResume(ids: String): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.deleteReceivedResume(ZPRequestBody().apply {
        put("ids", ids)
      }.objectEncrypt())
    }
  }

  /**
   * 获取收到的简历列表
   */
  suspend fun getReceivedResumeList(
    userId: Int,
    getReceivedResumeListParameters: GetReceivedResumeListParameters,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<List<ResumeItemDataV2>> {
    return httpRequest {
      mCoroutinesApi.getReceivedResumeList(ZPRequestBody().apply {
        put("cuid", userId);
        put("islook", getReceivedResumeListParameters.isLook);
        put("types", getReceivedResumeListParameters.resumeTypes);
        put("type", getReceivedResumeListParameters.resumeType);
        put("states", getReceivedResumeListParameters.resumeStates);
        put("state", getReceivedResumeListParameters.resumeState);
        put("relid", getReceivedResumeListParameters.positionId);
        put("pageIndex", pageIndex);
        put("pageSize", pageSize);
      })
    }
  }

  /**
   * 获取分享简历参数
   */
  suspend fun getShareResumeParams(
    jobId: Int,
    resumeId: Int,
  ): ReqResponse<ShareResumeInfoBean> {
    return httpRequest {
      mCoroutinesApi.getShareResumeParams(
        ZPRequestBody().apply {
          put("relid", jobId)
          put("resid", resumeId)
        }.objectEncrypt()
      )
    }
  }

  /**
   * 获取推荐简历列表
   */
  suspend fun getRecommendResumeList(
    resumeFilterParams: ResumeFilterParams,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<RecommendResumeResultV2> {
    return httpRequest {
      mCoroutinesApi.getRecommendResumeList(
        ZPRequestBody().apply {
          put("relId", resumeFilterParams.jobID)
          put("citys", resumeFilterParams.city)
          put("sex", resumeFilterParams.gender)
          put("MinAge", resumeFilterParams.minAge)
          put("MaxAge", resumeFilterParams.maxAge)
          put("minWillMoney", resumeFilterParams.minSalary)
          put("maxWillMoney", resumeFilterParams.maxSalary)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 根据职位信息获取推荐职位
   */
  suspend fun getResumeListByJobInfo(
    myOnlineJobResult: MyOnlineJobResult,
    flag: Int,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<RecommendResumeResultV2> {
    return httpRequest {
      mCoroutinesApi.getResumeListByJobInfo(
        ZPRequestBody().apply {
          put("name", myOnlineJobResult.name)
          put("citys", myOnlineJobResult.city)
          put("flag", flag)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取简历列表
   */
  suspend fun getResumeList(
    userId: Int,
    searchResumeParameters: SearchResumeParameters,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<List<ResumeItemDataV2>> {
    return httpRequest {
      mCoroutinesApi.getResumeList(
        ZPRequestBody().apply {
          put("relId", searchResumeParameters.jobId)
          put("uid", 0)
          put("myUid", userId)
          put("sex", searchResumeParameters.sex)
          put("wtid", searchResumeParameters.workExpId)
          put("quaid", searchResumeParameters.educationId)
          put("jnid", searchResumeParameters.workNatureId)
          put("tradeid", searchResumeParameters.industryId)
          put("jobType1", searchResumeParameters.firstClassId)
          put("jobType2", searchResumeParameters.secondClassId)
          put("province", searchResumeParameters.province)
          put("city", searchResumeParameters.city)
          put("county", searchResumeParameters.county)
          put("town", searchResumeParameters.town)
          put("name", searchResumeParameters.title)
          put("minWillMoney", searchResumeParameters.minWillMoney)
          put("maxWillMoney", searchResumeParameters.maxWillMoney)
          put("jobType2s", searchResumeParameters.filterResumeTypes)
          put("MinAge", searchResumeParameters.minAge)
          put("MaxAge", searchResumeParameters.maxAge)
          put("eTimeID", searchResumeParameters.updateDateId)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 下载简历
   */
  suspend fun downloadResume(
    userId: Int,
    jobId: Int,
    resumeId: Int,
    downloadMethod: Int,
  ):
    ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.downloadResume(
        ZPRequestBody()
          .apply {
            put("cuid", userId)
            put("relid", jobId)
            put("resid", resumeId)
            put("downloadType", downloadMethod)
          }
      )
    }
  }

  /**
   * 获取下载简历消耗
   */
  suspend fun getDownloadResumeConsume(downloadMethod: Int): ReqResponse<Int> {
    return httpRequest {
      mCoroutinesApi.getDownloadResumeConsume(
        ZPRequestBody()
          .apply {
            put("downloadType", downloadMethod)
          }
      ).apply {
        if (status == 10001) {
          data = if (!msg.isNullOrBlank()) msg.toInt() else 0
        }
      }
    }
  }

  /**
   * 更改简历状态
   * 0未处理，
  1初选合格（待沟通），
  2淘汰，
  3面试，
  4面试被拒绝，
  5面试通过，
  6已发offer，
  7已拒offer，
  8接受offer，
  9已入职
   */
  suspend fun updateResumeState(
    userId: Int,
    jobId: Int,
    resumeId: Int,
    state: Int,
    msg: String = "",
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.updateResumeState(
        ZPRequestBody().apply {
          put("cuid", userId)
          put("relid", jobId)
          put("resid", resumeId)
          put("state", state)
          put("msg", msg)
        }
      )
    }
  }

  /**
   * 简历求职意向
   */
  suspend fun getResumeCareerObjective(
    resumeUserId: Int,
    resumeId: Int,
    snapshotID: Int = 0,
    sendRecordID: Int = 0
  ): ReqResponse<ResumeCareerObjectiveData> {
    return httpRequest {
      mCoroutinesApi.getResumeCareerObjective(ZPRequestBody().apply {
        put("uid", resumeUserId)
        put("id", resumeId)
        put("kzid", snapshotID)
        put("jaID", sendRecordID)
      })
    }
  }

  /**
   * 获取简历基本信息
   */
  suspend fun getResumeBasicInfo(
    userId: Int,
    snapshotID: Int = 0,
    sendRecordID: Int = 0
  ): ReqResponse<ResumeBaseInfoData> {
    return httpRequest {
      mCoroutinesApi.getResumeBasicInfo(ZPRequestBody().apply {
        put("id", userId)
        put("kzid", snapshotID)
        put("jaID", sendRecordID)
      })
    }
  }

  /**
   * 邀请投递简历
   */
  suspend fun sendSayHelloMsg(
    userId: Int,
    jobId: Int,
    resumeId: String,
    remark: String,
  ): ReqResponse<BaseResponse<InviteSendResumeResult>> {
    return httpRequestResultOrigin {
      mCoroutinesApi.sendSayHello(
        ZPRequestBody().apply {
          put("cuid", userId)
          put("relid", jobId)
          put("resids", resumeId)
          put("remark", remark)
        }
      )
    }
  }

  suspend fun inviteSendResume(
    userId: Int,
    jobId: Int,
    resumeId: String,
    remark: String,
  ): ReqResponse<BaseResponse<InviteSendResumeResult>> {
    return httpRequestResultOrigin {
      mCoroutinesApi.inviteSendResume(
        ZPRequestBody().apply {
          put("cuid", userId)
          put("relid", jobId)
          put("resids", resumeId)
          put("remark", remark)
        }
      )
    }
  }

  /**
   * 返回符合给定参数[jobID][jobTypeID][cityID]的简历
   */
  suspend fun getRecommendResumeByJobId(
    userID: Int,
    jobID: Int,
    jobTypeID: Int,
    recommendIDs: String,
  ): ReqResponse<RecommendResumeResult> {
    return httpRequest {
      mCoroutinesApi.getRecommendResumeByJobId(
        ZPRequestBody().apply {
          put("uid", userID)
          put("relId", jobID)
          put("jobType2", jobTypeID)
          put("resIds", recommendIDs)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 根据[resumeIDs]邀请多份简历
   */
  suspend fun inviteMultipleResume(
    selfUserID: Int,
    jobID: Int,
    resumeIDs: String,
    remark: String,
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.inviteMultipleResume(
        ZPRequestBody().apply {
          put("cuid", selfUserID)
          put("relid", jobID)
          put("resids", resumeIDs)
          put("remark", remark)
        }
      )
    }
  }
}