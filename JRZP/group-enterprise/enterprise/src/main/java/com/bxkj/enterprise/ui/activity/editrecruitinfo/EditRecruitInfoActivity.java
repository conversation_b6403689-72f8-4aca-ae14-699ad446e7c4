package com.bxkj.enterprise.ui.activity.editrecruitinfo;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.ecommon.base.EBaseDBActivity;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.RecruitInfoItemData;
import com.bxkj.enterprise.databinding.EnterpriseActivityEditRecruitInfoBinding;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.editrecruitinfo
 * @Description: 招聘信息
 * @TODO: TODO
 * @date 2019/6/16
 */
public class EditRecruitInfoActivity extends
    EBaseDBActivity<EnterpriseActivityEditRecruitInfoBinding, EditRecruitInfoViewModel> {

    public static final String EXTRA_PAGE_TYPE = "PAGE_TYPE";
    public static final String EXTRA_RECRUIT_INFO = "RECRUIT_INFO";

    public static final int TYPE_ADD = 1;
    public static final int TYPE_EDIT = 2;

    public static final int RESULT_DELETE_SUCCESS = RESULT_OK + 2;

    public static Intent newAddInfoIntent(Context context) {
        return newIntent(context, TYPE_ADD, null);
    }

    public static Intent newEditInfoIntent(Context context, RecruitInfoItemData recruitInfoItemData) {
        return newIntent(context, TYPE_EDIT, recruitInfoItemData);
    }

    private static Intent newIntent(Context context, int pageType, RecruitInfoItemData recruitInfoItemData) {
        Intent intent = new Intent(context, EditRecruitInfoActivity.class);
        intent.putExtra(EXTRA_PAGE_TYPE, pageType);
        intent.putExtra(EXTRA_RECRUIT_INFO, recruitInfoItemData);
        return intent;
    }

    @Override
    protected Class<EditRecruitInfoViewModel> getViewModelClass() {
        return EditRecruitInfoViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.enterprise_activity_edit_recruit_info;
    }

    @Override
    protected void initPage(final Bundle savedInstanceState) {
        getViewBinding().setViewModel(getViewModel());
        getViewModel().handleIntentData(getIntent());
        subscribeViewModelCommand();
        getViewBinding().titleBar.setRightOptionClickListener(v -> getViewModel().saveChange());
    }

    private void subscribeViewModelCommand() {
        //订阅保存命令
        getViewModel().getSaveCommand().observe(this, recruitInfoItemData -> {
            if (recruitInfoItemData != null) {
                resultAndFinish(recruitInfoItemData, RESULT_OK);
            }
        });

        //订阅删除命令
        getViewModel().getDeleteCommand().observe(this, recruitInfoItemData -> {
            if (recruitInfoItemData != null) {
                new EActionDialog.Builder()
                        .setContent(getString(R.string.recruit_info_delete_tips))
                        .setOnConfirmClickListener((actionDialog, inputText) -> {
                            actionDialog.dismiss();
                            resultAndFinish(recruitInfoItemData, RESULT_DELETE_SUCCESS);
                        }).build().show(getSupportFragmentManager(), EActionDialog.TAG);
            }
        });
    }

    private void resultAndFinish(RecruitInfoItemData recruitInfoItemData, int resultCode) {
        SystemUtil.hideSoftKeyboard(this);
        Intent intent = new Intent();
        intent.putExtra(EXTRA_RECRUIT_INFO, recruitInfoItemData);
        setResult(resultCode, intent);
        finish();
    }

}
