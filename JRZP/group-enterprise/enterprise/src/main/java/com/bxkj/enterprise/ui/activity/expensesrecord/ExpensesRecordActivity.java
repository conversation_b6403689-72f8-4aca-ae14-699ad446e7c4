package com.bxkj.enterprise.ui.activity.expensesrecord;

import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.enterprise.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.expensesrecord
 * @Description: 消费记录
 * @TODO: TODO
 * @date 2018/10/13
 */
public class ExpensesRecordActivity extends BaseDaggerActivity {
    @Override
    protected int getLayoutId() {
        return R.layout.enterprise_activity_expenses_record;
    }

    @Override
    protected void initPage() {

    }
}
