package com.bxkj.enterprise.ui.activity.schoolrecruitjob.cityselect

import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.SectionItemDecoration
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.SchoolRecruitCityData
import com.bxkj.enterprise.databinding.EnterpriseFragmentCitySelectBinding
import com.github.promeg.pinyinhelper.Pinyin
import com.github.promeg.pinyinhelper.PinyinMapDict
import com.github.promeg.tinypinyin.lexicons.android.cncity.CnCityDict
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.sanjindev.pagestatelayout.OnStateSetUpListener

/**
 *
 * @author: sanjin
 * @date: 2022/4/20
 */
class CitySelectDialogFragment(private val onSelectedReturnListener: OnSelectedReturnListener) :
    BaseDBDialogFragment<EnterpriseFragmentCitySelectBinding, CitySelectViewModel>() {

    companion object {

        private const val EXTRA_SELECTED_CITY = "SELECTED_CITY"

        fun newInstance(
            selectedCity: List<String>?,
            onSelectedReturnListener: OnSelectedReturnListener
        ): CitySelectDialogFragment {
            return CitySelectDialogFragment(onSelectedReturnListener).apply {
                arguments = bundleOf(EXTRA_SELECTED_CITY to selectedCity)
            }
        }
    }

    private var sectionItemDecoration: SectionItemDecoration? = null

    override fun getViewModelClass(): Class<CitySelectViewModel> = CitySelectViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_fragment_city_select

    override fun initPage() {
        viewBinding.viewModel = viewModel

        viewBinding.ivClose.setOnClickListener {
            this.dismiss()
        }

        Pinyin.init(
            Pinyin.newConfig().with(CnCityDict.getInstance(requireContext()))
                .with(object : PinyinMapDict() {
                    override fun mapping(): MutableMap<String, Array<String>> {
                        return mutableMapOf(
                            "长治" to arrayOf("CHANG", "ZHI"),
                            "重庆" to arrayOf("CHONG", "QING")
                        )
                    }
                })
        )

        subscribeViewModelEvent()

        setupResultListAdapter()
        setupSelectedListAdapter()

        viewModel.start(getArgumentsSelectedCity())
    }

    private fun getArgumentsSelectedCity(): List<String>? {
        return arguments?.getStringArrayList(EXTRA_SELECTED_CITY)
    }

    private fun subscribeViewModelEvent() {
        viewModel.searchKey.observe(this) { text ->
            sectionItemDecoration?.let {
                if (text.isNullOrBlank()) {
                    if (viewBinding.recyclerResult.itemDecorationCount == 1) {
                        viewBinding.recyclerResult.addItemDecoration(it)
                    }
                } else {
                    viewBinding.recyclerResult.removeItemDecoration(it)
                }
            }
            viewModel.startSearch(text)
        }

        viewModel.allCity.observe(this, Observer {
            sectionItemDecoration = SectionItemDecoration(
                requireContext(),
                it,
                getResColor(R.color.calendar_F4F4F4),
                dip(32),
                18,
                getResColor(R.color.cl_333333)
            )
            viewBinding.recyclerResult.addItemDecoration(sectionItemDecoration!!)
        })

        viewModel.returnSelectCityCommand.observe(this) {
            onSelectedReturnListener.onSelectedReturn(it)
            this.dismiss()
        }

        viewModel.showLoadingPageCommand.observe(this) {
            viewBinding.pslContent.show(LoadingPageState::class.java)
        }

        viewModel.showNoResultPageCommand.observe(this) {
            viewBinding.pslContent.show(object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                    pageState.setContent("未查到相关城市")
                }
            })
        }

        viewModel.showLoadErrorPageCommand.observe(this) {
            viewBinding.pslContent.show(
                object : OnStateSetUpListener<ErrorPageState> {
                    override fun onStateSetUp(pageState: ErrorPageState) {
                        pageState.setNextOptionClickListener {
                            viewModel.start(getArgumentsSelectedCity())
                        }
                    }
                })
        }

        viewModel.hideStateLayoutCommand.observe(this) {
            viewBinding.pslContent.hidden()
        }

    }

    private fun setupResultListAdapter() {
        val resultListAdapter =
            SimpleDiffListAdapter(
                R.layout.enterprise_recycler_school_recruit_city_item,
                SchoolRecruitCityData.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.get(position)?.let {
                            viewModel.switchCheckedState(it)
                        }
                    }
                })
            }

        viewBinding.indexBar.apply {
            setOverlayTextView(viewBinding.tvOverlay)
            setOnIndexChangedListener { key, _ ->
                key?.let {
                    resultListAdapter.getData()?.let { data ->
                        for (i in data.indices) {
                            if (data[i].section == key) {
                                viewBinding.recyclerResult.toPosition(i)
                                break
                            }
                        }
                    }
                }
            }
        }

        viewBinding.recyclerResult.apply {
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_2))
                    .build()
            )
            adapter = resultListAdapter
        }
    }

    private fun setupSelectedListAdapter() {
        val selectedListAdapter =
            SimpleDiffListAdapter(
                R.layout.enterprise_recycler_selected_city_item,
                SchoolRecruitCityData.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.get(position)?.let {
                            viewModel.deleteSelected(it)
                        }
                    }
                })
            }

        viewBinding.recyclerSelected.apply {
            isNestedScrollingEnabled = false
            layoutManager = GridLayoutManager(requireContext(), 4)
            addItemDecoration(
                GridItemDecoration(getResDrawable(R.drawable.divider_4))
            )
            adapter = selectedListAdapter
        }
    }

    override fun getTheme(): Int {
        return R.style.BaseDialogFragmentStyle
    }

    override fun enableBottomSheet(): Boolean {
        return true
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let {
            it.setWindowAnimations(R.style.BottomPopupAnim)
            it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
                val layoutParams = layout.layoutParams
                layoutParams.height = resources.displayMetrics.heightPixels
                val bottomSheetBehavior = BottomSheetBehavior.from(layout)
                bottomSheetBehavior.peekHeight = resources.displayMetrics.heightPixels
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }
    }

    interface OnSelectedReturnListener {
        fun onSelectedReturn(selectedCity: List<SchoolRecruitCityData>)
    }

}