package com.bxkj.enterprise.ui.activity.beanmall

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/1/30
 **/
class BeanMallNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/bean_mall"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}