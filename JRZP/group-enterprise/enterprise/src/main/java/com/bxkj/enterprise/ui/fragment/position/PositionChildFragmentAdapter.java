package com.bxkj.enterprise.ui.fragment.position;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.ui.fragment.positionlist.PositionListFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.position
 * @Description:
 * @TODO: TODO
 * @date 2018/7/12
 */
public class PositionChildFragmentAdapter extends FragmentPagerAdapter {

    private String[] mTitles;

    public PositionChildFragmentAdapter(FragmentManager fm, String[] titles) {
        super(fm);
//        for (String title : titles) {
//            PositionListFragment positionListFragment = PositionListFragment.newInstance(title);
//            mFragmentList.add(positionListFragment);
//        }
        mTitles = titles;
    }

    @Override
    public Fragment getItem(int position) {
        return PositionListFragment.newInstance(mTitles[position]);
//        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mTitles.length;
//        return CheckUtils.isNullOrEmpty(mFragmentList) ? 0 : mFragmentList.size();
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        return mTitles[position];
    }
}
