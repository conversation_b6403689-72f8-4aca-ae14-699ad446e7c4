package com.bxkj.enterprise.ui.fragment.recommendresume

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.BRecyclerLatestResumeItemBinding
import com.bxkj.enterprise.databinding.EnterpriseFragmentRecommendResumeV2Binding
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentNavigation
import com.bxkj.enterprise.ui.activity.postjob.PostJobV2Activity
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2
import com.bxkj.enterprise.ui.fragment.homev2.ResumeFilterChild
import com.bxkj.enterprise.ui.fragment.homev2.ResumeFilterDialog
import com.bxkj.enterprise.ui.fragment.homev2.ResumeListContainer
import com.bxkj.enterprise.data.ResumeItemDataV2
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckInfoCallBackAdapter
import com.bxkj.jrzp.support.feature.ui.infocheck.InfoCompletedCheck
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 *
 * @author: sanjin
 * @date: 2022/11/16
 */
class LatestResumeFragment :
  BaseDBFragment<EnterpriseFragmentRecommendResumeV2Binding, LatestResumeViewModel>(),
  ResumeFilterChild {

  companion object {

    private const val EXTRA_JOB_ID = "JOB_ID"
    fun newInstance(jobID: Int?): Fragment {
      return LatestResumeFragment().apply {
        arguments = bundleOf(
          EXTRA_JOB_ID to jobID
        )
      }
    }
  }

  private val _extraJobID by lazy { arguments?.getInt(EXTRA_JOB_ID) }

  private var _resumeListAdapter: SimplePageDataAdapter<ResumeItemDataV2, BRecyclerLatestResumeItemBinding>? =
    null

  override fun getViewModelClass(): Class<LatestResumeViewModel> =
    LatestResumeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_fragment_recommend_resume_v2

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.pslContainer.show(LoadingPageState::class.java)

    viewModel.setJobId(_extraJobID.getOrDefault())

    setupResumeList()

    subscribeViewModelEvent()

    subscribePositionRefreshEvent()
  }

  private fun subscribeViewModelEvent() {
    viewModel.showNoMemberPermissionTipsCommand.observe(this, EventObserver {
      showInviteErrorMsg("开通会员", it)
    })

    viewModel.showNoInviteBalanceTipsCommand.observe(this, EventObserver {
      showInviteErrorMsg("充值", it)
    })

    viewModel.noPostJobEvent.observe(this, EventObserver {
      ActionDialog.Builder()
        .setTitle("提示")
        .setContent("您还没有发布职位，请发布职位后邀请")
        .setConfirmText("去发布")
        .setOnConfirmClickListener {
          InfoCompletedCheck.with(parentActivity)
            .checkItem(
              InfoCheckItem.Builder()
                .checkInfoTag(CheckInfoTag.CHECK_COMPANY_AUTH_NEW)
                .onlyCheck(false)
                .build()
            ).setCheckInfoCallBack(object : CheckInfoCallBackAdapter() {
              override fun onAllCheckSuccess(result: CheckResultMsg) {
                startActivity(PostJobV2Activity.newIntent(requireContext()))
              }
            }).start()
        }.build().show(childFragmentManager)
    })
  }

  private fun showInviteErrorMsg(confirmText: String, errTips: String) {
    ActionDialog.Builder()
      .setTitle("提示")
      .setContent(errTips)
      .setConfirmText(confirmText)
      .setOnConfirmClickListener {
        MemberCenterWebNavigation.create().start()
      }.build().show(childFragmentManager)
  }

  private fun subscribePositionRefreshEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.REFRESH_POSITION_LIST_CODE) {
            refresh()
          }
        }
    )
  }

  private fun setupResumeList() {
    _resumeListAdapter =
      SimplePageDataAdapter<ResumeItemDataV2, BRecyclerLatestResumeItemBinding>(
        R.layout.b_recycler_latest_resume_item,
        ResumeItemDataV2.DiffCallback()
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getItemBean(position)?.let { item ->
              if (v.id == R.id.tv_conversation) {
                if (item.isHasChat()) {
                  BusinessChatContentNavigation.create(
                    ChatRole.BUSINESS,
                    item.ubInfo?.uid.getOrDefault()
                  )
                    .start()
                } else {
                  viewModel.inviteSendResume(item)
                }
              } else {
                startActivity(
                  ApplicantResumeDetailActivityV2.newIntent(
                    requireContext(),
                    item.id,
                    item.ubInfo?.uid.getOrDefault(),
                    jobID = _extraJobID.getOrDefault(),
                  )
                )
              }
            }
          }
        }, R.id.tv_conversation)
        addLoadStateListener { loadStateFlow ->
          loadStateFlow.handleState({
            finishParentLoading()
            viewBinding.pslContainer.hidden()
          }, {
            finishParentLoading()
            viewBinding.pslContainer.show(
              EmptyPageState::class.java,
              object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                  pageState.setContent("未查到数据")
                }
              })
          }, {
            finishParentLoading()
            viewBinding.pslContainer.show(
              ErrorPageState::class.java,
              object : OnStateSetUpListener<ErrorPageState> {
                override fun onStateSetUp(pageState: ErrorPageState) {
                  pageState.setContent(it.message.getOrDefault())
                  pageState.setNextOptionClickListener { refresh() }
                }
              })
          })
        }
      }

    lifecycleScope.launch {
      viewModel.resumeListFlow.collectLatest {
        _resumeListAdapter?.submitData(it)
      }
    }

    viewBinding.recyclerResumeList.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_8)).build()
      )
      adapter =
        _resumeListAdapter?.withLoadStateFooter(
          LoadStateFooterAdapter { _resumeListAdapter?.retry() })
    }
  }

  private fun finishParentLoading() {
    if (parentFragment is ResumeListContainer) {
      (parentFragment as ResumeListContainer).finishRefresh()
    }
  }

  override fun refresh() {
    _resumeListAdapter?.refresh()
  }

  override fun resumeFilter(resumeFilterParams: ResumeFilterParams) {
    viewBinding.pslContainer.show(LoadingPageState::class.java)
    viewBinding.recyclerResumeList.toPosition(0)
    viewModel.setFilterParams(resumeFilterParams)
    _resumeListAdapter?.refresh()
  }

  override fun showFilter() {
    ResumeFilterDialog { filterParams ->
      resumeFilter(filterParams)
    }.apply {
      setParams(viewModel.getFilterParams())
    }.show(
      childFragmentManager,
      ResumeFilterDialog::class.java.simpleName
    )
  }
}