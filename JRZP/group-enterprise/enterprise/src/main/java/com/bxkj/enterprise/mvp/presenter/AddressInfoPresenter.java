package com.bxkj.enterprise.mvp.presenter;


import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.mvp.contract.AddressInfoContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: AddressId
 * @TODO: TODO
 * @date 2018/3/27
 */

public class AddressInfoPresenter extends AddressInfoContract.Presenter {

    private static final String TAG = AddressInfoPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public AddressInfoPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getAddressInfoByAddressName(int addressType, String addressName) {
        mBusinessApi.getAddressInfoByAddressName(addressType, addressName)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        if (baseResponse.getData()!=null){
                            mView.getAddressInfoSuccess((AreaOptionsData) baseResponse.getData());
                        }else {
                            mView.getAddressInfoSuccess(AreaOptionsData.getDefaultLocation());
                        }
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
