package com.bxkj.enterprise.data;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TextUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 教育背景
 * @TODO: TODO
 * @date 2018/5/9
 */

public class ResumeEduBackgroundData {

  private int id;
  private String date1;
  private String date2;
  private String school;
  private String proName2;
  private String quaName;

  private List<ResumeEduBackgroundData> eduBackgroundItemDataList;

  public ResumeEduBackgroundData(List<ResumeEduBackgroundData> eduBackgroundItemDataList) {
    this.eduBackgroundItemDataList = eduBackgroundItemDataList;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getDate1() {
    return date1;
  }

  public void setDate1(String date1) {
    this.date1 = date1;
  }

  public String getDate2() {
    return date2;
  }

  public void setDate2(String date2) {
    this.date2 = date2;
  }

  public String getSchool() {
    return school;
  }

  public void setSchool(String school) {
    this.school = school;
  }

  public String getProName2() {
    return proName2;
  }

  public void setProName2(String proName2) {
    this.proName2 = proName2;
  }

  public String getQuaName() {
    return quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public List<ResumeEduBackgroundData> getEduBackgroundItemDataList() {
    return eduBackgroundItemDataList;
  }

  public void setEduBackgroundItemDataList(
      List<ResumeEduBackgroundData> eduBackgroundItemDataList) {
    this.eduBackgroundItemDataList = eduBackgroundItemDataList;
  }

  public String getDescText() {
    final StringBuilder descBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(school)) {
      descBuilder.append(school);
    }
    if (!CheckUtils.isNullOrEmpty(proName2)) {
      descBuilder.append("|").append(proName2).append("|");
    }
    if (!CheckUtils.isNullOrEmpty(quaName)) {
      descBuilder.append(quaName);
    }
    return descBuilder.toString();
  }
}
