package com.bxkj.enterprise.weight.filterareapopup;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.enterprise.R;

import java.util.List;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.filterareapopup
 * @Description:
 * @TODO: TODO
 * @date 2019/3/16
 */
public class FilterAreaStreetView extends ConstraintLayout {

    private FilterAreaAdapter mFilterAreaAdapter;
    private FilterStreetAdapter mFilterStreetAdapter;
    private TextView tvCurrentCity;

    public FilterAreaStreetView(Context context) {
        this(context, null);
    }

    public FilterAreaStreetView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterAreaStreetView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.enterprise_popup_filter_area_and_street, this);

        tvCurrentCity = findViewById(R.id.tv_current_city);
        findViewById(R.id.ll_change_current_city).setOnClickListener(view -> {
            if (mAreaItemClickListener != null) {
                mAreaItemClickListener.onChangeCityClicked();
            }
        });

        mFilterAreaAdapter = new FilterAreaAdapter(getContext(), null, R.layout.enterprise_recycler_filter_area_item);
        RecyclerView recyclerArea = findViewById(R.id.recycler_filter_area);
        recyclerArea.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerArea.setAdapter(mFilterAreaAdapter);
        mFilterAreaAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                mAreaItemClickListener.onAreaItemClicked(position);
            }
        });

        mFilterStreetAdapter = new FilterStreetAdapter(getContext(), null, R.layout.enterprise_recycler_filter_street_item);
        RecyclerView recyclerStreet = findViewById(R.id.recycler_filter_street);
        recyclerStreet.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerStreet.setAdapter(mFilterStreetAdapter);
        mFilterStreetAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                mAreaItemClickListener.onStreetItemClicked(position);
            }
        });
    }

    /**
     * 设置区级数据
     *
     * @param optionsDataList
     */
    public void setAreaData(List<AreaOptionsData> optionsDataList) {
        mFilterAreaAdapter.clearSelected();
        mFilterAreaAdapter.setData(optionsDataList);
        mFilterAreaAdapter.notifyDataSetChanged();
    }
    public List<AreaOptionsData> getAreaData() {
        return mFilterAreaAdapter.getData();
    }
    /**
     * 设置街级数据
     *
     * @param optionsDataList
     */
    public void setStreetData(List<AreaOptionsData> optionsDataList) {
        mFilterStreetAdapter.getLayoutManager().scrollToPosition(0);
        mFilterStreetAdapter.resetSelectPosition();
        mFilterStreetAdapter.setData(optionsDataList);
        mFilterStreetAdapter.notifyDataSetChanged();
    }

   public List<AreaOptionsData> getStreetData(){
        return mFilterStreetAdapter.getData();
   }


    /**
     * 重置街道数据
     */
    public void resetStreetData() {
        mFilterStreetAdapter.setData(null);
        mFilterStreetAdapter.notifyDataSetChanged();
    }

    /**
     * 设置当前城市
     *
     * @param city
     */
    public void setCurrentCity(String city) {
        tvCurrentCity.setText(city);
    }

    private OnAreaItemClickListener mAreaItemClickListener;

    public void setOnAreaItemClickListener(OnAreaItemClickListener onAreaItemClickListener) {
        mAreaItemClickListener = onAreaItemClickListener;
    }


    public interface OnAreaItemClickListener {
        void onAreaItemClicked(int position);

        void onStreetItemClicked(int positions);

        void onChangeCityClicked();
    }
}
