package com.bxkj.enterprise.ui.fragment.collection;

import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.collection
 * @Description: Collection
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CollectionContract {
    interface View extends BaseHasListView {
        void getResumeListSuccess(List<ResumeItemData> collectionResumeItemDataList);

        void deleteCollectionSuccess(int position);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getCollectionResumeList(int userId, int pageIndex, int pageSize);

        abstract void deleteCollection(int userId,int resumeId,int position);
    }
}
