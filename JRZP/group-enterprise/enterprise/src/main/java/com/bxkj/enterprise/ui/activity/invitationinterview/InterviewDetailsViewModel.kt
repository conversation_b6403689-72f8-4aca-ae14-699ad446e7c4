package com.bxkj.enterprise.ui.activity.invitationinterview

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class InterviewDetailsViewModel @Inject constructor(
    private val _chatRepo: ChatRepo,
    private val _myJobRepo: MyJobRepo
) : BaseViewModel() {

    val interviewInfo = MutableLiveData<InterviewInfoBean>()

    val cancelSuccessEvent = MutableLiveData<VMEvent<Unit>>()

    val viewResumeCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val toChatCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val callPhoneCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val updateInterviewInfoCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val reInviteCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    private var _jobId: Int = 0
    private var _resumeId: Int = 0

    fun start(jobId: Int, resumeId: Int) {
        _jobId = jobId
        _resumeId = resumeId
        refreshInterviewInfo()
    }

    fun viewResume() {
        interviewInfo.value?.let {
            viewResumeCommand.value = VMEvent(it)
        }
    }

    fun toChat() {
        interviewInfo.value?.let {
            toChatCommand.value = VMEvent(it)
        }
    }

    fun callPhone() {
        interviewInfo.value?.let {
            callPhoneCommand.value = VMEvent(it)
        }
    }

    fun cancelInterview(reason: String) {
        interviewInfo.value?.let { info ->
            viewModelScope.launch {
                showLoading()
                _myJobRepo.cancelInterview(info.jiid, reason)
                    .handleResult({
                        showToast("取消成功")
                        cancelSuccessEvent.value = VMEvent(Unit)
                        refreshInterviewInfo()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun updateInterviewInfo() {
        interviewInfo.value?.let {
            updateInterviewInfoCommand.value = VMEvent(it)
        }
    }

    fun refreshInterviewInfo() {
        viewModelScope.launch {
            showLoading()
            _chatRepo.getInterviewInfo(_jobId, _resumeId, AppConstants.BUSINESS_TYPE)
                .handleResult({
                    interviewInfo.value = it
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    /**
     * 重新邀请
     */
    fun reInvite() {
        interviewInfo.value?.let {
            reInviteCommand.value = VMEvent(it)
        }
    }
}