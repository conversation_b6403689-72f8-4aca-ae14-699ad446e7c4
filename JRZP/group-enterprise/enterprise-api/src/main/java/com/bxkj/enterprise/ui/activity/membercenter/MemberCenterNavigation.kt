package com.bxkj.enterprise.ui.activity.membercenter

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/24
 * @version: V1.0
 */
class MemberCenterNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/membercenter"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}