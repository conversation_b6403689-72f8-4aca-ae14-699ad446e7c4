package com.bxkj.enterprise.ui.activity.interviewmanagement

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

class InterviewManagementNavigation {

    companion object {
        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/interview_management"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}