package com.bxkj.enterprise.ui.fragment.ordermanagement

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2021/10/25
 */
class OrderManagementNavigation {
  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/ordermanagement"

    const val EXTRA_PAGE_TYPE = "page_type"

    const val TYPE_PAID: Int = 1
    const val TYPE_UNPAID: Int = 2

    fun navigate(pageType: Int): Fragment {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_PAGE_TYPE, pageType)
        .createFragment() as Fragment
    }
  }
}