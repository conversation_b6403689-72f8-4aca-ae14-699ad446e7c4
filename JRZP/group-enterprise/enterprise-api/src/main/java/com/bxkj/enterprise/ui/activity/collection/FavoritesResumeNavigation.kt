package com.bxkj.enterprise.ui.activity.collection

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class FavoritesResumeNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/favoritesresume"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}