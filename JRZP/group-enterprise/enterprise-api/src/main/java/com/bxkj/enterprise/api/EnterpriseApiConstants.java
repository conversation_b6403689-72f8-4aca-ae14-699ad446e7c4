package com.bxkj.enterprise.api;

import androidx.annotation.StringDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.api
 * @Description:
 * @TODO: TODO
 * @date 2018/7/17
 */
public class EnterpriseApiConstants {

  //多条件查询职位列表
  public static final String I_GET_POSITION_LIST_BY_MULTIPLE = "/ReleaseJob/GetJobListByPage2/";

  public static final int POSITION_ON_LINE = 3;   //上线职位
  public static final int POSITION_OFF_LINE = 4;  //下线职位

  //检查系统状态
  public static final String I_CHECK_SYSTEM_STATUS = "/Comm/GetIsShowUpdateCompany/";

  //更新职位状态
  public static final String I_UPDATE_POSITION_STATE = "/ReleaseJob/UpdateJobState/";

  //删除职位
  public static final String I_DELETE_POSITION = "/ReleaseJob/DelJob/";

  //获取职位置顶信息
  public static final String I_GET_POSITION_TOP_INFO = "/ReleaseJob/GetTopJobInfo/";

  //获取置顶天数、折扣以及消费积分信息列表
  public static final String I_GET_POSITION_TOP_COST = "/ReleaseJobTopZhekou/GetZhekouDaysListV2/";

  //职位置顶
  public static final String I_POSITION_TOP = "/ReleaseJobTop/AddJobTop/";

  //修改公司信息
  public static final String I_UPDATE_ENTERPRISE_INFO = "/Company/EditComInfoE/";

  //获取行业列表
  public static final String I_GET_INDUSTRY_LIST = "/Trade/GetTradeList/";

  //获取公司性质
  public static final String I_GET_NATURE_OF_COMPANY = "/CompanyPorperty/GetComPorpertyList/";

  //获取职位发布，收到简历，职位刷新等数量
  public static final String I_GET_ENTERPRISE_USE_COUNT = "/ReleaseJob/GetIndexTongji/";

  //获取首页简历推荐列表
  public static final String I_GET_RECOMMEND_RESUME_TYPE_LIST = "/ReleaseJob/GetAllJob/";

  //分页获取简历列表
  public static final String I_GET_RESUME_LIST = "/Resume/GetResumeByPageV2/";

  //获取用户账户内积分余额、刷新次数
  public static final String I_GET_ACCOUNT_VIP_INFO = "/UserVip/GetAccountVipInfoV2/";

  //获取刷新一个职位所需积分
  public static final String I_GET_INTEGRAL_FOR_REFRESH_POSITION =
    "/ReleaseJobRefresh/GetDouCountByRefreshJob/";

  //获取刷新消耗的积分或次数
  public static final String I_GET_INTEGRAL_FOR_REFRESH =
    "/ReleaseJobRefresh/GetAccountByRefreshV2/";

  //新增职位刷新
  public static final String I_REFRESH_POSITION = "/ReleaseJobRefresh/AddReleaseJobRefreshV2/";

  //获取职位智能刷新信息
  public static final String I_GET_POSITION_REFRESH_INFO = "/ReleaseJobRefresh/GetNowRefreshInfo/";

  //新增职位
  public static final String I_RELEASE_JOB = "/ReleaseJob/AddReleaseJobV4/";

  //修改职位
  public static final String I_UPDATE_JOB = "/ReleaseJob/EditReleaseJobV4/";

  //检查职位是否热门
  public static final String I_CHECK_JOB_NAME_IS_HOT = "/ReleaseJob/CheckReleaseJobName/";

  //获取职位详情
  public static final String I_GET_JOB_INFO = "/ReleaseJob/GetReleaseJobInfoV2/";

  //获取职位预览信息
  public static final String I_GET_POSITION_PREVIEW_INFO = "/ReleaseJob/GetJobInfo/";

  //获取职位模板列表
  public static final String I_GET_JOB_TEMPLATE_LIST = "/ReleaseJobTemp/GetReleaseJobTempList/";

  //获取福利列表
  public static final String I_GET_WELFARE_LIST = "/Welfare/GetWelfareList/";

  //批量添加福利
  public static final String I_ADD_WELFARE = "/JobWelfare/AddJobWelfare/";

  //获取简历接收邮箱列表
  public static final String I_GET_RESUME_RECEIVE_MAILBOX_LIST =
    "/CompanyEmail/GetCompanyEmailList/";

  //添加简历接收邮箱
  public static final String I_ADD_RESUME_RECEIVE_MAILBOX = "/ReleaseJobEmail/AddReleaseJobEmail/";

  //获取hr列表
  public static final String I_GET_HR_LIST = "/CompanyHr/GetCompanyHrList/";

  //添加hr
  public static final String I_ADD_HR = "/CompanyHr/AddCompanyHrjm/";

  //修改hr
  public static final String I_UPDATE_HR = "/CompanyHr/EditCompanyHrjm/";

  //删除hr
  public static final String I_DELETE_HR = "/CompanyHr/DelCompanyHr/";

  //获取用户中心信息
  public static final String I_GET_USER_CENTER_INFO = "/Company/GetAppCenterInfo/";

  //获取公司详情
  public static final String I_GET_COMPANY_INFO = "/Company/GetComInfo/";

  //修改公司信息
  public static final String I_UPDATE_COMPANY_INFO = "/Company/EditComInfojm/";

  //根据公司id获取公司名
  public static final String I_GET_COMPANY_NAME_BY_ID = "/Company/GetCompanyNameByUID/";

  //获取公司产品
  public static final String I_GET_PRODUCTS_LIST = "/CompanyProduct/GetCompanyProductList/";

  //添加公司产品
  public static final String I_ADD_COMPANY_PRODUCT = "/CompanyProduct/AddCompanyProduct/";

  //修改公司产品
  public static final String I_UPDATE_COMPANY_PRODUCT = "/CompanyProduct/EditCompanyProduct/";

  //删除公司产品
  public static final String I_DELETE_COMPANY_PRODUCT = "/CompanyProduct/DelCompanyProduct/";

  //获取公司风采
  public static final String I_GET_COMPANY_STYLE = "/CompanyPic/GetCompanyPicList/";

  //新增公司风采
  public static final String I_ADD_COMPANY_STYLE = "/CompanyPic/AddCompanyPic/";

  //修改公司风采
  public static final String I_UPDATE_COMPANY_STYLE = "/CompanyPic/EditCompanyPic/";

  //删除公司风采
  public static final String I_DELETE_COMPANY_STYLE = "/CompanyPic/DelCompanyPic/";

  //获取团队成员列表
  public static final String I_GET_TEAM_MEMBER_LIST = "/CompanyTeam/GetCompanyTeamList/";

  //新增团队成员
  public static final String I_ADD_TEAM_MEMBER = "/CompanyTeam/AddCompanyTeam/";

  //修改团队成员
  public static final String I_UPDATE_TEAM_MEMBER = "/CompanyTeam/EditCompanyTeam/";

  //删除团队成员
  public static final String I_DELETE_TEAM_MEMBER = "/CompanyTeam/DelCompanyTeam/";

  //获取收到的简历列表
  public static final String I_GET_RECEIVE_RESUME_LIST = "/JobApply/GetJobApplyByPage/";

  //获取收藏的简历列表
  public static final String I_GET_COLLECTION_RESUME_LIST =
    "/CompanyResume/GetCompanyResumeByPage/";

  //简历添加/取消收藏
  public static final String I_ADD_OR_DELETE_COLLECTION = "/CompanyResume/AddOrDelCompanyResume/";

  //修改公司联系资料
  public static final String I_UPDATE_CONTRACT_INFO = "/Company/EditComInfo2/";

  //提交营业执照认证信息
  public static final String I_SUBMIT_CERTIFICATION_INFO =
    "/BusinessLicense/AddOrEditBusinessLicense/";

  //获取营业执照认证信息
  public static final String I_GET_CERTIFICATE_INFO = "/BusinessLicense/GetBusinessLicenseInfo/";

  //检查营业执照认证信息
  public static final String I_CHECK_CERTIFICATION = "/BusinessLicense/GetBusinessLicenseStatus/";

  //获取简历求职意向
  public static final String I_GET_RESUME_CAREER_OBJECTIVE = "/VResume/GetResumeInfo/";

  //获取简历教育背景
  public static final String I_GET_RESUME_EDU_BACKGROUND = "/ResumeEdu/GetResumeEduList/";

  //获取简历工作经验
  public static final String I_GET_RESUME_WORK_EXP = "/ResumeJobExp/GetResumeJobExpList/";

  //获取专业技能
  public static final String I_GET_RESUME_SKILL = "/ResumeProSkill/GetResumeProSkillList/";

  //获取语言能力
  public static final String I_GET_RESUME_LANGUAGE_PROFICIENCY = "/ResumeLan/GetResumeLanList/";

  //获取简历在校情况
  public static final String I_GET_RESUME_SCHOOL_SITUATION = "/ResumeSchool/GetResumeSchoolList/";

  //获取简历证书情况
  public static final String I_GET_RESUME_CERTIFICATE = "/ResumeCerti/GetResumeCertiList/";

  //获取简历基本信息
  public static final String I_GET_RESUME_BASE_INFO = "/UserBasicInfo/GetUserCenterBasicInfo/";

  //修改简历状态
  public static final String I_UPDATE_RESUME_STATES = "/JobApply/UpdateJobApplyState/";

  //判断简历是否收藏
  public static final String I_CHECK_RESUME_COLLECTION = "/CompanyResume/ExistCompanyResume/";

  //简历收藏/取消收藏
  public static final String I_COLLECTION_RESUME = "/CompanyResume/AddOrDelCompanyResume/";

  //检查简历状态
  public static final String I_CHECK_RESUME_STATE = "/JobApply/GetJobApplyInfo/";

  //获取全部在线职位
  public static final String I_GET_ONLINE_POSITION = "/ReleaseJob/GetOnlineJob/";

  //获取邀请投递在线职位
  public static final String I_GET_DELIVERY_POSITION = "/ReleaseJob/GetOnlineJobByToudi/";

  //获取面试邀请职位信息
  public static final String I_GET_INTERVIEW_POSITION_INFO = "/ReleaseJob/GetJobInfoByInterview/";

  //发送面试邀请
  public static final String I_SEND_INVITATION_INTERVIEW = "/JobInterview/AddJobInterview/";

  //判斷簡歷是否已經邀請投遞
  public static final String I_CHECK_RESUME_IS_INVITATION_DELIVERY =
    "/ApplyToudi/ExistApplyToudi2/";

  //新增邀请投递
  public static final String I_SEND_INVITATION_DELIVERY = "/ApplyToudi/AddApplyToudiMoreV2/";

  //获取下载一个简历需要扣除的下载次数或积分
  public static final String I_GET_DOWNLOAD_RESUME_INTEGRAL =
    "/JobApply/GetDownloadResumeTimesOrIntegralV2/";

  //下载简历
  public static final String I_DOWNLOAD_RESUME = "/JobApply/AddDownloadResumeV2/";

  //提交意见反馈
  public static final String I_SUBMIT_FEEDBACK = "/Message/AddMessage/";

  //根据地理位置获取位置编号
  public static final String I_GET_ADDRESS_INFO_BY_ADDRESS_NAME = "/Area/GetAreaByName/";

  //获取关于我们信息
  public static final String I_GET_ABOUT_US_INFO = "/About/About/";

  //获取app版本信息
  public static final String I_GET_APP_VERSION_INFO = "/About/AndroidVersion/";

  //获取首页banner
  public static final String I_GET_HOME_BANNER_LIST = "/Comm/GetCompanyAdvList/";

  //获取企业专属客服信息
  public static final String I_GET_CUSTOM_SERVICE_INFO = "/Comm/GetKefuInfo2/";

  //分页获取企业订单信息
  public static final String I_GET_ORDER_LIST = "/UserOrder/GetUserOrderListByPageV2/";

  //申请发票
  public static final String I_APPLY_INVOICE = "/Invoice/AddInvoice/";

  //重新提交发票
  public static final String I_REAPPLY_INVOICE = "/Invoice/ReAddInvoice/";

  //获取发票信息
  public static final String I_GET_INVOICE_DETAILS = "/Invoice/GetInvoiceInfo/";

  //获取发票历史记录
  public static final String I_GET_INVOICE_RECORD_LIST = "/Invoice/GetHasInvoiceListByPage/";

  //创建支付订单
  public static final String I_CREATE_PAYMENT_ORDER = "/UserOrder/AddIntegralRecharge/";

  //获取支付宝订单信息
  public static final String I_GET_ALIPAY_ORDER_INFO = "/UserOrder/GetZfbPayString/";

  //请求一元可兑换积分数
  public static final String I_GET_INTEGRAL_CONVERSION_RATIO =
    "/UserOrder/GetIntegralCountByOneYuan/";

  //获取短信充值比例
  public static final String I_GET_SMS_CONVERSION_RATIO = "/UserOrder/GetSmsCountByOneYuan/";

  //生效订单
  public static final String I_ACTIVATE_ORDER = "/UserOrder/EffectOrder/";

  //删除订单
  public static final String I_DELETE_ORDER = "/UserOrder/DelMoreUserOrder/";

  //创建会员办理或续费订单
  public static final String I_CREATE_VIP_ORDER = "/UserOrder/AddVip/";

  //获取订单详情
  public static final String I_GET_PAYMENT_RESULT = "/UserOrder/GetUserOrderInfo/";

  //获取会员套餐列表
  public static final String I_GET_MEMBER_SERVICES_LIST = "/UserVip/GetVipList/";

  //检查公司信息是否完善
  public static final String I_CHECK_COMPANY_INFO_FILL = "/Company/GetCompanyBaseInfoISFinish/";

  //扫码登录
  public static final String I_SCAN_QR_CODE_LOGIN = "/User/UserLoginByScan/";

  //获取首页校园招聘列表
  public static final String I_GET_HOME_SCHOOL_RECRUITMENT_LIST =
    "/NewsArea/GetIndexJinqiXiaozhaoList/";

  //请求校园招聘列表
  public static final String I_GET_SCHOOL_RECRUIT_LIST = "/NewsArea/GetXiaozhaoListByPage/";

  //获取校园招聘详情
  public static final String I_GET_SCHOOL_RECRUIT_DETAILS = "/NewsArea/GetXiaozhaoInfo/";

  //获取高校信息
  public static final String I_GET_SCHOOL_DETAILS_INFO = "/Gaoxiao/GetGaoxiaoInfo/";

  //获取高校招聘会信息
  public static final String I_GET_SCHOOL_JOB_FAIR_LIST = "/NewsArea/GetGaoxiaozphListByPage/";

  //获取教职工分类list
  public static final String I_GET_FACULTY_TYPE_LIST =
    "/NewTypeGaoxiaozhaopin/GetNewTypeGaoxiaozhaopinList/";

  //获取教职工招聘列表
  public static final String I_GET_FACULTY_RECRUIT_LIST = "/News/GetJiaozhigongZhaopinListByPage/";

  //招聘会报名
  public static final String I_JOB_FAIR_REGISTRATION = "/NewsBaoming/AddNewsBaoming/";

  //判断校园招聘报名状态
  public static final String I_CHECK_SCHOOL_RECRUIT_REGISTER_STATUS =
    "/NewsBaoming/IsHasNewsBaoming/";

  //获取教职工招聘详情
  public static final String I_GET_FACULTY_RECRUIT_DETAILS = "/News/GetJiaozhigongZhaopinInfo/";

  //获取每日任务完成情况
  public static final String I_GET_DAILY_TASK_INFO = "/RenWu/GetEveryDayRenwuInfo/";

  //获取积分任务
  public static final String I_GET_INTEGRAL_TASK_LIST = "/Renwu/GetRenwuList/";

  //获取积分权益列表
  public static final String I_GET_INTEGRAL_RIGHTS_LIST = "/Comm/GetIntegralQuanyiList/";

  //获取积分充值数量列表
  public static final String I_GET_RECHARGE_INTEGRAL_LIST =
    "/UserOrder/GetIntegralRechargeItemList/";

  //获取邀请好友注册获取积分列表
  public static final String I_GET_INVITE_RECORD_LIST = "/IntegralRecord/GetListOfYaoqingReg/";

  //获取邀请好友注册还未完成列表
  public static final String I_GET_INVITE_UNDONE_LIST = "/IntegralRecord/GetHasNotIntegralList/";

  //获取分享目标url
  public static final String I_GET_SHARE_TARGET_URL = "/User/GetYaoqingRegUrl/";

  //检查是否已签到
  public static final String I_CHECK_IS_SIGN_IN = "/IntegralRecord/GetIsHasQiandao/";

  //签到
  public static final String I_SIGN_IN = "/IntegralRecord/Qiandao/";

  //检查个人简历信息是否完善
  public static final String I_CHECK_RESUME_INFO_PERFECTED =
    "/UserBasicInfo/IsHasFinishedPersionalInfo/";

  //获取创建简历个人资料
  public static final String I_GET_RESUME_PERSONAL_DATA =
    "/UserBasicInfo/GetPersonalinformationBasicInfo2/";

  //添加简历求职意向
  public static final String I_ADD_RESUME_CAREER_OBJECTIVE = "/Resume/CreateJobIntention/";

  //上传简历附件
  public static final String I_OPERATION_ATTACHMENT_RESUME =
    "http://img.jrzp.com/serviceInterface.ashx";

  //置顶位置PC
  public static final int POSITION_TOP_FOR_PC = 0;

  //获取用户关键字置顶生效时间
  public static final String I_GET_POSITION_TOP_KEYWORD_EFFECTIVE_DATE =
    "/ReleaseJobTopInfo/GetTopKeySdate/";

  //获取用户职位置顶剩余天数
  public static final String I_GET_POSITION_TOP_DAYS_BALANCE = "/UserVip/GetUserReleaseTop/";

  //获取职位已置顶区域
  public static final String I_GET_POSITION_TOP_CITYS = "/ReleaseJobTopInfo/GetJobTopCityListV2/";

  //新增职位置顶
  public static final String I_ADD_POSITION_TOP = "/ReleaseJobTop/AddJobTopV2/";

  //检查隐私条款是否有更新
  public static final String I_CHECK_AGREEMENT_VERSION = "/About/QyYinsiTiaokuanVersion/";

  //获取客服信息
  public static final String I_GET_SERVICES_INFO = "/User/GetCustomerService/";

  public static final int SHARE_TYPE_RESUME = 7;

  //获取分享信息
  public static final String I_GET_SHARE_CONTENT = "/Share/GetShareMessage/";

  //获取视频未读消息列表
  public static final String I_GET_VIDEO_UNREAD_MSG_LIST = "/Notify/GetSignUpByPage/";

  //设置消息已读
  public static final String I_SETUP_MSG_READ_V2 = "/Notify/GetNoticeRead/";

  //获取系统消息列表
  public static final String I_GET_SYSTEM_MSG_LIST = "/Notice/GetSystemXiaoxiListByPage/";

  //设置系统消息已读
  public static final String I_SET_SYSTEM_MSG_READ = "/Notice/UpdateSysteNoticemLook/";

  //获取消息数量
  public static final String I_GET_UNREAD_MSG_COUNT = "/Notify/GetNoticeCountByType/";

  //获取视频报名列表
  public static final String I_GET_SIGN_UP_USER_LIST = "/Video/GetSignUpListByPage/";

  //更新视频报名状态
  public static final String I_UPDATE_SIGN_UP_USER_STATUS = "/Video/UpdateSignUpState/";

  //检查是否有未读视频消息
  public static final String I_CHECK_HAS_UNREAD_VIDEO_MSG = "/Notify/HasUnLookedSignUp/";

  //检查是否于求职者存在会话
  public static final String I_CHECK_HAS_CONVERSATION = "/Conversation/MatchConInfo/";

  //获取打招呼余量
  public static final String I_GET_GREET_BALANCE = "/Conversation/GetTodaySyZhiliaoCount/";

  //企业版消息通知
  public static final String MSG_TYPE_E_NOTICE = "1,5,6,7,8,9,10,11";
  public static final String MSG_TYPE_E_RECRUITMENT = "5,6,7,8,9";
  public static final String MSG_TYPE_E_VIEW_ME = "4";
  public static final String MSG_TYPE_E_SYSTEM_MSG = "1";

  @StringDef({
    MSG_TYPE_E_NOTICE,
    MSG_TYPE_E_RECRUITMENT,
    MSG_TYPE_E_VIEW_ME,
    MSG_TYPE_E_SYSTEM_MSG
  })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface MsgType {
  }

  //获取消息列表
  public static final String I_GET_MESSAGE_LIST = "/Notice/GetNoticeListByPageV2/";

  //检查是否有发布的职位
  public static final String I_CHECK_HAS_PUBLISH_JOB = "/ReleaseJob/HasReleaseJob/";

  //绑定用户推送Token
  public static final String I_BIND_USER_PUSH_TOKEN = "/PushToken/AddPushToken/";

  //修改公司资料
  public static final String I_UPDATE_SINGLE_COMPANY_INFO = "/Company/AddOrEditInfo/";

  //修改公司简称和公司简介
  public static final String I_UPDATE_COMPANY_INFO_V2 =
    "/BusinessLicense/AddOrEditBusinessLicenseV3/";
}