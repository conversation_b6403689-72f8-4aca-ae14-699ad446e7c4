package com.bxkj.enterprise.ui.activity.conversation

import com.bxkj.common.enums.ChatRole.Companion.Role
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2021/11/1
 */
class BusinessChatContentNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/econversation"

    const val EXTRA_IDENTITY = "IDENTITY"
    const val EXTRA_CONVERSATION_USER_ID = "CHAT_USER_ID"

    //会话关联职位id
    const val EXTRA_ATTACH_JOB_ID = "ATTACH_JOB_ID"

    //会话关联简历id
    const val EXTRA_ATTACH_RESUME_ID = "ATTACH_RESUME_ID"

    //会话关联职位名称
    const val EXTRA_ATTACH_JOB_NAME = "ATTACH_JOB_OR_RESUME_NAME"

    //是否是打招呼
    const val EXTRA_IS_SAY_HELLO = "IS_SAY_HELLO"

    @JvmStatic
    @JvmOverloads
    fun create(
      @Role role: Int,
      conversationUserId: Int,
      attachJobId: Int = ECommonApiConstants.NO_ID,
      attachResumeId: Int = ECommonApiConstants.NO_ID,
      attachJobName: String = ECommonApiConstants.NO_TEXT,
      sayHello: Boolean = false,
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_IDENTITY,role)
        .withInt(EXTRA_CONVERSATION_USER_ID,conversationUserId)
        .withInt(EXTRA_ATTACH_JOB_ID,attachJobId)
        .withInt(EXTRA_ATTACH_RESUME_ID,attachResumeId)
        .withString(EXTRA_ATTACH_JOB_NAME,attachJobName)
        .withBoolean(EXTRA_IS_SAY_HELLO,sayHello)
    }
  }
}