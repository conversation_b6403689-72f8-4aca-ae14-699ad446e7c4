package com.bxkj.ecommon.widget.adresspickerdialog;

import android.content.Context;
import android.view.View;

import com.bxkj.common.R;
import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.AreaOptionsData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.AdressPickerDialog
 * @Description: 地址选择器适配器
 * @TODO: TODO
 * @date 2018/5/5
 */

public class AddressPickerAdapter extends SuperAdapter<AreaOptionsData> {

    public static final String TAG = "AddressPickerAdapter";

    private static final int NO_POSITION = -1;

    private int selectPosition = NO_POSITION;

    public AddressPickerAdapter(Context context, List<AreaOptionsData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, AreaOptionsData areaOptionsData, int position) {
        holder.setText(R.id.tv_item_name, areaOptionsData.getName());
        holder.findViewById(R.id.tv_item_name).setSelected(selectPosition == position);
        holder.findViewById(R.id.iv_selected_tag).setVisibility(selectPosition == position ? View.VISIBLE : View.GONE);

        holder.itemView.setOnClickListener(view -> {
            selectPosition = position;
            notifyItemChanged(position);
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(holder.itemView, position);
            }
        });
    }

    @Override
    public void reset(List<AreaOptionsData> items) {
        super.reset(items);
        selectPosition = NO_POSITION;
    }

    public void setSelected(String item) {
        for (int i = 0; i < mList.size(); i++) {
            if (mList.get(i).getName().equals(item)) {
                selectPosition = i;
                notifyItemChanged(i);
                if (SuperItemClickListener != null) {
                    SuperItemClickListener.onClick(null, selectPosition);
                }
                break;
            }
        }
    }
}
