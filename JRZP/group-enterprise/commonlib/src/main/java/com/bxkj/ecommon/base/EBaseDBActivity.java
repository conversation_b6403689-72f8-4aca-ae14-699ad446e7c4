package com.bxkj.ecommon.base;

import androidx.databinding.ViewDataBinding;
import com.bxkj.common.base.mvvm.BaseDBActivity;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.base
 * @Description:
 * @TODO: TODO
 * @date 2019/4/3
 */
public abstract class EBaseDBActivity<DB extends ViewDataBinding, VM extends BaseViewModel>
        extends BaseDBActivity<DB, VM> {

//    private CompositeDisposable mCompositeDisposable;

//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setupStatusBarColor();
//    }
//
//    private void setupStatusBarColor() {
//        ConstraintLayout clTitleBar = findViewById(R.id.title_bar);
//        if (clTitleBar != null) {
//            getStatusBarManager().titleBar(clTitleBar)
//                    .statusBarDarkFont(true, 0.4f)
//                    .navigationBarColor(R.color.common_white)
//                    .keyboardEnable(true)
//                    .init();
//        }
//    }


//    public void initViewModel(BaseViewModel mViewModel) {
//        mViewModel.getToast().observe(this, (Observer<Object>) msg -> {
//            if (msg instanceof String) {
//                showToast((String) msg);
//            } else if (msg instanceof Integer) {
//                showToast(getString((Integer) msg));
//            }
//        });
//
//        mViewModel.showLoadingCommand().observe(this, loadText -> showLoading(loadText));
//        mViewModel.hideLoadingCommand().observe(this, aVoid -> hiddenLoading());
//    }

//    protected void addDisposable(Disposable disposable) {
//        if (mCompositeDisposable == null) {
//            mCompositeDisposable = new CompositeDisposable();
//        }
//        mCompositeDisposable.add(disposable);
//    }

}
