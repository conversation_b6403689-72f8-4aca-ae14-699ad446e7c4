package com.bxkj.ecommon.widget.dialogfragment;

import androidx.fragment.app.FragmentManager;

import android.view.View;
import android.widget.TextView;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.ecommon.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description:
 * @TODO: TODO
 * @date 2018/5/7
 */

public class ETipsDialog extends BaseDialogFragment {

  public static final String TAG = "TipsDialog";

  private TextView tvTitle;
  private TextView tvContent;
  private TextView tvConfirm;

  private OnConfirmClickListener mOnConfirmClickListener;

  @Override
  protected int getRootViewId() {
    return R.layout.enterprise_dialog_tips;
  }

  @Override
  protected void initView() {
    tvTitle = getRootView().findViewById(R.id.tv_dialog_title);
    tvContent = getRootView().findViewById(R.id.tv_dialog_content);
    tvConfirm = getRootView().findViewById(R.id.tv_dialog_confirm);

    tvConfirm.setOnClickListener(view -> {
      if (mOnConfirmClickListener != null) {
        mOnConfirmClickListener.onConfirmClicked();
      }
      dismiss();
    });
  }

  private String title;
  private String content;
  private String confirmText;
  private boolean mCancelable = true;

  public ETipsDialog setTitle(String title) {
    this.title = title;
    return this;
  }

  public ETipsDialog setMCancelable(boolean cancelable) {
    this.mCancelable = cancelable;
    return this;
  }

  public ETipsDialog setContent(String content) {
    this.content = content;
    return this;
  }

  public ETipsDialog setConfirmText(String text) {
    this.confirmText = text;
    return this;
  }

  public ETipsDialog setOnConfirmClickListener(OnConfirmClickListener onConfirmClickListener) {
    mOnConfirmClickListener = onConfirmClickListener;
    return this;
  }

  @Override
  public void onStart() {
    super.onStart();
    if (!CheckUtils.isNullOrEmpty(title)) {
      tvTitle.setText(title);
    } else {
      tvTitle.setVisibility(View.GONE);
    }
    if (!CheckUtils.isNullOrEmpty(content)) {
      tvContent.setText(content);
    } else {
      tvContent.setVisibility(View.GONE);
    }
    if (!CheckUtils.isNullOrEmpty(confirmText)) {
      tvConfirm.setText(confirmText);
    }
    setCancelable(mCancelable);
  }

  @Override
  public void show(FragmentManager manager, String tag) {
    //        super.show(manager, tag);
    manager.beginTransaction().add(this, tag).commitAllowingStateLoss();
  }

  public interface OnConfirmClickListener {
    void onConfirmClicked();
  }
}
