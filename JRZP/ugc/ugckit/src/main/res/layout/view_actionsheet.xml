<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/actionsheet_top_normal"
        android:gravity="center"
        android:minHeight="45dp"
        android:paddingBottom="10dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="10dp"
        android:textColor="@color/actionsheet_gray"
        android:textSize="13sp"
        android:visibility="gone" />

    <ScrollView
        android:id="@+id/sLayout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fadingEdge="none">

        <LinearLayout
            android:id="@+id/lLayout_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"/>
    </ScrollView>

    <TextView
        android:id="@+id/txt_cancel"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/actionsheet_single_selector"
        android:gravity="center"
        android:text="@string/btn_cancel"
        android:textSize="18sp" />

</LinearLayout>