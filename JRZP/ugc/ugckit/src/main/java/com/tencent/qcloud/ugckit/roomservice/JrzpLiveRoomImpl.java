package com.tencent.qcloud.ugckit.roomservice;

import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;

import com.tencent.qcloud.ugckit.roomservice.http.HttpResponse;

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/23
 * @version: V1.0
 */
class JrzpLiveRoomImpl extends MLVBLiveRoomImpl {

  protected JrzpLiveRoomImpl(Context context) {
    super(context);
  }

  protected class RoomInfoPollThread {
    private Handler handler;

    public RoomInfoPollThread() {
    }

    private final Runnable roomInfoPollRunnable = new Runnable() {
      @Override
      public void run() {
        Handler localHander = handler;
        if (localHander == null) {
          return;
        }
        if (mSelfAccountInfo != null
            && mSelfAccountInfo.userID != null
            && mSelfAccountInfo.userID.length() > 0
            && mCurrRoomID != null
            && mCurrRoomID.length() > 0) {
          if (mHttpRequest != null) {
            //mHttpRequest.heartBeat(mSelfAccountInfo.userID, mCurrRoomID, mRoomStatusCode);
            mHttpRequest.getAudienceList(mCurrRoomID, (retcode, retmsg, data) -> {
              if (retcode == HttpResponse.CODE_OK && data != null && data.audienceCount != 0) {
                callbackOnThread(mListener, "onAudienceCountChange", data.audienceCount);
              }
            });
          }
          localHander.postDelayed(roomInfoPollRunnable, 3000);
        }
      }
    };

    public void startHeartbeat() {
      synchronized (this) {
        if (handler != null && handler.getLooper() != null) {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            handler.getLooper().quitSafely();
          } else {
            handler.getLooper().quit();
          }
        }
        HandlerThread thread = new HandlerThread("RoomInfoPollThread");
        thread.start();
        handler = new Handler(thread.getLooper());
        handler.postDelayed(roomInfoPollRunnable, 1000);
      }
    }

    public void stopHeartbeat() {
      synchronized (this) {
        if (handler != null) {
          handler.removeCallbacks(roomInfoPollRunnable);
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            handler.getLooper().quitSafely();
          } else {
            handler.getLooper().quit();
          }
          handler = null;
        }
      }
    }
  }
}
