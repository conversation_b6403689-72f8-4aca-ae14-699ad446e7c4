<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/beauty_ll_progress"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/beauty_iv_progress_img"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:background="@drawable/beauty_loading_circle_progress" />

    <TextView
        android:id="@+id/beauty_tv_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/beauty_iv_progress_img"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:text=""
        android:textColor="@color/beauty_color_white"
        android:visibility="visible" />

</LinearLayout>